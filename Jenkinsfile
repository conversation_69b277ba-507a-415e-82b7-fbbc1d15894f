pipeline {
  options {
    buildDiscarder(logRotator(numToKeepStr: '3', artifactNumToKeepStr: '3'))
  }
  environment {
    APP_ID="clientconfig"
    MAJOR_VER="3"
    MINOR_VER="2"
    RE_VERSION="0"
    REGISTRY_URL="harbor.xylink.com"
    REGISTRY_PRO="xylink"
   }

  parameters {
      choice(name: 'profile', choices: ['snapshot','release'], description: 'choice profile')
      choice(name: 'DEPLOY_ENV', choices: ['tx-dev','pre','prd','testqa','dc1'], description: 'choose deploy env')
  }

  agent {
    kubernetes {
      label 'clientconfig'
      defaultContainer 'jnlp'
      yamlFile 'pod_mainfest.yaml'
    }
  }

  stages {
    stage('build_app') {
      steps {
        script {
          env.BRANCH = sh(returnStdout: true, script: "git symbolic-ref --short -q HEAD")
          env.REVSION = sh(returnStdout: true, script: "git rev-list HEAD | wc -l").trim()
          env.HASH = sh(returnStdout: true, script: "git rev-parse --short=4 HEAD").trim()
          currentBuild.displayName = "${DEPLOY_ENV}-${profile}-${APP_ID}:${env.MAJOR_VER}.${env.MINOR_VER}.${env.RE_VERSION}_${env.REVSION}_${env.HASH}"
        }
        container('gradle-jdk') {
          sh 'mvn clean package -DskipTests=true -P prd'
        }
      }
    }

    stage('build_image') {
      environment {
        RESG = credentials('harbor')
        APP_VERSION = "${env.MAJOR_VER}.${env.MINOR_VER}.${env.RE_VERSION}_${env.REVSION}_${env.HASH}"
      }
      steps {
        container('docker') {
          echo "build image"
          sh """
            cp config-server/target/config-server.jar config-server/jenkins/config-server.jar
            cd config-server/jenkins
            docker login -u$RESG_USR -p$RESG_PSW https://$REGISTRY_URL
            docker build -t ${APP_ID} .
          """
          echo "push image"
          sh """
            docker tag ${APP_ID} $REGISTRY_URL/$REGISTRY_PRO/${APP_ID}:$APP_VERSION
            docker push $REGISTRY_URL/$REGISTRY_PRO/${APP_ID}:$APP_VERSION
          """
          echo "delete pushed image"
          sh """
            docker rmi -f ${APP_ID}
            docker rmi -f $REGISTRY_URL/$REGISTRY_PRO/${APP_ID}:$APP_VERSION
          """
         }
       }
     }

    stage('curl_post') {
              environment {
                APP_VERSION = "${env.MAJOR_VER}.${env.MINOR_VER}.${env.RE_VERSION}_${env.REVSION}_${env.HASH}"
              }
              steps {
                script {
                   env.GITHEAD = sh(returnStdout: true, script: "git rev-parse --short HEAD")
                   env.Author = sh(returnStdout: true, script: 'git log --pretty=format:"%cn" $GITHEAD -1')
                }
                container('gradle-jdk') {
                  echo "curl -x post"
                  sh """
                    echo '{
                       "env":"${params.DEPLOY_ENV}",
                       "profile":"${params.profile}",
                       "releaseVersion":"${env.MAJOR_VER}.${env.MINOR_VER}.${env.RE_VERSION}",
                       "versionInfo":"${env.REVSION}_${env.HASH}",
                       "serverName":"${APP_ID}",
                       "gitVersion":"${env.HASH}",
                       "gitBranch":"${env.BRANCH}",
                       "commitAuthor":"${env.Author}",
                       "imagePath":"$REGISTRY_URL/$REGISTRY_PRO/$APP_ID:$APP_VERSION"
                    }' > parems.json

                    curl -v -X POST "http://devops.xylink.com/api/rest/versionmanager/internal/v1/createVersionInfo" -H "Content-Type:application/json" -d @parems.json
                  """


                }
              }
          }

     }

}
