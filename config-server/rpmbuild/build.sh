#!/usr/bin/env bash

set -x
#+100 for lost some commit
RPM_DIR=`pwd`
export GIT_REVISION=`expr $(git rev-list --all | wc -l) + 500`
DIST_DIR=${RPM_DIR}/build
rm -rf ${DIST_DIR}
cd ../..
mvn clean install package -P ${profile} -e -U
cd config-server/rpmbuild
mkdir libs
pwd && ls
ls ../
mv ../target/config-server*.jar libs/clientconfig
tar -cvf clientconfig.tar libs

pwd && ls

cd ${RPM_DIR}
RPMSPEC=${RPM_DIR}/SPECS
RPMSOURCE=${RPM_DIR}/SOURCES
RPMDST=${RPM_DIR}/RPMS
mkdir -p ${RPMSPEC}
mkdir -p ${RPMSOURCE}
mkdir -p ${RPMDST}

cp -rf clientconfig.spec ${RPMSPEC}

source ${RPM_DIR}/info.dat
APP_VERSION=$major.$minor.$revision

VER_LINE=`grep -n "Version" clientconfig.spec | cut -d: -f1 | sed  's/^[[:space:]]*//'`
sed -i -e''${VER_LINE}'c\Version: '${APP_VERSION}'' ${RPMSPEC}/clientconfig.spec

REL_LINE=`grep -n "Release" clientconfig.spec | cut -d: -f1 | sed  's/^[[:space:]]*//'`
sed -i -e''${REL_LINE}'c\Release: '${GIT_REVISION}'' ${RPMSPEC}/clientconfig.spec

sed -i "/Packager/c\Packager: `git rev-list --all | head -1`" ${RPMSPEC}/clientconfig.spec

pwd && ls

cp -rf clientconfig.tar ${RPMSOURCE}
rm -rf clientconfig
echo "------------------------------------------------------------------------------------------------1---$_topdir"
rpmbuild --define "_topdir ${RPM_DIR}" -vv -bb ${RPMSPEC}/clientconfig.spec
HWPF=`uname -i | sed  's/^[[:space:]]*//'`

mv ${RPMDST}/${HWPF}/*.rpm .