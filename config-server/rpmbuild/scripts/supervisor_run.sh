#!/bin/bash
LIBRA_HOME=/usr/libra
SERVER_HOME=$LIBRA_HOME/clientconfig

function shutdown()
{
echo "shutting down clientconfig" >>/tmp/clientconfig_trace.log
LOCATOR_PID=`ps -ef |grep 'process.name=ainemo-clientconfig' | grep -v grep |awk '{print $2}'`
kill -9 $LOCATOR_PID
}

ulimit -n 65535

pid=`ps -ef|grep -v grep|grep 'process.name=ainemo-clientconfig'| awk '{print $2}'`
if [ ! -z "$pid" ]; then
echo "clientconfig is running, will not start again".
exit 0
fi

SCRIPT="`readlink -e $0`"
CURRENT_PATH="`dirname $SCRIPT`"
SIGSERVER_HOME=$CURRENT_PATH/..

exec java -jar uaa-admin-server.jar -Xmx5120m -Xms5120m -Xmn1536m -XX:PermSize=64m -XX:MaxPermSize=256m -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/usr/libra/clientconfig/dump -Dsun.net.http.allowRestrictedHeaders=true -XX:+PrintTenuringDistribution -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+PrintGCDateStamps -Xloggc:/usr/libra/clientconfig/logs/clientconfig-gc.log -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=5 -XX:GCLogFileSize=100M 1>/dev/null 2>&1

trap shutdown HUP INT QUIT KILL TERM USR1 USR2

LOCATOR_PID=`ps -ef |grep 'process.name=ainemo-clientconfig' | grep -v grep |awk '{print $2}'`

echo "wait..."
wait $LOCATOR_PID

echo "done"