<project name="clientconfig" default="all" basedir=".">
	<property environment="SystemVariable" />
	<property name="git.revision" value="${SystemVariable.GIT_REVISION}" />
	<property name="libra.version" value="${git.revision}" />
	<property name="dist.dir" value="${basedir}/dist/${libra.version}" />
	<target name="all" depends="clientconfig" />
    <target name="clientconfig">
        <property name="to.dir" value="${dist.dir}/clientconfig"/>
        <delete dir="${to.dir}" />
        <mkdir dir="${to.dir}" />
        <property name="clientconfig.scripts.dir" value="${basedir}/scripts" />
        <exec executable="bash" dir="${basedir}/../../" failonerror="true" >
            <arg line="build.sh" />
        </exec>
        <copy todir="${to.dir}/tomcat/webapps">
            <fileset dir="${basedir}/../../build/libs" includes="**/*ROOT.war"/>
        </copy>
        <copy todir="${to.dir}/scripts">
            <fileset dir="${clientconfig.scripts.dir}">
            </fileset>
        </copy>
        <exec executable="bash" dir="${to.dir}/scripts">
            <arg value="-c"/>
            <arg value="chmod +x *" />
        </exec>
        <tar destfile="${to.dir}/clientconfig.tar" basedir="${to.dir}" />
        <delete includeemptydirs="true">
            <fileset dir="${to.dir}" excludes="**/clientconfig.tar" />
        </delete>
        <move todir="${to.dir}/clientconfig" file="${to.dir}/clientconfig.tar" />
        <echo file="${to.dir}/clientconfig/version.properties" >clientconfig.revision=${git.revision}${line.separator}</echo>
        <tar destfile="${dist.dir}/clientconfig_${libra.version}.tgz" compression="gzip">
            <tarfileset dir="${to.dir}" includes="**/clientconfig/"/>
        </tar>
        <delete dir="${to.dir}" />
    </target>
</project>
