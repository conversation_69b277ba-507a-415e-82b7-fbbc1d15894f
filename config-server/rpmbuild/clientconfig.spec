Summary:   clientconfig package
Name:      ainemo-clientconfig
Version:   1.0.0
Release:   1
License:   xylilnk.com
Group:     System
Source:    clientconfig.tar
Url:       https://www.xylink.com
Packager:  Build
Prefix:    %{_prefix}
Prefix:    %{_sysconfdir}
%define    installpath /usr/libra/clientconfig/
%define __prelink_undo_cmd      /bin/cat prelink library

%description
clientconfig

%prep
%setup -c
%install
install -d -m 755 $RPM_BUILD_ROOT%{installpath}
cp -rf ${RPM_BUILD_ROOT}/../../BUILD/%{name}-%{version}/* $RPM_BUILD_ROOT%{installpath}

%clean
rm -rf $RPM_BUILD_ROOT
rm -rf $RPM_BUILD_DIR/%{name}-%{version}

%files
%defattr(-,root,root,-)
%{installpath}