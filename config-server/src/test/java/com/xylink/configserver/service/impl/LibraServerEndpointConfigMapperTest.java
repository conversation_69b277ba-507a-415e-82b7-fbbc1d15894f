package com.xylink.configserver.service.impl;

import com.xylink.configserver.mapper.LibraServerEndpointConfigMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/1/9 12:03 下午
 */
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class LibraServerEndpointConfigMapperTest {

    @Autowired
    LibraServerEndpointConfigMapper libraServerEndpointConfigMapper;


    @Test
    public void verifyTest() {
        List<Long> device = new ArrayList<>();
        List<Long> user = new ArrayList<>();
        user.add(247567L);
        user.add(247568L);
        device.add(1L);
        device.add(95L);
        libraServerEndpointConfigMapper.findServerEndpointConfigBatch(user, device, "translateLanguage");
    }

}
