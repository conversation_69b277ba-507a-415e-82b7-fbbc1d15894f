package com.xylink.configserver.service.deviceseries.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xylink.configserver.data.model.deviceseries.DeviceSeriesDto;
import com.xylink.configserver.data.model.deviceseries.DeviceSeriesEntity;
import com.xylink.configserver.data.model.deviceseries.DeviceSeriesSubtypeConfigsDto;
import com.xylink.configserver.service.deviceseries.DeviceSeriesService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-12 15:43
 */
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class DeviceSeriesServiceImplTest {
    @Resource
    DeviceSeriesService deviceSeriesService;

    @Test
    public void getAll() {
        System.out.println(deviceSeriesService.list());
    }

    @Test
    public void save() {
        DeviceSeriesEntity deviceSeriesEntity = new DeviceSeriesEntity();
        deviceSeriesEntity.setSeriesName("ME90II");
        deviceSeriesEntity.setId(1L);
        deviceSeriesService.saveOrUpdate(deviceSeriesEntity);
    }

    @Test
    public void page() {
        Page<DeviceSeriesEntity> page = new Page<>();
        page.setSize(1);
        deviceSeriesService.page(page);
        System.out.println(page.getRecords());
        System.out.println(page.getTotal());
    }

    @Test
    public void getDeviceSeriesDto() {
        System.out.println(deviceSeriesService.getDeviceSeriesDto(5));
    }

    @Test
    public void saveOrUpdateSeries() {
        DeviceSeriesDto dto = new DeviceSeriesDto();
        dto.setSeriesName("NE");
        List<DeviceSeriesSubtypeConfigsDto> configs = new ArrayList<>();
        DeviceSeriesSubtypeConfigsDto dto1 = new DeviceSeriesSubtypeConfigsDto();
        dto1.setConfigId(1L);
        configs.add(dto1);
        DeviceSeriesSubtypeConfigsDto dto2 = new DeviceSeriesSubtypeConfigsDto();
        dto2.setConfigId(2L);
        configs.add(dto2);
        dto.setConfigs(configs);
        deviceSeriesService.saveOrUpdateSeries(dto);
    }


}
