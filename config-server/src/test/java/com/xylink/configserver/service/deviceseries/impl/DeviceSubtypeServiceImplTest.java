package com.xylink.configserver.service.deviceseries.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xylink.configserver.data.model.deviceseries.DeviceSubtypeListDto;
import com.xylink.configserver.service.deviceseries.DeviceSubtypeService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2021-01-18 17:28
 */
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class DeviceSubtypeServiceImplTest {
    @Resource
    private DeviceSubtypeService deviceSubtypeService;

    @Test
    public void page() {
        Page<DeviceSubtypeListDto> page = new Page<>();
        page.setSize(10);
        page.setCurrent(1);
        String queryKey = "NE";
        System.out.println(deviceSubtypeService.page(page, queryKey).getRecords());
    }

    @Test
    public void detail(){
        System.out.println(deviceSubtypeService.detail(21));
    }
}
