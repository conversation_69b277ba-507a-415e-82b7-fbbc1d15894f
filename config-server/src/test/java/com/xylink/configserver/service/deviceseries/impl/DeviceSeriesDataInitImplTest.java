package com.xylink.configserver.service.deviceseries.impl;

import com.xylink.configserver.data.model.deviceseries.*;
import com.xylink.configserver.mapper.deviceseries.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021-02-19 09:42
 */
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class DeviceSeriesDataInitImplTest {
    @Resource
    private DeviceConfigDictTypeMapper deviceConfigDictTypeMapper;
    @Resource
    private DeviceConfigDictDataMapper deviceConfigDictDataMapper;
    @Resource
    private DeviceSubtypeSeriesConfigDictionaryMapper deviceSubtypeSeriesConfigDictionaryMapper;
    @Resource
    private DeviceSubtypeSeriesConfigDictionaryDataMapper deviceSubtypeSeriesConfigDictionaryDataMapper;
    @Resource
    private DeviceSeriesMapper deviceSeriesMapper;
    @Resource
    private DeviceSeriesSubtypeMapper deviceSeriesSubtypeMapper;
    @Resource
    private DeviceSubtypeSeriesConfigMapper deviceSubtypeSeriesConfigMapper;

    @Test
    public void init() {
        /**
         * 先清空数据库
         *         delete from libra_device_series;
         *         delete from libra_device_series_subtype;
         *         delete from libra_device_subtype_series_config;
         *         delete from libra_device_subtype_series_config_dictionary;
         *         delete from libra_device_subtype_series_config_dictionary_data;
         *         delete from libra_config_dict_type;
         *         delete from libra_config_dict_data;
         *
         *
         *         alter table libra_device_series AUTO_INCREMENT=1;
         *         alter table libra_device_series_subtype AUTO_INCREMENT=1;
         *         alter table libra_device_subtype_series_config AUTO_INCREMENT=1;
         *         alter table libra_device_subtype_series_config_dictionary AUTO_INCREMENT=1;
         *         alter table libra_device_subtype_series_config_dictionary_data AUTO_INCREMENT=1;
         *         alter table libra_config_dict_type AUTO_INCREMENT=1;
         *         alter table libra_config_dict_data AUTO_INCREMENT=1;
         */

        initConfigDictData();
        initConfigDictionary();
        initSeries();
        initSubtype();
    }

    /**
     * 初始化字典表
     */
    @Test
    public void initConfigDictData() {
        DeviceConfigDictTypeEntity entity1 = new DeviceConfigDictTypeEntity();
        entity1.setDictName("允许被呼叫");
        entity1.setDictType("AllowNemoNumberCall");
        entity1.setRemark("设置设备响应呼叫的范围");
        deviceConfigDictTypeMapper.insert(entity1);

        DeviceConfigDictDataEntity entity1Data1 = new DeviceConfigDictDataEntity();
        entity1Data1.setDictCode("关闭");
        entity1Data1.setDictValue("0");
        entity1Data1.setDictType(entity1.getDictType());
        entity1Data1.setDictSort(1);
        deviceConfigDictDataMapper.insert(entity1Data1);

        DeviceConfigDictDataEntity entity1Data2 = new DeviceConfigDictDataEntity();
        entity1Data2.setDictCode("所有用户");
        entity1Data2.setDictValue("1");
        entity1Data2.setDictType(entity1.getDictType());
        entity1Data2.setDictSort(2);
        deviceConfigDictDataMapper.insert(entity1Data2);


        DeviceConfigDictDataEntity entity1Data3 = new DeviceConfigDictDataEntity();
        entity1Data3.setDictCode("通讯录用户");
        entity1Data3.setDictValue("2");
        entity1Data3.setDictType(entity1.getDictType());
        entity1Data3.setDictSort(3);
        deviceConfigDictDataMapper.insert(entity1Data3);

        DeviceConfigDictDataEntity entity1Data4 = new DeviceConfigDictDataEntity();
        entity1Data4.setDictCode("所有非匿名用户");
        entity1Data4.setDictValue("3");
        entity1Data4.setDictType(entity1.getDictType());
        entity1Data4.setDictSort(4);
        deviceConfigDictDataMapper.insert(entity1Data4);


        DeviceConfigDictTypeEntity entity2 = new DeviceConfigDictTypeEntity();
        entity2.setDictName("开启/关闭");
        entity2.setDictType("OpenClose");
        entity2.setRemark("开关项");
        deviceConfigDictTypeMapper.insert(entity2);

        DeviceConfigDictDataEntity entity2Data1 = new DeviceConfigDictDataEntity();
        entity2Data1.setDictCode("关闭");
        entity2Data1.setDictValue("false");
        entity2Data1.setDictType(entity2.getDictType());
        entity2Data1.setDictSort(1);
        deviceConfigDictDataMapper.insert(entity2Data1);

        DeviceConfigDictDataEntity entity2Data2 = new DeviceConfigDictDataEntity();
        entity2Data2.setDictCode("开启");
        entity2Data2.setDictValue("true");
        entity2Data2.setDictType(entity2.getDictType());
        entity2Data2.setDictSort(2);
        deviceConfigDictDataMapper.insert(entity2Data2);


        DeviceConfigDictTypeEntity entity3 = new DeviceConfigDictTypeEntity();
        entity3.setDictName("允许主动呼叫");
        entity3.setDictType("AllowOutgoingCall");
        entity3.setRemark("设置设备呼叫的范围");
        deviceConfigDictTypeMapper.insert(entity3);

        DeviceConfigDictDataEntity entity3Data1 = new DeviceConfigDictDataEntity();
        entity3Data1.setDictCode("关闭");
        entity3Data1.setDictValue("0");
        entity3Data1.setDictType(entity3.getDictType());
        entity3Data1.setDictSort(1);
        deviceConfigDictDataMapper.insert(entity3Data1);

        DeviceConfigDictDataEntity entity3Data2 = new DeviceConfigDictDataEntity();
        entity3Data2.setDictCode("所有用户");
        entity3Data2.setDictValue("1");
        entity3Data2.setDictType(entity3.getDictType());
        entity3Data2.setDictSort(2);
        deviceConfigDictDataMapper.insert(entity3Data2);


        DeviceConfigDictDataEntity entity3Data3 = new DeviceConfigDictDataEntity();
        entity3Data3.setDictCode("通讯录用户");
        entity3Data3.setDictValue("2");
        entity3Data3.setDictType(entity3.getDictType());
        entity3Data3.setDictSort(3);
        deviceConfigDictDataMapper.insert(entity3Data3);


        DeviceConfigDictTypeEntity entity4 = new DeviceConfigDictTypeEntity();
        entity4.setDictName("自动应答");
        entity4.setDictType("AutoAnswer");
        entity4.setRemark("设置设备被呼叫时，自动接听的范围");
        deviceConfigDictTypeMapper.insert(entity4);

        DeviceConfigDictDataEntity entity4Data1 = new DeviceConfigDictDataEntity();
        entity4Data1.setDictCode("关闭");
        entity4Data1.setDictValue("0");
        entity4Data1.setDictType(entity4.getDictType());
        entity4Data1.setDictSort(1);
        deviceConfigDictDataMapper.insert(entity4Data1);

        DeviceConfigDictDataEntity entity4Data2 = new DeviceConfigDictDataEntity();
        entity4Data2.setDictCode("所有用户");
        entity4Data2.setDictValue("1");
        entity4Data2.setDictType(entity4.getDictType());
        entity4Data2.setDictSort(2);
        deviceConfigDictDataMapper.insert(entity4Data2);


        DeviceConfigDictDataEntity entity4Data3 = new DeviceConfigDictDataEntity();
        entity4Data3.setDictCode("通讯录用户");
        entity4Data3.setDictValue("2");
        entity4Data3.setDictType(entity4.getDictType());
        entity4Data3.setDictSort(3);
        deviceConfigDictDataMapper.insert(entity4Data3);


        DeviceConfigDictTypeEntity entity5 = new DeviceConfigDictTypeEntity();
        entity5.setDictName("AGC增益调节");
        entity5.setDictType("AutomaticManual");
        entity5.setRemark("AGC增益调节");
        deviceConfigDictTypeMapper.insert(entity5);

        DeviceConfigDictDataEntity entity5Data1 = new DeviceConfigDictDataEntity();
        entity5Data1.setDictCode("自动");
        entity5Data1.setDictValue("true");
        entity5Data1.setDictType(entity5.getDictType());
        entity5Data1.setDictSort(1);
        deviceConfigDictDataMapper.insert(entity5Data1);

        DeviceConfigDictDataEntity entity5Data2 = new DeviceConfigDictDataEntity();
        entity5Data2.setDictCode("手动");
        entity5Data2.setDictValue("false");
        entity5Data2.setDictType(entity5.getDictType());
        entity5Data2.setDictSort(2);
        deviceConfigDictDataMapper.insert(entity5Data2);


        DeviceConfigDictTypeEntity entity6 = new DeviceConfigDictTypeEntity();
        entity6.setDictName("隐藏/显示");
        entity6.setDictType("HideShow");
        entity6.setRemark("开关项");
        deviceConfigDictTypeMapper.insert(entity6);

        DeviceConfigDictDataEntity entity6Data1 = new DeviceConfigDictDataEntity();
        entity6Data1.setDictCode("显示");
        entity6Data1.setDictValue("true");
        entity6Data1.setDictType(entity6.getDictType());
        entity6Data1.setDictSort(1);
        deviceConfigDictDataMapper.insert(entity6Data1);

        DeviceConfigDictDataEntity entity6Data2 = new DeviceConfigDictDataEntity();
        entity6Data2.setDictCode("隐藏");
        entity6Data2.setDictValue("false");
        entity6Data2.setDictType(entity6.getDictType());
        entity6Data2.setDictSort(2);
        deviceConfigDictDataMapper.insert(entity6Data2);


        DeviceConfigDictTypeEntity entity7 = new DeviceConfigDictTypeEntity();
        entity7.setDictName("标签样式");
        entity7.setDictType("LabelStyle");
        entity7.setRemark("终端颜色配色组");
        deviceConfigDictTypeMapper.insert(entity7);

        DeviceConfigDictDataEntity entity7Data1 = new DeviceConfigDictDataEntity();
        entity7Data1.setDictCode("Black");
        entity7Data1.setDictValue("{\\\"TextForegroundRGB\\\":\\\"#FFFFFF\\\",\\\"TextBackgroundRGB\\\":\\\"#000000\\\",\\\"TextBackgroundAlpha\\\":0.8}");
        entity7Data1.setDictType(entity7.getDictType());
        entity7Data1.setDictSort(1);
        deviceConfigDictDataMapper.insert(entity7Data1);

        DeviceConfigDictDataEntity entity7Data2 = new DeviceConfigDictDataEntity();
        entity7Data2.setDictCode("Gray");
        entity7Data2.setDictValue("{\\\"TextForegroundRGB\\\":\\\"#FFFFFF\\\",\\\"TextBackgroundRGB\\\":\\\"#6C7783\\\",\\\"TextBackgroundAlpha\\\":0.8}");
        entity7Data2.setDictType(entity7.getDictType());
        entity7Data2.setDictSort(2);
        deviceConfigDictDataMapper.insert(entity7Data2);


        DeviceConfigDictDataEntity entity7Data3 = new DeviceConfigDictDataEntity();
        entity7Data3.setDictCode("Red");
        entity7Data3.setDictValue("{\\\"TextForegroundRGB\\\":\\\"#FFFFFF\\\",\\\"TextBackgroundRGB\\\":\\\"#993131\\\",\\\"TextBackgroundAlpha\\\":0.8}");
        entity7Data3.setDictType(entity7.getDictType());
        entity7Data3.setDictSort(3);
        deviceConfigDictDataMapper.insert(entity7Data3);


        DeviceConfigDictDataEntity entity7Data4 = new DeviceConfigDictDataEntity();
        entity7Data4.setDictCode("Pink");
        entity7Data4.setDictValue("{\\\"TextForegroundRGB\\\":\\\"#192229\\\",\\\"TextBackgroundRGB\\\":\\\"#FFC6DA\\\",\\\"TextBackgroundAlpha\\\":0.8}");
        entity7Data4.setDictType(entity7.getDictType());
        entity7Data4.setDictSort(4);
        deviceConfigDictDataMapper.insert(entity7Data4);

        DeviceConfigDictDataEntity entity7Data5 = new DeviceConfigDictDataEntity();
        entity7Data5.setDictCode("Yellow");
        entity7Data5.setDictValue("{\\\"TextForegroundRGB\\\":\\\"#FFFFFF\\\",\\\"TextBackgroundRGB\\\":\\\"#FFB72E\\\",\\\"TextBackgroundAlpha\\\":0.8}");
        entity7Data5.setDictType(entity7.getDictType());
        entity7Data5.setDictSort(5);
        deviceConfigDictDataMapper.insert(entity7Data5);

        DeviceConfigDictDataEntity entity7Data6 = new DeviceConfigDictDataEntity();
        entity7Data6.setDictCode("Blue");
        entity7Data6.setDictValue("{\\\"TextForegroundRGB\\\":\\\"#FFFFFF\\\",\\\"TextBackgroundRGB\\\":\\\"#0E97F4\\\",\\\"TextBackgroundAlpha\\\":0.8}");
        entity7Data6.setDictType(entity7.getDictType());
        entity7Data6.setDictSort(6);
        deviceConfigDictDataMapper.insert(entity7Data6);


    }

    /**
     * 初始化配置项字典
     */
    @Test
    public void initConfigDictionary() {
        // SUBTYPE配置
        DeviceSubtypeSeriesConfigDictionaryEntity entitySub = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entitySub.setConfigCode("terminalParameterSettings");
        entitySub.setConfigName("终端参数设置");
        entitySub.setConfigComment("终端参数设置");
        entitySub.setConfigOrder(1);
        entitySub.setPId(0L);
        entitySub.setDictionaryType(DeviceGroupEnum.SUBTYPE.name());
        entitySub.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entitySub);

        //管理员
        DeviceSubtypeSeriesConfigDictionaryEntity entitySub01 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entitySub01.setConfigCode("administrator");
        entitySub01.setConfigName("管理员");
        entitySub01.setConfigComment("设置管理员");
        entitySub01.setConfigOrder(1);
        entitySub01.setPId(entitySub.getId());
        entitySub01.setDictionaryType(DeviceGroupEnum.SUBTYPE.name());
        entitySub01.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entitySub01);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entitySub01Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entitySub01Data01.setConfigId(entitySub01.getId());
        entitySub01Data01.setClientConfigName("common");
        entitySub01Data01.setConfigName("administrator");
        entitySub01Data01.setConfigShowName("管理员");
        entitySub01Data01.setConfigComment("设置管理员");
        entitySub01Data01.setConfigValuesType(ConfigValuesTypeEnum.SELECTED.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entitySub01Data01);

        // 允许被呼叫
        DeviceSubtypeSeriesConfigDictionaryEntity entitySub02 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entitySub02.setConfigCode("allowCalled");
        entitySub02.setConfigName("允许被呼叫");
        entitySub02.setConfigComment("设置设备响应呼叫的范围");
        entitySub02.setConfigOrder(2);
        entitySub02.setPId(entitySub.getId());
        entitySub02.setDictionaryType(DeviceGroupEnum.SUBTYPE.name());
        entitySub02.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entitySub02);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entitySub02Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entitySub02Data01.setConfigId(entitySub02.getId());
        entitySub02Data01.setClientConfigName("common");
        entitySub02Data01.setConfigName("allowNemoNumberCall");
        entitySub02Data01.setConfigShowName("允许被呼叫");
        entitySub02Data01.setConfigComment("设置设备响应呼叫的范围");
        entitySub02Data01.setConfigDictType("AllowNemoNumberCall");
        entitySub02Data01.setConfigValuesType(ConfigValuesTypeEnum.SELECTED.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entitySub02Data01);

        //允许主动呼叫
        DeviceSubtypeSeriesConfigDictionaryEntity entitySub03 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entitySub03.setConfigCode("allowActiveCalls");
        entitySub03.setConfigName("允许主动呼叫");
        entitySub03.setConfigComment("设置设备呼叫的范围");
        entitySub03.setConfigOrder(3);
        entitySub03.setPId(entitySub.getId());
        entitySub03.setDictionaryType(DeviceGroupEnum.SUBTYPE.name());
        entitySub03.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entitySub03);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entitySub03Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entitySub03Data01.setConfigId(entitySub03.getId());
        entitySub03Data01.setClientConfigName("common");
        entitySub03Data01.setConfigName("allowOutgoingCall");
        entitySub03Data01.setConfigShowName("允许主动呼叫");
        entitySub03Data01.setConfigComment("设置设备呼叫的范围");
        entitySub03Data01.setConfigDictType("AllowOutgoingCall");
        entitySub03Data01.setConfigValuesType(ConfigValuesTypeEnum.SELECTED.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entitySub03Data01);

        //自动应答
        DeviceSubtypeSeriesConfigDictionaryEntity entitySub04 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entitySub04.setConfigCode("autoAnswer");
        entitySub04.setConfigName("自动应答");
        entitySub04.setConfigComment("设置设备被呼叫时，自动接听的范围");
        entitySub04.setConfigOrder(4);
        entitySub04.setPId(entitySub.getId());
        entitySub04.setDictionaryType(DeviceGroupEnum.SUBTYPE.name());
        entitySub04.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entitySub04);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entitySub04Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entitySub04Data01.setConfigId(entitySub04.getId());
        entitySub04Data01.setClientConfigName("common");
        entitySub04Data01.setConfigName("autoAnswer");
        entitySub04Data01.setConfigShowName("自动应答");
        entitySub04Data01.setConfigComment("设置设备被呼叫时，自动接听的范围");
        entitySub04Data01.setConfigDictType("AutoAnswer");
        entitySub04Data01.setConfigValuesType(ConfigValuesTypeEnum.SELECTED.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entitySub04Data01);

        //通话中免打扰
        DeviceSubtypeSeriesConfigDictionaryEntity entitySub05 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entitySub05.setConfigCode("doNotDisturbDuringCall");
        entitySub05.setConfigName("通话中免打扰");
        entitySub05.setConfigComment("终端在通话中不接受其他呼叫");
        entitySub05.setConfigOrder(5);
        entitySub05.setPId(entitySub.getId());
        entitySub05.setDictionaryType(DeviceGroupEnum.SUBTYPE.name());
        entitySub05.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entitySub05);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entitySub05Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entitySub05Data01.setConfigId(entitySub05.getId());
        entitySub05Data01.setClientConfigName("common");
        entitySub05Data01.setConfigName("noDisturbIncall");
        entitySub05Data01.setConfigShowName("通话中免打扰");
        entitySub05Data01.setConfigComment("终端在通话中不接受其他呼叫");
        entitySub05Data01.setConfigDictType("OpenClose");
        entitySub05Data01.setConfigValuesType(ConfigValuesTypeEnum.SWITCH.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entitySub05Data01);

        //允许远程遥控本地摄像头
        DeviceSubtypeSeriesConfigDictionaryEntity entitySub06 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entitySub06.setConfigCode("allowRemoteControlOfLocalCamera");
        entitySub06.setConfigName("允许远程遥控本地摄像头");
        entitySub06.setConfigComment("终端在通话中本地摄像头可被其他参会人遥控");
        entitySub06.setConfigOrder(6);
        entitySub06.setPId(entitySub.getId());
        entitySub06.setDictionaryType(DeviceGroupEnum.SUBTYPE.name());
        entitySub06.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entitySub06);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entitySub06Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entitySub06Data01.setConfigId(entitySub06.getId());
        entitySub06Data01.setClientConfigName("common");
        entitySub06Data01.setConfigName("enableFecc");
        entitySub06Data01.setConfigShowName("允许远程遥控本地摄像头");
        entitySub06Data01.setConfigComment("终端在通话中本地摄像头可被其他参会人遥控");
        entitySub06Data01.setConfigDictType("OpenClose");
        entitySub06Data01.setConfigValuesType(ConfigValuesTypeEnum.SWITCH.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entitySub06Data01);

        //显示录制提示
        DeviceSubtypeSeriesConfigDictionaryEntity entitySub07 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entitySub07.setConfigCode("showRecordingPrompt");
        entitySub07.setConfigName("显示录制提示");
        entitySub07.setConfigComment("如果会议正在录制，在终端上显示录制提示");
        entitySub07.setConfigOrder(7);
        entitySub07.setPId(entitySub.getId());
        entitySub07.setDictionaryType(DeviceGroupEnum.SUBTYPE.name());
        entitySub07.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entitySub07);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entitySub07Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entitySub07Data01.setConfigId(entitySub07.getId());
        entitySub07Data01.setClientConfigName("common");
        entitySub07Data01.setConfigName("showRecordingIndicator");
        entitySub07Data01.setConfigShowName("显示录制提示");
        entitySub07Data01.setConfigComment("如果会议正在录制，在终端上显示录制提示");
        entitySub07Data01.setConfigDictType("OpenClose");
        entitySub07Data01.setConfigValuesType(ConfigValuesTypeEnum.SWITCH.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entitySub07Data01);

        //AGC增益调节
        DeviceSubtypeSeriesConfigDictionaryEntity entitySub08 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entitySub08.setConfigCode("agcenabled");
        entitySub08.setConfigName("AGC增益调节");
        entitySub08.setConfigComment("AGC增益调节");
        entitySub08.setConfigOrder(8);
        entitySub08.setPId(entitySub.getId());
        entitySub08.setDictionaryType(DeviceGroupEnum.SUBTYPE.name());
        entitySub08.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entitySub08);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entitySub08Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entitySub08Data01.setConfigId(entitySub08.getId());
        entitySub08Data01.setClientConfigName("common");
        entitySub08Data01.setConfigName("agcenabled");
        entitySub08Data01.setConfigShowName("AGC增益调节");
        entitySub08Data01.setConfigComment("AGC增益调节");
        entitySub08Data01.setConfigDictType("AutomaticManual");
        entitySub08Data01.setConfigValuesType(ConfigValuesTypeEnum.RADIO.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entitySub08Data01);

        //麦克风输入音量
        DeviceSubtypeSeriesConfigDictionaryEntity entitySub09 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entitySub09.setConfigCode("micfixedgainvalue");
        entitySub09.setConfigName("麦克风输入音量");
        entitySub09.setConfigComment("麦克风输入音量");
        entitySub09.setConfigOrder(9);
        entitySub09.setPId(entitySub.getId());
        entitySub09.setDictionaryType(DeviceGroupEnum.SUBTYPE.name());
        entitySub09.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entitySub09);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entitySub09Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entitySub09Data01.setConfigId(entitySub09.getId());
        entitySub09Data01.setClientConfigName("common");
        entitySub09Data01.setConfigName("micfixedgainvalue");
        entitySub09Data01.setConfigShowName("麦克风输入音量");
        entitySub09Data01.setConfigComment("麦克风输入音量");
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entitySub09Data01);

        //二维码
        DeviceSubtypeSeriesConfigDictionaryEntity entitySub10 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entitySub10.setConfigCode("qRCode");
        entitySub10.setConfigName("二维码");
        entitySub10.setConfigOrder(10);
        entitySub10.setPId(entitySub.getId());
        entitySub10.setDictionaryType(DeviceGroupEnum.SUBTYPE.name());
        entitySub10.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entitySub10);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entitySub10Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entitySub10Data01.setConfigId(entitySub10.getId());
        entitySub10Data01.setClientConfigName("common");
        entitySub10Data01.setConfigName("show2DimensionalBarcode");
        entitySub10Data01.setConfigShowName("二维码");
        entitySub10Data01.setConfigDictType("HideShow");
        entitySub10Data01.setConfigValuesType(ConfigValuesTypeEnum.EYE.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entitySub10Data01);

        //头像
        DeviceSubtypeSeriesConfigDictionaryEntity entitySub11 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entitySub11.setConfigCode("avatar");
        entitySub11.setConfigName("头像");
        entitySub11.setConfigComment("设置头像");
        entitySub11.setConfigOrder(11);
        entitySub11.setPId(entitySub.getId());
        entitySub11.setDictionaryType(DeviceGroupEnum.SUBTYPE.name());
        entitySub11.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entitySub11);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entitySub11Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entitySub11Data01.setConfigId(entitySub11.getId());
        entitySub11Data01.setClientConfigName("common");
        entitySub11Data01.setConfigName("avatar");
        entitySub11Data01.setConfigShowName("头像");
        entitySub11Data01.setConfigComment("设置头像");
        entitySub11Data01.setConfigValuesType(ConfigValuesTypeEnum.FILE.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entitySub11Data01);

        // 本地名称显示
        DeviceSubtypeSeriesConfigDictionaryEntity entitySub12 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entitySub12.setConfigCode("videoDisplayNameStyle");
        entitySub12.setConfigName("本地名称显示");
        entitySub12.setConfigComment("本地名称显示");
        entitySub12.setConfigOrder(12);
        entitySub12.setPId(entitySub.getId());
        entitySub12.setDictionaryType(DeviceGroupEnum.SUBTYPE.name());
        entitySub12.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entitySub12);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entitySub12Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entitySub12Data01.setConfigId(entitySub12.getId());
        entitySub12Data01.setClientConfigName("common");
        entitySub12Data01.setConfigName("VideoDisplayNameStyle");
        entitySub12Data01.setConfigShowName("本地名称显示");
        entitySub12Data01.setConfigComment("本地名称显示");
        entitySub12Data01.setConfigValuesType(ConfigValuesTypeEnum.SELECTED.name());
        entitySub12Data01.setConfigDictType("LabelStyle");
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entitySub12Data01);

        // SERIES配置
        // 通话设置
        DeviceSubtypeSeriesConfigDictionaryEntity entity = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity.setConfigCode("callSettings");
        entity.setConfigName("通话设置");
        entity.setConfigComment("通话设置");
        entity.setConfigOrder(1);
        entity.setPId(0L);
        entity.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity);

        // 通话中工具栏
        DeviceSubtypeSeriesConfigDictionaryEntity entity1 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity1.setConfigCode("inCallToolbar");
        entity1.setConfigName("通话中工具栏");
        entity1.setConfigComment("通话中工具栏");
        entity1.setConfigOrder(2);
        entity1.setPId(0L);
        entity1.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity1.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity1);

        // 系统设置
        DeviceSubtypeSeriesConfigDictionaryEntity entity2 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity2.setConfigCode("systemSettings");
        entity2.setConfigName("系统设置");
        entity2.setConfigComment("系统设置");
        entity2.setConfigOrder(3);
        entity2.setPId(0L);
        entity2.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity2.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity2);

        // 允许被呼叫
        DeviceSubtypeSeriesConfigDictionaryEntity entity01 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity01.setConfigCode("allowCalled");
        entity01.setConfigName("允许被呼叫");
        entity01.setConfigComment("设置设备响应呼叫的范围");
        entity01.setConfigOrder(1);
        entity01.setPId(entity.getId());
        entity01.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity01.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity01);

        DeviceSubtypeSeriesConfigDictionaryEntity entity0101 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity0101.setConfigCode("allowCalled-BUFFET");
        entity0101.setConfigName("支持管理平台批量设置");
        entity0101.setConfigOrder(1);
        entity0101.setPId(entity01.getId());
        entity0101.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity0101.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity0101);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity0101Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity0101Data01.setConfigId(entity0101.getId());
        entity0101Data01.setClientConfigName("common");
        entity0101Data01.setConfigName("allowNemoNumberCall");
        entity0101Data01.setConfigShowName("允许被呼叫");
        entity0101Data01.setConfigComment("设置设备响应呼叫的范围");
        entity0101Data01.setConfigDictType("AllowNemoNumberCall");
        entity0101Data01.setConfigValuesType(ConfigValuesTypeEnum.SELECTED.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity0101Data01);

        DeviceSubtypeSeriesConfigDictionaryEntity entity0102 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity0102.setConfigCode("allowCalled-CLIENT");
        entity0102.setConfigName("支持管理平台设置在终端隐藏/显示");
        entity0102.setConfigOrder(2);
        entity0102.setPId(entity01.getId());
        entity0102.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity0102.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity0102);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity0102Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity0102Data01.setConfigId(entity0102.getId());
        entity0102Data01.setClientConfigName("UIDisplayCustomization");
        entity0102Data01.setConfigName("nemoNumberCall");
        entity0102Data01.setConfigShowName("允许被呼叫");
        entity0102Data01.setConfigComment("设置设备响应呼叫的范围");
        entity0102Data01.setSpecial(1);
        entity0102Data01.setConfigDictType("HideShow");
        entity0102Data01.setConfigValuesType(ConfigValuesTypeEnum.EYE.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity0102Data01);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity0102Data02 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity0102Data02.setConfigId(entity0102.getId());
        entity0102Data02.setClientConfigName("UIDisplayCustomization");
        entity0102Data02.setConfigName("upComingMeetingsNotify");
        entity0102Data02.setConfigShowName("允许被呼叫");
        entity0102Data02.setConfigComment("设置设备响应呼叫的范围");
        entity0102Data02.setSpecial(0);
        entity0102Data02.setConfigDictType("HideShow");
        entity0102Data02.setConfigValuesType(ConfigValuesTypeEnum.EYE.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity0102Data02);

        // 允许主动呼叫
        DeviceSubtypeSeriesConfigDictionaryEntity entity02 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity02.setConfigCode("allowActiveCalls");
        entity02.setConfigName("允许主动呼叫");
        entity02.setConfigComment("设置设备呼叫的范围");
        entity02.setConfigOrder(2);
        entity02.setPId(entity.getId());
        entity02.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity02.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity02);

        DeviceSubtypeSeriesConfigDictionaryEntity entity0201 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity0201.setConfigCode("allowActiveCalls-BUFFET");
        entity0201.setConfigName("支持管理平台批量设置");
        entity0201.setConfigOrder(1);
        entity0201.setPId(entity02.getId());
        entity0201.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity0201.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity0201);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity0201Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity0201Data01.setConfigId(entity0201.getId());
        entity0201Data01.setClientConfigName("common");
        entity0201Data01.setConfigName("allowOutgoingCall");
        entity0201Data01.setConfigShowName("允许主动呼叫");
        entity0201Data01.setConfigComment("设置设备呼叫的范围");
        entity0201Data01.setConfigDictType("AllowOutgoingCall");
        entity0201Data01.setConfigValuesType(ConfigValuesTypeEnum.SELECTED.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity0201Data01);

        // 自动应答
        DeviceSubtypeSeriesConfigDictionaryEntity entity03 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity03.setConfigCode("autoAnswer");
        entity03.setConfigName("自动应答");
        entity03.setConfigComment("设置设备被呼叫时，自动接听的范围");
        entity03.setConfigOrder(3);
        entity03.setPId(entity.getId());
        entity03.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity03.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity03);

        DeviceSubtypeSeriesConfigDictionaryEntity entity0301 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity0301.setConfigCode("autoAnswer-BUFFET");
        entity0301.setConfigName("支持管理平台批量设置");
        entity0301.setConfigOrder(1);
        entity0301.setPId(entity03.getId());
        entity0301.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity0301.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity0301);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity0301Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity0301Data01.setConfigId(entity0301.getId());
        entity0301Data01.setClientConfigName("common");
        entity0301Data01.setConfigName("autoAnswer");
        entity0301Data01.setConfigShowName("自动应答");
        entity0301Data01.setConfigComment("设置设备被呼叫时，自动接听的范围");
        entity0301Data01.setConfigDictType("AutoAnswer");
        entity0301Data01.setConfigValuesType(ConfigValuesTypeEnum.SELECTED.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity0301Data01);

        DeviceSubtypeSeriesConfigDictionaryEntity entity0302 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity0302.setConfigCode("autoAnswer-CLIENT");
        entity0302.setConfigName("支持管理平台设置在终端隐藏/显示");
        entity0302.setConfigOrder(2);
        entity0302.setPId(entity03.getId());
        entity0302.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity0302.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity0302);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity0302Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity0302Data01.setConfigId(entity0302.getId());
        entity0302Data01.setClientConfigName("UIDisplayCustomization");
        entity0302Data01.setConfigName("autoAnswer");
        entity0302Data01.setConfigShowName("自动应答");
        entity0302Data01.setConfigComment("设置设备被呼叫时，自动接听的范围");
        entity0302Data01.setConfigDictType("HideShow");
        entity0302Data01.setConfigValuesType(ConfigValuesTypeEnum.EYE.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity0302Data01);

        // 通话中免打扰
        DeviceSubtypeSeriesConfigDictionaryEntity entity04 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity04.setConfigCode("doNotDisturbDuringCall");
        entity04.setConfigName("通话中免打扰");
        entity04.setConfigComment("终端在通话中不接受其他呼叫");
        entity04.setConfigOrder(4);
        entity04.setPId(entity.getId());
        entity04.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity04.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity04);

        DeviceSubtypeSeriesConfigDictionaryEntity entity0401 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity0401.setConfigCode("doNotDisturbDuringCall-BUFFET");
        entity0401.setConfigName("支持管理平台批量设置");
        entity0401.setConfigOrder(1);
        entity0401.setPId(entity04.getId());
        entity0401.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity0401.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity0401);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity0401Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity0401Data01.setConfigId(entity0401.getId());
        entity0401Data01.setClientConfigName("common");
        entity0401Data01.setConfigName("noDisturbIncall");
        entity0401Data01.setConfigShowName("通话中免打扰");
        entity0401Data01.setConfigComment("终端在通话中不接受其他呼叫");
        entity0401Data01.setConfigDictType("OpenClose");
        entity0401Data01.setConfigValuesType(ConfigValuesTypeEnum.SWITCH.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity0401Data01);

        DeviceSubtypeSeriesConfigDictionaryEntity entity0402 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity0402.setConfigCode("doNotDisturbDuringCall-CLIENT");
        entity0402.setConfigName("支持管理平台设置在终端隐藏/显示");
        entity0402.setConfigOrder(2);
        entity0402.setPId(entity04.getId());
        entity0402.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity0402.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity0402);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity0402Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity0402Data01.setConfigId(entity0402.getId());
        entity0402Data01.setClientConfigName("UIDisplayCustomization");
        entity0402Data01.setConfigName("showNotDisturbedIncall");
        entity0402Data01.setConfigShowName("通话中免打扰");
        entity0402Data01.setConfigComment("终端在通话中不接受其他呼叫");
        entity0402Data01.setConfigDictType("HideShow");
        entity0402Data01.setConfigValuesType(ConfigValuesTypeEnum.EYE.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity0402Data01);

        // 允许远程遥控本地摄像头
        DeviceSubtypeSeriesConfigDictionaryEntity entity05 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity05.setConfigCode("allowRemoteControlOfLocalCamera");
        entity05.setConfigName("允许远程遥控本地摄像头");
        entity05.setConfigComment("终端在通话中本地摄像头可被其他参会人遥控");
        entity05.setConfigOrder(5);
        entity05.setPId(entity.getId());
        entity05.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity05.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity05);

        DeviceSubtypeSeriesConfigDictionaryEntity entity0501 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity0501.setConfigCode("allowRemoteControlOfLocalCamera-BUFFET");
        entity0501.setConfigName("支持管理平台批量设置");
        entity0501.setConfigOrder(1);
        entity0501.setPId(entity05.getId());
        entity0501.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity0501.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity0501);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity0501Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity0501Data01.setConfigId(entity0501.getId());
        entity0501Data01.setClientConfigName("common");
        entity0501Data01.setConfigName("enableFecc");
        entity0501Data01.setConfigShowName("允许远程遥控本地摄像头");
        entity0501Data01.setConfigComment("终端在通话中本地摄像头可被其他参会人遥控");
        entity0501Data01.setConfigDictType("OpenClose");
        entity0501Data01.setConfigValuesType(ConfigValuesTypeEnum.SWITCH.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity0501Data01);

        DeviceSubtypeSeriesConfigDictionaryEntity entity0502 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity0502.setConfigCode("allowRemoteControlOfLocalCamera-CLIENT");
        entity0502.setConfigName("支持管理平台设置在终端隐藏/显示");
        entity0502.setConfigOrder(2);
        entity0502.setPId(entity05.getId());
        entity0502.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity0502.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity0502);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity0502Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity0502Data01.setConfigId(entity0502.getId());
        entity0502Data01.setClientConfigName("UIDisplayCustomization");
        entity0502Data01.setConfigName("showEnableFecc");
        entity0502Data01.setConfigShowName("允许远程遥控本地摄像头");
        entity0502Data01.setConfigComment("终端在通话中本地摄像头可被其他参会人遥控");
        entity0502Data01.setConfigDictType("HideShow");
        entity0502Data01.setConfigValuesType(ConfigValuesTypeEnum.EYE.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity0502Data01);

        // 显示录制提示
        DeviceSubtypeSeriesConfigDictionaryEntity entity06 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity06.setConfigCode("showRecordingPrompt");
        entity06.setConfigName("显示录制提示");
        entity06.setConfigComment("如果会议正在录制，在终端上显示录制提示");
        entity06.setConfigOrder(6);
        entity06.setPId(entity.getId());
        entity06.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity06.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity06);

        DeviceSubtypeSeriesConfigDictionaryEntity entity0601 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity0601.setConfigCode("showRecordingPrompt-BUFFET");
        entity0601.setConfigName("支持管理平台批量设置");
        entity0601.setConfigOrder(1);
        entity0601.setPId(entity06.getId());
        entity0601.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity0601.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity0601);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity0601Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity0601Data01.setConfigId(entity0601.getId());
        entity0601Data01.setClientConfigName("common");
        entity0601Data01.setConfigName("showRecordingIndicator");
        entity0601Data01.setConfigShowName("显示录制提示");
        entity0601Data01.setConfigComment("如果会议正在录制，在终端上显示录制提示");
        entity0601Data01.setConfigDictType("OpenClose");
        entity0601Data01.setConfigValuesType(ConfigValuesTypeEnum.SWITCH.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity0601Data01);

        DeviceSubtypeSeriesConfigDictionaryEntity entity0602 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity0602.setConfigCode("showRecordingPrompt-CLIENT");
        entity0602.setConfigName("支持管理平台设置在终端隐藏/显示");
        entity0602.setConfigOrder(2);
        entity0602.setPId(entity06.getId());
        entity0602.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity0602.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity0602);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity0602Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity0602Data01.setConfigId(entity0602.getId());
        entity0602Data01.setClientConfigName("UIDisplayCustomization");
        entity0602Data01.setConfigName("showRecordTips");
        entity0602Data01.setConfigShowName("显示录制提示");
        entity0602Data01.setConfigComment("如果会议正在录制，在终端上显示录制提示");
        entity0602Data01.setConfigDictType("HideShow");
        entity0602Data01.setConfigValuesType(ConfigValuesTypeEnum.EYE.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity0602Data01);

        // 开启/停止录制
        DeviceSubtypeSeriesConfigDictionaryEntity entity11 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity11.setConfigCode("start/stopRecording");
        entity11.setConfigName("开启/停止录制");
        entity11.setConfigOrder(1);
        entity11.setPId(entity1.getId());
        entity11.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity11.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity11);

        DeviceSubtypeSeriesConfigDictionaryEntity entity110 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity110.setConfigCode("start/stopRecording-CLIENT");
        entity110.setConfigName("支持管理平台设置在终端隐藏/显示");
        entity110.setConfigOrder(1);
        entity110.setPId(entity11.getId());
        entity110.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity110.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity110);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity110Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity110Data01.setConfigId(entity110.getId());
        entity110Data01.setClientConfigName("common");
        entity110Data01.setConfigName("recordingInCall");
        entity110Data01.setConfigShowName("开启/停止录制");
        entity110Data01.setConfigDictType("HideShow");
        entity110Data01.setConfigValuesType(ConfigValuesTypeEnum.EYE.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity110Data01);

        // 会议管理
        DeviceSubtypeSeriesConfigDictionaryEntity entity12 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity12.setConfigCode("meetingManagement");
        entity12.setConfigName("会议管理");
        entity12.setConfigOrder(2);
        entity12.setPId(entity1.getId());
        entity12.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity12.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity12);

        DeviceSubtypeSeriesConfigDictionaryEntity entity120 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity120.setConfigCode("meetingManagement-CLIENT");
        entity120.setConfigName("支持管理平台设置在终端隐藏/显示");
        entity120.setConfigOrder(1);
        entity120.setPId(entity12.getId());
        entity120.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity120.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity120);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity120Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity120Data01.setConfigId(entity120.getId());
        entity120Data01.setClientConfigName("common");
        entity120Data01.setConfigName("enableMeetingControl");
        entity120Data01.setConfigShowName("会议管理");
        entity120Data01.setConfigDictType("HideShow");
        entity120Data01.setConfigValuesType(ConfigValuesTypeEnum.EYE.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity120Data01);

        // 邀请通话
        DeviceSubtypeSeriesConfigDictionaryEntity entity13 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity13.setConfigCode("inviteCall");
        entity13.setConfigName("邀请通话");
        entity13.setConfigOrder(3);
        entity13.setPId(entity1.getId());
        entity13.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity13.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity13);

        DeviceSubtypeSeriesConfigDictionaryEntity entity130 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity130.setConfigCode("inviteCall-CLIENT");
        entity130.setConfigName("支持管理平台设置在终端隐藏/显示");
        entity130.setConfigOrder(1);
        entity130.setPId(entity13.getId());
        entity130.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity130.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity130);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity130Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity130Data01.setConfigId(entity130.getId());
        entity130Data01.setClientConfigName("UIDisplayCustomization");
        entity130Data01.setConfigName("showInviteInCall");
        entity130Data01.setConfigShowName("邀请通话");
        entity130Data01.setConfigDictType("HideShow");
        entity130Data01.setConfigValuesType(ConfigValuesTypeEnum.EYE.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity130Data01);

        // 开启/退出白板
        DeviceSubtypeSeriesConfigDictionaryEntity entity14 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity14.setConfigCode("start/stopWhiteboard");
        entity14.setConfigName("开启/退出白板");
        entity14.setConfigOrder(4);
        entity14.setPId(entity1.getId());
        entity14.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity14.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity14);

        DeviceSubtypeSeriesConfigDictionaryEntity entity140 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity140.setConfigCode("start/stopWhiteboard-CLIENT");
        entity140.setConfigName("支持管理平台设置在终端隐藏/显示");
        entity140.setConfigOrder(1);
        entity140.setPId(entity14.getId());
        entity140.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity140.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity140);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity140Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity140Data01.setConfigId(entity140.getId());
        entity140Data01.setClientConfigName("common");
        entity140Data01.setConfigName("enableWhiteboard");
        entity140Data01.setConfigShowName("开启/退出白板");
        entity140Data01.setConfigDictType("HideShow");
        entity140Data01.setConfigValuesType(ConfigValuesTypeEnum.EYE.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity140Data01);

        // 二维码
        DeviceSubtypeSeriesConfigDictionaryEntity entity21 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity21.setConfigCode("qRCode");
        entity21.setConfigName("二维码");
        entity21.setConfigOrder(1);
        entity21.setPId(entity2.getId());
        entity21.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity21.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity21);

        DeviceSubtypeSeriesConfigDictionaryEntity entity210 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity210.setConfigCode("qRCode-CLIENT");
        entity210.setConfigName("支持管理平台设置在终端隐藏/显示");
        entity210.setConfigOrder(1);
        entity210.setPId(entity21.getId());
        entity210.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity210.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity210);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity210Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity210Data01.setConfigId(entity210.getId());
        entity210Data01.setClientConfigName("common");
        entity210Data01.setConfigName("show2DimensionalBarcode");
        entity210Data01.setConfigShowName("二维码");
        entity210Data01.setConfigDictType("HideShow");
        entity210Data01.setConfigValuesType(ConfigValuesTypeEnum.EYE.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity210Data01);

        // 恢复出厂设置
        DeviceSubtypeSeriesConfigDictionaryEntity entity22 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity22.setConfigCode("enableSoftFactoryRestore");
        entity22.setConfigName("恢复出厂设置");
        entity22.setConfigOrder(2);
        entity22.setPId(entity2.getId());
        entity22.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity22.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity22);

        DeviceSubtypeSeriesConfigDictionaryEntity entity220 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity220.setConfigCode("enableSoftFactoryRestore-CLIENT");
        entity220.setConfigName("支持管理平台设置在终端隐藏/显示");
        entity220.setConfigOrder(1);
        entity220.setPId(entity22.getId());
        entity220.setDictionaryType(DeviceGroupEnum.SERIES.name());
        entity220.setConfigType(ConfigTypeEnum.ITEM.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity220);

        DeviceSubtypeSeriesConfigDictionaryDataEntity entity220Data01 = new DeviceSubtypeSeriesConfigDictionaryDataEntity();
        entity220Data01.setConfigId(entity220.getId());
        entity220Data01.setClientConfigName("common");
        entity220Data01.setConfigName("enableSoftFactoryRestore");
        entity220Data01.setConfigShowName("恢复出厂设置");
        entity220Data01.setConfigDictType("HideShow");
        entity220Data01.setConfigValuesType(ConfigValuesTypeEnum.EYE.name());
        deviceSubtypeSeriesConfigDictionaryDataMapper.insert(entity220Data01);

    }


    /**
     * 初始化系列以及系列终端类型对应关系
     */
    @Test
    public void initSeries() {
        // 允许被呼叫	允许主动呼叫	自动应答	通话中免打扰	允许远程遥控本地摄像头	显示录制提示	 开启/停止录制	会议管理	邀请通话	开启/退出白板	二维码	恢复出厂设置
        List<Integer> ne = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1);
        List<Integer> me20 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> me40 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> me90 = Arrays.asList(1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> android = Arrays.asList(1, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
        List<Integer> me60 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ne20Xp = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 0, 1, 1);
        List<Integer> ae2060 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1);
        List<Integer> ae20 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ae40 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ae700 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ae70 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ae90 = Arrays.asList(1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ae95 = Arrays.asList(1, 0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> es700 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> me40II = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ae600 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ts600 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ts700 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ts200 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> me50s = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ae2020 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1);
        List<Integer> me60s = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ae620 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ae720 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ae2062 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1, 1, 1, 1);

        List<Integer> ae42 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ae22 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> me55s = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ae650 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);
        List<Integer> ae2022 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1);
        List<Integer> ae1000 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1);
        List<Integer> ne90 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 1, 0, 1, 0, 0);
        List<Integer> ae800 = Arrays.asList(1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1, 0);


        Map<String, Object> query = new HashMap<>();
        query.put("status", 0);
        query.put("dictionary_type", DeviceGroupEnum.SERIES.name());
        query.put("config_type", "ITEM");
        List<DeviceSubtypeSeriesConfigDictionaryEntity> allConfigs = deviceSubtypeSeriesConfigDictionaryMapper.selectByMap(query);
        // NE系列
        DeviceSeriesEntity deviceSeriesEntity1 = new DeviceSeriesEntity();
        deviceSeriesEntity1.setSeriesName("NE60/80/20系列");
        deviceSeriesEntity1.setSpecial(1);
        deviceSeriesMapper.insert(deviceSeriesEntity1);

        List<Integer> series1Subtypes = Arrays.asList(27, 21, 22, 20);
        for (Integer subtype : series1Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity1.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }
        for (int i = 0; i < allConfigs.size(); i++) {
            if (ne.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity1.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // ME20系列
        DeviceSeriesEntity deviceSeriesEntity2 = new DeviceSeriesEntity();
        deviceSeriesEntity2.setSeriesName("ME20系列");
        deviceSeriesMapper.insert(deviceSeriesEntity2);

        List<Integer> series2Subtypes = Arrays.asList(80, 800, 801, 802, 803);
        for (Integer subtype : series2Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity2.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }
        for (int i = 0; i < allConfigs.size(); i++) {
            if (me20.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity2.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }
        // ME40系列
        DeviceSeriesEntity deviceSeriesEntity3 = new DeviceSeriesEntity();
        deviceSeriesEntity3.setSeriesName("ME40系列");
        deviceSeriesMapper.insert(deviceSeriesEntity3);

        List<Integer> series3Subtypes = Arrays.asList(82, 820, 821, 824, 870, 822, 823);
        for (Integer subtype : series3Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity3.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }
        for (int i = 0; i < allConfigs.size(); i++) {
            if (me40.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity3.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }
        // ME90系列
        DeviceSeriesEntity deviceSeriesEntity4 = new DeviceSeriesEntity();
        deviceSeriesEntity4.setSeriesName("ME90系列");
        deviceSeriesMapper.insert(deviceSeriesEntity4);

        List<Integer> series4Subtypes = Arrays.asList(7);
        for (Integer subtype : series4Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity4.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }
        for (int i = 0; i < allConfigs.size(); i++) {
            if (me90.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity4.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }
        // Android触屏
        DeviceSeriesEntity deviceSeriesEntity5 = new DeviceSeriesEntity();
        deviceSeriesEntity5.setSeriesName("Android触屏");
        deviceSeriesMapper.insert(deviceSeriesEntity5);

        List<Integer> series5Subtypes = Arrays.asList(841, 843);
        for (Integer subtype : series5Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity5.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (android.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity5.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }


        // ME60
        DeviceSeriesEntity deviceSeriesEntity7 = new DeviceSeriesEntity();
        deviceSeriesEntity7.setSeriesName("ME60II系列");
        deviceSeriesMapper.insert(deviceSeriesEntity7);

        List<Integer> series7Subtypes = Arrays.asList(830, 831, 825);
        for (Integer subtype : series7Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity7.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (me60.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity7.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // NE20XP系列
        DeviceSeriesEntity deviceSeriesEntity8 = new DeviceSeriesEntity();
        deviceSeriesEntity8.setSeriesName("NE20XP系列");
        deviceSeriesMapper.insert(deviceSeriesEntity8);

        List<Integer> series8Subtypes = Arrays.asList(28, 880);
        for (Integer subtype : series8Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity8.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ne20Xp.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity8.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }
        // AE2060/AE2080
        DeviceSeriesEntity deviceSeriesEntity9 = new DeviceSeriesEntity();
        deviceSeriesEntity9.setSeriesName("AE2060/AE2080");
        deviceSeriesEntity9.setSpecial(1);
        deviceSeriesMapper.insert(deviceSeriesEntity9);

        List<Integer> series9Subtypes = Arrays.asList(23, 24);
        for (Integer subtype : series9Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity9.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae2060.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity9.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE20系列
        DeviceSeriesEntity deviceSeriesEntity10 = new DeviceSeriesEntity();
        deviceSeriesEntity10.setSeriesName("AE20系列");
        deviceSeriesMapper.insert(deviceSeriesEntity10);

        List<Integer> series10Subtypes = Arrays.asList(87, 870);
        for (Integer subtype : series10Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity10.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae20.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity10.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE40系列
        DeviceSeriesEntity deviceSeriesEntity11 = new DeviceSeriesEntity();
        deviceSeriesEntity11.setSeriesName("AE40系列");
        deviceSeriesMapper.insert(deviceSeriesEntity11);

        List<Integer> series11Subtypes = Arrays.asList(86, 860);
        for (Integer subtype : series11Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity11.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae40.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity11.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE700系列
        DeviceSeriesEntity deviceSeriesEntity12 = new DeviceSeriesEntity();
        deviceSeriesEntity12.setSeriesName("AE700系列");
        deviceSeriesMapper.insert(deviceSeriesEntity12);

        List<Integer> series12Subtypes = Arrays.asList(832);
        for (Integer subtype : series12Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity12.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae700.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity12.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE70系列
        DeviceSeriesEntity deviceSeriesEntity13 = new DeviceSeriesEntity();
        deviceSeriesEntity13.setSeriesName("AE70系列");
        deviceSeriesMapper.insert(deviceSeriesEntity13);

        List<Integer> series13Subtypes = Arrays.asList(76);
        for (Integer subtype : series13Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity13.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae70.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity13.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE90系列
        DeviceSeriesEntity deviceSeriesEntity14 = new DeviceSeriesEntity();
        deviceSeriesEntity14.setSeriesName("AE90系列");
        deviceSeriesMapper.insert(deviceSeriesEntity14);

        List<Integer> series14Subtypes = Arrays.asList(74);
        for (Integer subtype : series14Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity14.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae90.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity14.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE95系列
        DeviceSeriesEntity deviceSeriesEntity15 = new DeviceSeriesEntity();
        deviceSeriesEntity15.setSeriesName("AE95系列");
        deviceSeriesMapper.insert(deviceSeriesEntity15);

        List<Integer> series15Subtypes = Arrays.asList(75);
        for (Integer subtype : series15Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity15.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae95.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity15.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // ES700系列
        DeviceSeriesEntity deviceSeriesEntity16 = new DeviceSeriesEntity();
        deviceSeriesEntity16.setSeriesName("ES700系列");
        deviceSeriesMapper.insert(deviceSeriesEntity16);

        List<Integer> series16Subtypes = Arrays.asList(833);
        for (Integer subtype : series16Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity16.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (es700.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity16.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // ME40II系列
        DeviceSeriesEntity deviceSeriesEntity17 = new DeviceSeriesEntity();
        deviceSeriesEntity17.setSeriesName("ME40II系列");
        deviceSeriesMapper.insert(deviceSeriesEntity17);

        List<Integer> series17Subtypes = Arrays.asList(825);
        for (Integer subtype : series17Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity17.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (me40II.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity17.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE600系列
        DeviceSeriesEntity deviceSeriesEntity18 = new DeviceSeriesEntity();
        deviceSeriesEntity18.setSeriesName("AE600系列");
        deviceSeriesMapper.insert(deviceSeriesEntity18);

        List<Integer> series18Subtypes = Arrays.asList(827);
        for (Integer subtype : series18Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity18.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae600.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity18.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // TS600系列
        DeviceSeriesEntity deviceSeriesEntity19 = new DeviceSeriesEntity();
        deviceSeriesEntity19.setSeriesName("TS600系列");
        deviceSeriesMapper.insert(deviceSeriesEntity19);

        List<Integer> series19Subtypes = Arrays.asList(836);
        for (Integer subtype : series19Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity19.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ts600.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity19.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // TS700系列
        DeviceSeriesEntity deviceSeriesEntity20 = new DeviceSeriesEntity();
        deviceSeriesEntity20.setSeriesName("TS700系列");
        deviceSeriesMapper.insert(deviceSeriesEntity20);

        List<Integer> series20Subtypes = Arrays.asList(835);
        for (Integer subtype : series20Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity20.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ts700.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity20.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // TS200系列
        DeviceSeriesEntity deviceSeriesEntity21 = new DeviceSeriesEntity();
        deviceSeriesEntity21.setSeriesName("TS200系列");
        deviceSeriesMapper.insert(deviceSeriesEntity21);

        List<Integer> series21Subtypes = Arrays.asList(837);
        for (Integer subtype : series21Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity21.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ts200.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity21.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // ME50S系列
        DeviceSeriesEntity deviceSeriesEntity22 = new DeviceSeriesEntity();
        deviceSeriesEntity22.setSeriesName("ME50S系列");
        deviceSeriesMapper.insert(deviceSeriesEntity22);

        List<Integer> series22Subtypes = Arrays.asList(828);
        for (Integer subtype : series22Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity22.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (me50s.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity22.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE2020系列
        DeviceSeriesEntity deviceSeriesEntity23 = new DeviceSeriesEntity();
        deviceSeriesEntity23.setSeriesName("AE2020系列");
        deviceSeriesMapper.insert(deviceSeriesEntity23);

        List<Integer> series23Subtypes = Arrays.asList(29);
        for (Integer subtype : series23Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity23.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae2020.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity23.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }
        // ME60S系列
        DeviceSeriesEntity deviceSeriesEntity24 = new DeviceSeriesEntity();
        deviceSeriesEntity24.setSeriesName("ME60S系列");
        deviceSeriesMapper.insert(deviceSeriesEntity24);

        List<Integer> series24Subtypes = Arrays.asList(838);
        for (Integer subtype : series24Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity24.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (me60s.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity24.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE620系列
        DeviceSeriesEntity deviceSeriesEntity25 = new DeviceSeriesEntity();
        deviceSeriesEntity25.setSeriesName("AE620系列");
        deviceSeriesMapper.insert(deviceSeriesEntity25);

        List<Integer> series25Subtypes = Arrays.asList(890);
        for (Integer subtype : series25Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity25.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae620.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity25.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE720系列
        DeviceSeriesEntity deviceSeriesEntity26 = new DeviceSeriesEntity();
        deviceSeriesEntity26.setSeriesName("AE720系列");
        deviceSeriesMapper.insert(deviceSeriesEntity26);

        List<Integer> series26Subtypes = Arrays.asList(891);
        for (Integer subtype : series26Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity26.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae720.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity26.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE2062/2082系列
        DeviceSeriesEntity deviceSeriesEntity27 = new DeviceSeriesEntity();
        deviceSeriesEntity27.setSeriesName("AE2062/2082系列");
        deviceSeriesEntity27.setSpecial(1);
        deviceSeriesMapper.insert(deviceSeriesEntity27);

        List<Integer> series27Subtypes = Arrays.asList(201, 202);
        for (Integer subtype : series27Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity27.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae2062.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity27.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE42系列
        DeviceSeriesEntity deviceSeriesEntity28 = new DeviceSeriesEntity();
        deviceSeriesEntity28.setSeriesName("AE42系列");
        deviceSeriesMapper.insert(deviceSeriesEntity28);

        List<Integer> series28Subtypes = Arrays.asList(895);
        for (Integer subtype : series28Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity28.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae42.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity28.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE22系列
        DeviceSeriesEntity deviceSeriesEntity29 = new DeviceSeriesEntity();
        deviceSeriesEntity29.setSeriesName("AE22系列");
        deviceSeriesMapper.insert(deviceSeriesEntity29);

        List<Integer> series29Subtypes = Arrays.asList(894);
        for (Integer subtype : series29Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity29.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae22.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity29.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        //ME55S系列
        DeviceSeriesEntity deviceSeriesEntity30 = new DeviceSeriesEntity();
        deviceSeriesEntity30.setSeriesName("ME55S系列");
        deviceSeriesMapper.insert(deviceSeriesEntity30);

        List<Integer> series30Subtypes = Arrays.asList(839);
        for (Integer subtype : series30Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity30.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (me55s.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity30.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }
        // AE650系列
        DeviceSeriesEntity deviceSeriesEntity31 = new DeviceSeriesEntity();
        deviceSeriesEntity31.setSeriesName("AE650系列");
        deviceSeriesMapper.insert(deviceSeriesEntity31);

        List<Integer> series31Subtypes = Arrays.asList(898);
        for (Integer subtype : series31Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity31.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae650.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity31.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }
        // AE2022系列
        DeviceSeriesEntity deviceSeriesEntity32 = new DeviceSeriesEntity();
        deviceSeriesEntity32.setSeriesName("AE2022系列");
        deviceSeriesMapper.insert(deviceSeriesEntity32);

        List<Integer> series32Subtypes = Arrays.asList(203);
        for (Integer subtype : series32Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity32.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae2022.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity32.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE1000系列
        DeviceSeriesEntity deviceSeriesEntity33 = new DeviceSeriesEntity();
        deviceSeriesEntity33.setSeriesName("AE1000系列");
        deviceSeriesMapper.insert(deviceSeriesEntity33);

        List<Integer> series33Subtypes = Arrays.asList(892);
        for (Integer subtype : series33Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity33.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae1000.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity33.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }


        // NE90系列
        DeviceSeriesEntity deviceSeriesEntity34 = new DeviceSeriesEntity();
        deviceSeriesEntity34.setSeriesName("NE90系列");
        deviceSeriesEntity34.setSpecial(1);
        deviceSeriesMapper.insert(deviceSeriesEntity34);

        List<Integer> series34Subtypes = Arrays.asList(204, 206,208);
        for (Integer subtype : series34Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity34.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ne90.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity34.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE800系列
        DeviceSeriesEntity deviceSeriesEntity35 = new DeviceSeriesEntity();
        deviceSeriesEntity35.setSeriesName("AE800系列");
        deviceSeriesMapper.insert(deviceSeriesEntity35);

        List<Integer> series35Subtypes = Arrays.asList(893);
        for (Integer subtype : series35Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity35.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ae800.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity35.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }

        // AE350系列
        DeviceSeriesEntity deviceSeriesEntity36 = new DeviceSeriesEntity();
        deviceSeriesEntity36.setSeriesName("AE300系列");
        deviceSeriesEntity36.setSpecial(1);
        deviceSeriesMapper.insert(deviceSeriesEntity36);

        List<Integer> series36Subtypes = Arrays.asList(207,209);
        for (Integer subtype : series36Subtypes) {
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = new DeviceSeriesSubtypeEntity();
            deviceSeriesSubtypeEntity.setSeriesId(deviceSeriesEntity36.getId());
            deviceSeriesSubtypeEntity.setSubtype(subtype);
            deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        }

        for (int i = 0; i < allConfigs.size(); i++) {
            if (ne90.get(i) == 1) {
                DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                deviceSubtypeSeriesConfigEntity.setSeriesId(deviceSeriesEntity36.getId());
                deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
                deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);
            }
        }
    }

    /**
     *  初始化终端配置
     */
    @Test
    public void initSubtype() {
        Map<String, Object> query = new HashMap<>();
        query.put("status", 0);
        query.put("dictionary_type", DeviceGroupEnum.SUBTYPE.name());
        query.put("config_type", "ITEM");
        List<DeviceSubtypeSeriesConfigDictionaryEntity> allConfigs = deviceSubtypeSeriesConfigDictionaryMapper.selectByMap(query);
        // 管理员 1  允许被呼叫 2 允许主动呼叫 3  自动应答 4  通话中免打扰 5 允许远程遥控本地摄像头 6 显示录制提示 7   AGC增益调节 8 麦克风输入音量 9  二维码 10  头像 11   本地显示 12
        List<Integer> list81 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list10 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list83 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,0);
        List<Integer> list13 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list85 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list74 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,1);
        List<Integer> list15 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list75 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,1);
        List<Integer> list86 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,1);
        List<Integer> list87 = Arrays.asList(0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0,1);
        List<Integer> list23 = Arrays.asList(1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1,0);
        List<Integer> list20 = Arrays.asList(1, 1, 1, 1, 1, 1, 0, 0, 1, 0, 1,0);
        List<Integer> list76 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,1);
        List<Integer> list201 = Arrays.asList(1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1,0);
        List<Integer> list202 = Arrays.asList(1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1,0);
        List<Integer> list203 = Arrays.asList(1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1,0);
        List<Integer> list204 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1,0);
        List<Integer> list21 = Arrays.asList(1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1,0);
        List<Integer> list205 = Arrays.asList(1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1,0);
        List<Integer> list211 = Arrays.asList(1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1,0);
        List<Integer> list24 = Arrays.asList(1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1,0);
        List<Integer> list25 = Arrays.asList(1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1,0);
        List<Integer> list26 = Arrays.asList(1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1,0);
        List<Integer> list27 = Arrays.asList(1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1,0);
        List<Integer> list28 = Arrays.asList(1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1,0);
        List<Integer> list29 = Arrays.asList(1, 1, 1, 1, 1, 0, 0, 0, 1, 1, 1,0);
        List<Integer> list3 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list22 = Arrays.asList(1, 1, 1, 1, 1, 1, 0, 0, 1, 1, 1,0);
        List<Integer> list70 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,1);
        List<Integer> list71 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,1);
        List<Integer> list72 = Arrays.asList(0, 1, 0, 1, 1, 1, 1, 1, 1, 1, 0,0);
        List<Integer> list710 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,1);
        List<Integer> list720 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,0);
        List<Integer> list77 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,1);
        List<Integer> list78 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,0);
        List<Integer> list73 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,1);
        List<Integer> list80 = Arrays.asList(0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0,1);
        List<Integer> list800 = Arrays.asList(0, 1, 1, 1, 1, 0, 1, 1, 1, 1, 0,1);
        List<Integer> list801 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,0);
        List<Integer> list802 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,0);
        List<Integer> list803 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,1);
        List<Integer> list82 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,1);
        List<Integer> list820 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,1);
        List<Integer> list821 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,1);
        List<Integer> list822 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,0);
        List<Integer> list823 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,0);
        List<Integer> list824 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,1);
        List<Integer> list825 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,0);
        List<Integer> list826 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,1);
        List<Integer> list827 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,1);
        List<Integer> list828 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,1);
        List<Integer> list830 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,1);
        List<Integer> list831 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,1);
        List<Integer> list832 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,1);
        List<Integer> list833 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,1);
        List<Integer> list834 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,0);
        List<Integer> list835 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,1);
        List<Integer> list836 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,1);
        List<Integer> list837 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,1);
        List<Integer> list838 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,1);
        List<Integer> list839 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,1);
        List<Integer> list84 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,0);
        List<Integer> list840 = Arrays.asList(0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0,0);
        List<Integer> list841 = Arrays.asList(0, 1, 0, 1, 0, 0, 0, 0, 1, 0, 0,0);
        List<Integer> list843 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 1, 0,0);
        List<Integer> list844 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list850 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list860 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0,1);
        List<Integer> list870 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0,1);
        List<Integer> list88 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list880 = Arrays.asList(0, 1, 1, 1, 1, 0, 0, 0, 1, 0, 0,0);
        List<Integer> list89 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list890 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,1);
        List<Integer> list891 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,1);
        List<Integer> list892 = Arrays.asList(0, 1, 1, 1, 1, 0, 1, 0, 1, 0, 0,1);
        List<Integer> list893 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0,0);
        List<Integer> list894 = Arrays.asList(0, 1, 1, 1, 1, 0, 1, 0, 1, 0, 0,1);
        List<Integer> list895 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 0, 0,1);
        List<Integer> list896 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,0);
        List<Integer> list898 = Arrays.asList(0, 1, 1, 1, 1, 1, 1, 0, 1, 1, 0,1);
        List<Integer> list5 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list1 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list9 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list11 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list12 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list17 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list31 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);
        List<Integer> list16 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 1, 0, 0,0);

        List<Integer> list206 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1,0);
        List<Integer> list207 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1,0);
        List<Integer> list208 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1,0);
        List<Integer> list209 = Arrays.asList(0, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1,0);



        Map<Integer, List<Integer>> mapping = new HashMap<>();
        mapping.put(81, list81);
        mapping.put(10, list10);
        mapping.put(83, list83);
        mapping.put(13, list13);
        mapping.put(85, list85);
        mapping.put(74, list74);
        mapping.put(75, list75);
        mapping.put(15, list15);
        mapping.put(86, list86);
        mapping.put(87, list87);
        mapping.put(23, list23);
        mapping.put(20, list20);
        mapping.put(76, list76);
        mapping.put(201, list201);
        mapping.put(202, list202);
        mapping.put(203, list203);
        mapping.put(204, list204);
        mapping.put(205, list205);
        mapping.put(211, list211);
        mapping.put(24, list24);
        mapping.put(25, list25);
        mapping.put(26, list26);
        mapping.put(27, list27);
        mapping.put(28, list28);
        mapping.put(29, list29);
        mapping.put(21, list21);
        mapping.put(3, list3);
        mapping.put(22, list22);
        mapping.put(70, list70);
        mapping.put(71, list71);
        mapping.put(72, list72);
        mapping.put(710, list710);
        mapping.put(720, list720);
        mapping.put(77, list77);
        mapping.put(78, list78);
        mapping.put(73, list73);
        mapping.put(80, list80);
        mapping.put(800, list800);
        mapping.put(801, list801);
        mapping.put(802, list802);
        mapping.put(803, list803);
        mapping.put(82, list82);
        mapping.put(820, list820);
        mapping.put(821, list821);
        mapping.put(822, list822);
        mapping.put(823, list823);
        mapping.put(824, list824);
        mapping.put(825, list825);
        mapping.put(826, list826);
        mapping.put(827, list827);
        mapping.put(828, list828);
        mapping.put(830, list830);
        mapping.put(831, list831);
        mapping.put(832, list832);
        mapping.put(833, list833);
        mapping.put(834, list834);
        mapping.put(835, list835);
        mapping.put(836, list836);
        mapping.put(837, list837);
        mapping.put(838, list838);
        mapping.put(839, list839);
        mapping.put(84, list84);
        mapping.put(840, list840);
        mapping.put(841, list841);
        mapping.put(843, list843);
        mapping.put(844, list844);
        mapping.put(850, list850);
        mapping.put(860, list860);
        mapping.put(870, list870);
        mapping.put(88, list88);
        mapping.put(880, list880);
        mapping.put(89, list89);
        mapping.put(890, list890);
        mapping.put(891, list891);
        mapping.put(892, list892);
        mapping.put(893, list893);
        mapping.put(894, list894);
        mapping.put(895, list895);
        mapping.put(896, list896);
        mapping.put(898, list898);
        mapping.put(5, list5);
        mapping.put(1, list1);
        mapping.put(9, list9);
        mapping.put(11, list11);
        mapping.put(12, list12);
        mapping.put(17, list17);
        mapping.put(31, list31);
        mapping.put(16, list16);
        mapping.put(206, list206);
        mapping.put(207, list207);
        mapping.put(208, list208);
        mapping.put(209, list209);


        mapping.forEach((key, selectedValue) -> {
            for (int i = 0; i < allConfigs.size(); i++) {
                if (selectedValue.get(i) == 1) {
                    DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
                    deviceSubtypeSeriesConfigEntity.setSubtype(key);
                    deviceSubtypeSeriesConfigEntity.setConfigId(allConfigs.get(i).getId());
                    deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
                    deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SUBTYPE.name());
                    deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity);

                    DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity2 = new DeviceSubtypeSeriesConfigEntity();
                    deviceSubtypeSeriesConfigEntity2.setSubtype(key);
                    deviceSubtypeSeriesConfigEntity2.setConfigId(allConfigs.get(i).getId());
                    deviceSubtypeSeriesConfigEntity2.setPlatform(PlatformEnum.DEVICE.name());
                    deviceSubtypeSeriesConfigEntity2.setType(DeviceGroupEnum.SUBTYPE.name());
                    deviceSubtypeSeriesConfigMapper.insert(deviceSubtypeSeriesConfigEntity2);
                }
            }
        });
    }

}
