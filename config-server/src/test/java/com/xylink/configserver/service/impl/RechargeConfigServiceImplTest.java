package com.xylink.configserver.service.impl;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class RechargeConfigServiceImplTest {

    @Autowired
    private RechargeConfigServiceImpl rechargeConfigService;

    @Test
    public void canOperate4KTest() throws InterruptedException {
        System.out.println(rechargeConfigService.canOperate4K(204));
        System.out.println(rechargeConfigService.canOperate4K(204));//缓存查询
        Thread.sleep(1000 * 60); //等待定时任务刷新缓存
    }
}
