package com.xylink.configserver.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.configserver.data.model.DeviceConfigUpdate;
import com.xylink.configserver.service.ConfigValueHandleService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/1/9 12:03 下午
 */
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class ConfigValueHandleServiceImplTest {

    @Autowired
    ConfigValueHandleService configValueHandleService;
    @Autowired
    ConfigValueHandleServiceImpl configValueHandleServiceImpl;


    @Test
    public void verifyTest() {
        // 模拟两种参数
        // 1
        Map<String, String> unsavedConfigs = new HashMap<>();
        unsavedConfigs.put("\"Common\"", "{\"advMode\":\"TRUE\"}");
        unsavedConfigs.put("\"UIDisplayCustomization\"", "{\"show4kResolution\":\"FALSE\"}");
        unsavedConfigs.put("\"mediaConfig\"", "{\"cvrxFrameRate\":\"ME40\"}");
        
        // System.out.println(configValueHandleService.verifyConfig(unsavedConfigs));
        
        
        // 2
        List<DeviceConfigUpdate> deviceConfigUpdateList = new ArrayList<>();
        deviceConfigUpdateList.add(new DeviceConfigUpdate("advMode", "TRUE", "Common"));
        deviceConfigUpdateList.add(new DeviceConfigUpdate("show4kResolution", "FALSE", "UIDisplayCustomization"));
        deviceConfigUpdateList.add(new DeviceConfigUpdate("cvrxFrameRate", "ME40", "mediaConfig"));

        //System.out.println(configValueHandleService.verifyConfig(deviceConfigUpdateList));
    }

    @Test
    public void parseTest() throws InterruptedException {
        // 测试配置解析
        Map<String, String> pendingConfigMap = new HashMap<>();

        // array测试
        String clientConfigName = "mediaConfig";
        pendingConfigMap.put("cvrxResolution", "['ME40', 'ME20', 'ME90']");

//        Object obj = configValueHandleService.parseConfig(pendingConfigMap, clientConfigName);
//        if (obj != null) {
//            System.out.println(obj.toString());
//        }
//        Thread.sleep(1000 * 60);
    }

    @Test
    public void oldParseProcess() {
        Object res =configValueHandleServiceImpl.oldParseProcess("\"\"true\"\"");
        System.out.println(res.toString());
    }

    @Test
    public void oldParseProcess1() throws JsonProcessingException {
         ObjectMapper jsonMapper = new ObjectMapper();
        Map<String, Object> map = new HashMap<>();
        map.put("test", true);
        System.out.println(jsonMapper.writeValueAsString(map));
        System.out.println(jsonMapper.writeValueAsString(jsonMapper.writeValueAsString(map)));

    }

    @Test
    public void parseConfigs() {
        Map<String, String> pendingConfigMap = new HashMap<>();
        pendingConfigMap.put("faceDetectFR", "\"1.0\"");
        String clientConfigName = "RemoteSDKConfig";

        Map<String, Object> map = configValueHandleServiceImpl.parseConfigs(pendingConfigMap, clientConfigName);
        String s = configValueHandleServiceImpl.transferToJson(map);
        System.out.println(s);

    }
}
