package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xylink.configserver.data.model.LibraDeviceSubtypeModel;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class LibraDeviceSubtypeModelMapperTest {

    @Autowired
    public LibraDeviceSubtypeModelMapper libraDeviceSubtypeModelMapper;

    @Test
    public void selectBySubtypeTest() {
        int subtype = 204;
        List<LibraDeviceSubtypeModel> res = libraDeviceSubtypeModelMapper.selectBySubtype(subtype);
        System.out.println(res);
    }

    @Test
    public void getAllDeviceTest() {
        List<LibraDeviceSubtypeModel> li = libraDeviceSubtypeModelMapper.selectList(new QueryWrapper<>());
        List<LibraDeviceSubtypeModel> li1 = libraDeviceSubtypeModelMapper.selectAll();
    }

}
