package com.xylink.configserver.mapper.deviceseries;

import com.xylink.configserver.data.model.deviceseries.DeviceConfigDictDataEntity;
import com.xylink.configserver.data.model.deviceseries.DeviceConfigDictTypeEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2021-02-18 17:56
 */
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class DeviceConfigDictTypeMapperTest {
    @Resource
    private DeviceConfigDictTypeMapper deviceConfigDictTypeMapper;
    @Resource
    private DeviceConfigDictDataMapper deviceConfigDictDataMapper;

    @Test
    public void init() {

        DeviceConfigDictTypeEntity entity1 = new DeviceConfigDictTypeEntity();
        entity1.setDictName("允许被呼叫");
        entity1.setDictType("AllowToBeCalled");
        entity1.setRemark("设置设备响应呼叫的范围");
        deviceConfigDictTypeMapper.insert(entity1);

        DeviceConfigDictDataEntity entity1Data1 = new DeviceConfigDictDataEntity();
        entity1Data1.setDictCode("关闭");
        entity1Data1.setDictValue("0");
        entity1Data1.setDictType(entity1.getDictType());
        entity1Data1.setDictSort(1);
        deviceConfigDictDataMapper.insert(entity1Data1);

        DeviceConfigDictDataEntity entity1Data2 = new DeviceConfigDictDataEntity();
        entity1Data2.setDictCode("所有用户");
        entity1Data2.setDictValue("1");
        entity1Data2.setDictType(entity1.getDictType());
        entity1Data2.setDictSort(2);
        deviceConfigDictDataMapper.insert(entity1Data2);


        DeviceConfigDictDataEntity entity1Data3 = new DeviceConfigDictDataEntity();
        entity1Data3.setDictCode("通讯录用户");
        entity1Data3.setDictValue("2");
        entity1Data3.setDictType(entity1.getDictType());
        entity1Data3.setDictSort(3);
        deviceConfigDictDataMapper.insert(entity1Data3);

        DeviceConfigDictDataEntity entity1Data4 = new DeviceConfigDictDataEntity();
        entity1Data4.setDictCode("所有非匿名用户");
        entity1Data4.setDictValue("3");
        entity1Data4.setDictType(entity1.getDictType());
        entity1Data4.setDictSort(4);
        deviceConfigDictDataMapper.insert(entity1Data4);


        DeviceConfigDictTypeEntity entity2 = new DeviceConfigDictTypeEntity();
        entity2.setDictName("开启/关闭");
        entity2.setDictType("OpenClose");
        entity2.setRemark("开关项");
        deviceConfigDictTypeMapper.insert(entity2);

        DeviceConfigDictDataEntity entity2Data1 = new DeviceConfigDictDataEntity();
        entity2Data1.setDictCode("关闭");
        entity2Data1.setDictValue("false");
        entity2Data1.setDictType(entity2.getDictType());
        entity2Data1.setDictSort(1);
        deviceConfigDictDataMapper.insert(entity2Data1);

        DeviceConfigDictDataEntity entity2Data2 = new DeviceConfigDictDataEntity();
        entity2Data2.setDictCode("开启");
        entity2Data2.setDictValue("true");
        entity2Data2.setDictType(entity2.getDictType());
        entity2Data2.setDictSort(2);
        deviceConfigDictDataMapper.insert(entity2Data2);



        DeviceConfigDictTypeEntity entity3 = new DeviceConfigDictTypeEntity();
        entity3.setDictName("允许主动呼叫");
        entity3.setDictType("AllowActiveCalls");
        entity3.setRemark("设置设备呼叫的范围");
        deviceConfigDictTypeMapper.insert(entity3);

        DeviceConfigDictDataEntity entity3Data1 = new DeviceConfigDictDataEntity();
        entity3Data1.setDictCode("关闭");
        entity3Data1.setDictValue("0");
        entity3Data1.setDictType(entity3.getDictType());
        entity3Data1.setDictSort(1);
        deviceConfigDictDataMapper.insert(entity3Data1);

        DeviceConfigDictDataEntity entity3Data2 = new DeviceConfigDictDataEntity();
        entity3Data2.setDictCode("所有用户");
        entity3Data2.setDictValue("1");
        entity3Data2.setDictType(entity3.getDictType());
        entity3Data2.setDictSort(2);
        deviceConfigDictDataMapper.insert(entity3Data2);


        DeviceConfigDictDataEntity entity3Data3 = new DeviceConfigDictDataEntity();
        entity3Data3.setDictCode("通讯录用户");
        entity3Data3.setDictValue("2");
        entity3Data3.setDictType(entity3.getDictType());
        entity3Data3.setDictSort(3);
        deviceConfigDictDataMapper.insert(entity3Data3);


        DeviceConfigDictTypeEntity entity4 = new DeviceConfigDictTypeEntity();
        entity4.setDictName("自动应答");
        entity4.setDictType("AutoAnswer");
        entity4.setRemark("设置设备被呼叫时，自动接听的范围");
        deviceConfigDictTypeMapper.insert(entity4);

        DeviceConfigDictDataEntity entity4Data1 = new DeviceConfigDictDataEntity();
        entity4Data1.setDictCode("关闭");
        entity4Data1.setDictValue("0");
        entity4Data1.setDictType(entity4.getDictType());
        entity4Data1.setDictSort(1);
        deviceConfigDictDataMapper.insert(entity4Data1);

        DeviceConfigDictDataEntity entity4Data2 = new DeviceConfigDictDataEntity();
        entity4Data2.setDictCode("所有用户");
        entity4Data2.setDictValue("1");
        entity4Data2.setDictType(entity4.getDictType());
        entity4Data2.setDictSort(2);
        deviceConfigDictDataMapper.insert(entity4Data2);


        DeviceConfigDictDataEntity entity4Data3 = new DeviceConfigDictDataEntity();
        entity4Data3.setDictCode("通讯录用户");
        entity4Data3.setDictValue("2");
        entity4Data3.setDictType(entity4.getDictType());
        entity4Data3.setDictSort(3);
        deviceConfigDictDataMapper.insert(entity4Data3);

    }

}
