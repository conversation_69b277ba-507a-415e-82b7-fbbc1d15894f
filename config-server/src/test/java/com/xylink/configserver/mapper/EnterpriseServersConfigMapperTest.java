package com.xylink.configserver.mapper;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2021-03-04 11:07
 */
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class EnterpriseServersConfigMapperTest {

    @Resource
    private EnterpriseServersConfigMapper enterpriseServersConfigMapper;


    @Test
    public void getDefaultServerConfigByConfigName(){
        System.out.println(enterpriseServersConfigMapper.getDefaultServerConfigByConfigName("errorcode"));
    }
}