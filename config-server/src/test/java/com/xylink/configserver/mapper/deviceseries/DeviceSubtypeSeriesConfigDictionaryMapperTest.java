package com.xylink.configserver.mapper.deviceseries;

import com.xylink.configserver.data.model.deviceseries.ConfigTypeEnum;
import com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigDictionaryEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-28 15:51
 */
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class DeviceSubtypeSeriesConfigDictionaryMapperTest {

    @Resource
    private DeviceSubtypeSeriesConfigDictionaryMapper deviceSubtypeSeriesConfigDictionaryMapper;

    @Test
    public void init() {
        DeviceSubtypeSeriesConfigDictionaryEntity entity = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity.setConfigCode("callSettings");
        entity.setConfigName("通话设置");
        entity.setConfigComment("通话设置");
        entity.setConfigOrder(1);
        entity.setPId(0L);
        entity.setDictionaryType("SERIES");
        entity.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity);


        DeviceSubtypeSeriesConfigDictionaryEntity entity1 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity1.setConfigCode("inCallToolbar");
        entity1.setConfigName("通话中工具栏");
        entity1.setConfigComment("通话中工具栏");
        entity1.setConfigOrder(2);
        entity1.setPId(0L);
        entity1.setDictionaryType("SERIES");
        entity1.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity1);


        DeviceSubtypeSeriesConfigDictionaryEntity entity2 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity2.setConfigCode("systemSettings");
        entity2.setConfigName("系统设置");
        entity2.setConfigComment("系统设置");
        entity2.setConfigOrder(3);
        entity2.setPId(0L);
        entity2.setDictionaryType("SERIES");
        entity2.setConfigType(ConfigTypeEnum.GROUP.name());
        deviceSubtypeSeriesConfigDictionaryMapper.insert(entity2);


        List<DeviceSubtypeSeriesConfigDictionaryEntity> data = new ArrayList<>();
        DeviceSubtypeSeriesConfigDictionaryEntity entity01 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity01.setConfigCode("allowCalled");
        entity01.setConfigName("允许被呼叫");
        entity01.setConfigComment("允许被呼叫");
        entity01.setConfigOrder(1);
        entity01.setPId(entity.getId());
        entity01.setDictionaryType("SERIES");
        entity01.setConfigType(ConfigTypeEnum.ITEM.name());
        data.add(entity01);


        DeviceSubtypeSeriesConfigDictionaryEntity entity02 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity02.setConfigCode("allowActiveCalls");
        entity02.setConfigName("允许主动呼叫");
        entity02.setConfigComment("允许主动呼叫");
        entity02.setConfigOrder(2);
        entity02.setPId(entity.getId());
        entity02.setDictionaryType("SERIES");
        entity02.setConfigType(ConfigTypeEnum.ITEM.name());
        data.add(entity02);

        DeviceSubtypeSeriesConfigDictionaryEntity entity03 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity03.setConfigCode("autoAnswer");
        entity03.setConfigName("自动应答");
        entity03.setConfigComment("自动应答");
        entity03.setConfigOrder(3);
        entity03.setPId(entity.getId());
        entity03.setDictionaryType("SERIES");
        entity03.setConfigType(ConfigTypeEnum.ITEM.name());
        data.add(entity03);

        DeviceSubtypeSeriesConfigDictionaryEntity entity04 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity04.setConfigCode("doNotDisturbDuringCall");
        entity04.setConfigName("通话中免打扰");
        entity04.setConfigComment("通话中免打扰");
        entity04.setConfigOrder(4);
        entity04.setPId(entity.getId());
        entity04.setDictionaryType("SERIES");
        entity04.setConfigType(ConfigTypeEnum.ITEM.name());
        data.add(entity04);


        DeviceSubtypeSeriesConfigDictionaryEntity entity05 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity05.setConfigCode("allowRemoteControlOfLocalCamera");
        entity05.setConfigName("允许远程遥控本地摄像头");
        entity05.setConfigComment("允许远程遥控本地摄像头");
        entity05.setConfigOrder(5);
        entity05.setPId(entity.getId());
        entity05.setDictionaryType("SERIES");
        entity05.setConfigType(ConfigTypeEnum.ITEM.name());
        data.add(entity05);

        DeviceSubtypeSeriesConfigDictionaryEntity entity06 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity06.setConfigCode("showRecordingPrompt");
        entity06.setConfigName("显示录制提示");
        entity06.setConfigComment("显示录制提示");
        entity06.setConfigOrder(6);
        entity06.setPId(entity.getId());
        entity06.setDictionaryType("SERIES");
        entity06.setConfigType(ConfigTypeEnum.ITEM.name());
        data.add(entity06);


        DeviceSubtypeSeriesConfigDictionaryEntity entity11 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity11.setConfigCode("start/stopRecording");
        entity11.setConfigName("开启/停止录制");
        entity11.setConfigComment("开启/停止录制");
        entity11.setConfigOrder(1);
        entity11.setPId(entity1.getId());
        entity11.setDictionaryType("SERIES");
        entity11.setConfigType(ConfigTypeEnum.ITEM.name());
        data.add(entity11);


        DeviceSubtypeSeriesConfigDictionaryEntity entity12 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity12.setConfigCode("meetingManagement");
        entity12.setConfigName("会议管理");
        entity12.setConfigComment("会议管理");
        entity12.setConfigOrder(2);
        entity12.setPId(entity1.getId());
        entity12.setDictionaryType("SERIES");
        entity12.setConfigType(ConfigTypeEnum.ITEM.name());
        data.add(entity12);


        DeviceSubtypeSeriesConfigDictionaryEntity entity13 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity13.setConfigCode("inviteCall");
        entity13.setConfigName("邀请通话");
        entity13.setConfigComment("邀请通话");
        entity13.setConfigOrder(3);
        entity13.setPId(entity1.getId());
        entity13.setDictionaryType("SERIES");
        entity13.setConfigType(ConfigTypeEnum.ITEM.name());
        data.add(entity13);


        DeviceSubtypeSeriesConfigDictionaryEntity entity14 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity14.setConfigCode("start/stopWhiteboard");
        entity14.setConfigName("开启/退出白板");
        entity14.setConfigComment("邀请通话");
        entity14.setConfigOrder(4);
        entity14.setPId(entity1.getId());
        entity14.setDictionaryType("SERIES");
        entity14.setConfigType(ConfigTypeEnum.ITEM.name());
        data.add(entity14);


        DeviceSubtypeSeriesConfigDictionaryEntity entity21 = new DeviceSubtypeSeriesConfigDictionaryEntity();
        entity21.setConfigCode("qRCode");
        entity21.setConfigName("二维码");
        entity21.setConfigComment("二维码");
        entity21.setConfigOrder(1);
        entity21.setPId(entity2.getId());
        entity21.setDictionaryType("SERIES");
        entity21.setConfigType(ConfigTypeEnum.ITEM.name());
        data.add(entity21);
        for (DeviceSubtypeSeriesConfigDictionaryEntity item : data) {
            deviceSubtypeSeriesConfigDictionaryMapper.insert(item);
        }

    }

}
