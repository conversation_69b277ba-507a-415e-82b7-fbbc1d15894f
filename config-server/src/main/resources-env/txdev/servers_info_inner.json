{"accessServer": "wss://testdev.xylink.com", "vodBroker": "", "vodBrokerHost": "https://testdev.xylink.com", "vodPub": "http://dev.xxy123.cn", "vodUpload": "https://testdev.xylink.com", "logServer": "https://txdevlog.xylink.com", "appDownload": "http://www.ainemo.com/app", "officialSite": "http://www.zaijia.com", "bbs": "http://bbs.xxy123.cn", "faq": "", "newFetaure": "", "shopping": "http://www.zaijia.com/buy", "ipeiban": "", "aiUpload": "https://testdev.xylink.com", "serviceLine": "400-8787-905", "albumServerPrefix": "https://testdev.xylink.com/api/rest/v3/albumitem/", "albumServerClearPrefix": "https://testdev.xylink.com/api/rest/v3/clearalbumitem/", "imageUpload": "https://testdev.xylink.com", "opennemos": "", "netTestServer": "{\"name\": \"nettest\", \"ip\": \"**************\", \"port\": 80}", "stunserver": "**************:15000", "nemoConfigPageUrl": "https://testdev.xylink.com/page/nemoconfig/", "nemoConfigHelpPageUrl": "", "pstnOutNumbers": "01056452754", "contentSourceServer": "https://testdev.xylink.com", "semanticServer": "https://testdev.xylink.com", "featurenemo": "", "serviceNumber": "114,10010,10000,10086,95xxx,96xxx,12306,12580,12315,1010xxxx,400xxxxxxx,800xxxxxxx", "nemoPublicnemo": "https://testdev.xylink.com/page/nemo/opennemos/public/", "liveCustomerService": "", "yellowPageUrl": "https://testdev.xylink.com/page/yellowpage/", "cloudMeetingRoom": "https://testdevcdn.xylink.com/noa/minecmr.html", "nemoNumberHelpUrl": "", "tos": "http://www.zaijia.com/forum/tos", "customerServiceNemo": "166166", "nemoPstnChargeUrl": "https://dev.xxy123.cn/console/nemophone/index", "conferenceControlUrl": "https://testdev.xylink.com/page/cloudConf/", "sharingServerUrl": "", "liveEntryUrl": "", "webServerUrl": "https://testdev.xylink.com", "pcclientLiveEntryUrl": "https://testdev.xylink.com/live/liveVideo/pcclient/", "commandClientPrefix": "https://testdevcdn.xylink.com", "matrixApplicationServer": "https://matrixtestdev.xylink.com", "shortConnectUrl": "https://testdev.xylink.com/api/rest/v3/en/vote/shortUrl", "deviceSettingsWebViewPrefix": "https://testdevcdn.xylink.com/settings/"}