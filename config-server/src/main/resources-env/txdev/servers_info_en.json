{"accessServer": "wss://testdev.xylink.com", "vodBroker": "", "vodBrokerHost": "https://testdev.xylink.com", "vodPub": "https://testdev.xylink.com", "vodUpload": "https://testdev.xylink.com", "logServer": "https://txdevlog.xylink.com", "appDownload": "http://testdev.xylink.com/app", "officialSite": "http://*************", "faq": "", "newFetaure": "", "ipeiban": "", "aiUpload": "https://testdev.xylink.com", "serviceLine": "400-8787-905", "albumServerPrefix": "https://testdev.xylink.com/api/rest/v3/albumitem/", "albumServerClearPrefix": "https://testdev.xylink.com/api/rest/v3/clearalbumitem/", "imageUpload": "https://testdev.xylink.com", "opennemos": "", "netTestServer": "{\"name\": \"nettest\", \"ip\": \"**************\", \"port\": 80}", "stunserver": "**************:15000", "nemoConfigPageUrl": "https://testdev.xylink.com/page/nemoconfig/", "nemoConfigHelpPageUrl": "", "pstnOutNumbers": "01056452754", "contentSourceServer": "https://testdev.xylink.com", "semanticServer": "https://testdev.xylink.com", "featurenemo": "", "serviceNumber": "114,10010,10000,10086,95xxx,96xxx,12306,12580,12315,1010xxxx,400xxxxxxx,800xxxxxxx", "nemoPublicnemo": "https://testdev.xylink.com/page/nemo/opennemos/public/", "liveCustomerService": "", "yellowPageUrl": "https://testdev.xylink.com/page/yellowpage/", "cloudMeetingRoom": "https://testdevcdn.xylink.com/noa/minecmr.html", "nemoNumberHelpUrl": "", "tos": "https://testdev.xylink.com/client/agreement", "privacyPolicyUrl": "", "customerServiceNemo": "166166", "nemoPstnChargeUrl": "https://dev.xxy123.cn/console/nemophone/index", "conferenceControlUrl": "https://testdev.xylink.com/page/cloudConf/", "nemoManualUrl": "https://cdn.xylink.com/xiaoyuonline/nemo/manual.html", "liveVideoUrl": "", "sharingServerUrl": "", "liveEntryUrl": "", "shareCenterUrl": "https://testdev.xylink.com/vods/sharecenter/", "pcclientLiveEntryUrl": "https://testdev.xylink.com/live/liveVideo/pcclient/", "pcScheduledMeetingUrl": "https://testdev.xylink.com/meetingSchduler/", "appInviteMeetingUrl": "https://testdev.xylink.com/meetingSchduler/invite/app/", "pcEditScheduledMeetingUrl": "https://testdev.xylink.com/meetingSchduler/edit/", "pcJoinMeetingUrl": "https://testdev.xylink.com/page/client/joinMeeting/", "pcInviteMeetingUrl": "https://testdev.xylink.com/page/client/inviteMeeting/", "pcHostMeetingUrl": "https://testdev.xylink.com/page/client/hostMeeting/", "pcCalendarUrl": "https://testdev.xylink.com/page/client/calendar/", "pcEditLiveUrl": "", "appShareCenterUrl": "https://testdev.xylink.com/vods/sharecenter/app/", "pcMeetingDetailUrl": "https://testdev.xylink.com/meetingSchduler/detail/", "liveWebServer": "https://testdev.xylink.com", "webServerUrl": "https://testdev.xylink.com", "pcContactUrl": "https://testdevcdn.xylink.com/nop/contact.html", "hardwareLiveUrl": "https://testdev.xylink.com/live/liveVideo/nemo", "meetingControl": "https://testdev.xylink.com/page/cloudConf", "clientDownloadUrl": "http://testdev.xylink.com/download", "meetingControlMemebersUrl": "https://testdev.xylink.com/page/cloudConf/members", "commandClientPrefix": "https://testdevcdn.xylink.com", "matrixApplicationServer": "https://matrixtestdev.xylink.com", "shortConnectUrl": "https://testdev.xylink.com/api/rest/v3/en/vote/shortUrl", "deviceSettingsWebViewPrefix": "https://testdevcdn.xylink.com/settings/", "educlient": "https://devcdn.xylink.com/educlient/callnumber/index.html", "eduFolderUrl": "https://devcdn.xylink.com/educlient/folder/index.html", "commandClientPrefixHTTP": "http://testdevcdn.xylink.com", "whiteBoard": "txdevshare.xylink.com", "appResourceUrl": "https://testdevcdn.xylink.com/app/enterprise/resource.html", "appCombinationUrl": "https://testdevcdn.xylink.com/app/enterprise/business-combination.html", "appResourcePayUrl": "https://testdevcdn.xylink.com/app/enterprise/pay.html", "appCrmUrl": "https://testdevcdn.xylink.com/app/crm/index.html", "clientIm": "https://testdevcdn.xylink.com/im_front/index.html", "joinEntQRCodeUrl": "http://qr16.cn/BaWkDO", "appVoucherUrl": "https://testdevcdn.xylink.com/app/voucher/index.html"}