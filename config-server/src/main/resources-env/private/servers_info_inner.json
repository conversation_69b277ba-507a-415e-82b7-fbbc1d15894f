{"accessServer": "wss://dev390.xylink.com", "vodBroker": "", "vodBrokerHost": "http://dev390.xylink.com", "vodPub": "http://dev390.xylink.com", "vodUpload": "http://**************", "logServer": "http://dev390.xylink.com", "appDownload": "http://dev390.xylink.com", "officialSite": "http://dev390.xylink.com", "bbs": "http://dev390.xylink.com", "faq": "", "newFetaure": "", "shopping": "http://dev390.xylink.com/buy", "ipeiban": "", "aiUpload": "", "serviceLine": "", "albumServerPrefix": "https://dev390.xylink.com/api/rest/v3/albumitem/", "albumServerClearPrefix": "https://dev390.xylink.com/api/rest/v3/clearalbumitem/", "imageUpload": "https://dev390.xylink.com", "opennemos": "", "netTestServer": "{\"name\": \"nettest\", \"ip\": \"*************\", \"port\":5008}", "stunserver": "", "nemoConfigPageUrl": "https://dev390.xylink.com/page/nemoconfig/", "nemoConfigHelpPageUrl": "", "pstnOutNumbers": "", "contentSourceServer": "http://dev390.xylink.com", "semanticServer": "http://dev390.xylink.com", "featurenemo": "", "serviceNumber": "", "nemoPublicnemo": "https://dev390.xylink.com/page/nemo/opennemos/public/", "liveCustomerService": "", "yellowPageUrl": "https://dev390.xylink.com/page/yellowpage/", "cloudMeetingRoom": "https://dev390.xylink.com/static_source/noa/minecmr.html", "nemoNumberHelpUrl": "", "tos": "", "customerServiceNemo": "", "nemoPstnChargeUrl": "https://dev390.xylink.com/console/nemophone/index", "conferenceControlUrl": "https://dev390.xylink.com/page/cloudConf/", "sharingServerUrl": "", "liveEntryUrl": "", "webServerUrl": "http://dev390.xylink.com", "pcclientLiveEntryUrl": "https://dev390.xylink.com/live/liveVideo/pcclient/", "commandClientPrefix": "http://dev390.xylink.com/static_source/localcdn", "matrixApplicationServer": "http://dev390.xylink.com", "shortConnectUrl": "http://dev390.xylink.com/api/rest/v3/en/vote/shortUrl", "deviceSettingsWebViewPrefix": "https:///static_source/settings/", "commandClientCurrentUrl": "http://dev390.xylink.com/static_source/localcdn", "whiteBoard": "dev390.xylink.com", "appResourceUrl": "https://cdn.xylink.com/app/enterprise/resource.html", "appCombinationUrl": "https://cdn.xylink.com/app/enterprise/business-combination.html", "appResourcePayUrl": "https://cdn.xylink.com/app/enterprise/pay.html", "appCrmUrl": "https://cdn.xylink.com/app/crm/index.html", "clientIm": "https://cdn.xylink.com/im_front/index.html", "joinEntQRCodeUrl": "https://qr16.cn/BaWkDO", "appVoucherUrl": "https://cdn.xylink.com/app/voucher/index.html", "dispathcerDispath": "", "dispathcerMonitor": "", "dispathcerRefactorSlcd": ""}