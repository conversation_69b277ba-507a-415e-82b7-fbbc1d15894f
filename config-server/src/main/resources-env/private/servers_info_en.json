{"accessServer": "wss://dev390.xylink.com", "vodBroker": "", "vodBrokerHost": "http://dev390.xylink.com", "vodPub": "http://dev390.xylink.com", "vodUpload": "http://**************", "logServer": "http://dev390.xylink.com", "appDownload": "http://dev390.xylink.com/app", "officialSite": "http://dev390.xylink.com", "faq": "", "newFetaure": "", "ipeiban": "", "aiUpload": "", "serviceLine": "", "albumServerPrefix": "https://dev390.xylink.com/api/rest/v3/albumitem/", "albumServerClearPrefix": "https://dev390.xylink.com/api/rest/v3/clearalbumitem/", "imageUpload": "https://dev390.xylink.com", "opennemos": "", "netTestServer": "{\"name\": \"nettest\", \"ip\": \"*************\", \"port\":5008}", "stunserver": "", "nemoConfigPageUrl": "https://dev390.xylink.com/page/nemoconfig/", "nemoConfigHelpPageUrl": "", "pstnOutNumbers": "", "contentSourceServer": "http://dev390.xylink.com", "semanticServer": "http://dev390.xylink.com", "featurenemo": "", "serviceNumber": "", "nemoPublicnemo": "https://dev390.xylink.com/page/nemo/opennemos/public/", "liveCustomerService": "", "yellowPageUrl": "https://dev390.xylink.com/page/yellowpage/", "cloudMeetingRoom": "https://dev390.xylink.com/static_source/noa/minecmr.html", "nemoNumberHelpUrl": "", "tos": "https://www.xiaoyuonline.com/forum/tos", "privacyPolicyUrl": "", "customerServiceNemo": "", "nemoPstnChargeUrl": "https://dev390.xylink.com/console/nemophone/index", "conferenceControlUrl": "https://dev390.xylink.com/page/cloudConf/", "nemoManualUrl": "", "liveVideoUrl": "", "sharingServerUrl": "", "liveEntryUrl": "", "shareCenterUrl": "https://dev390.xylink.com/vods/sharecenter/", "pcclientLiveEntryUrl": "https://dev390.xylink.com/live/liveVideo/pcclient/", "pcScheduledMeetingUrl": "https://dev390.xylink.com/meetingSchduler/", "appInviteMeetingUrl": "https://dev390.xylink.com/meetingSchduler/invite/app/", "pcEditScheduledMeetingUrl": "http://dev390.xylink.com/meetingSchduler/edit/", "pcJoinMeetingUrl": "https://dev390.xylink.com/page/client/joinMeeting/", "pcInviteMeetingUrl": "https://dev390.xylink.com/page/client/inviteMeeting/", "pcHostMeetingUrl": "https://dev390.xylink.com/page/client/hostMeeting/", "pcCalendarUrl": "https://dev390.xylink.com/page/client/calendar/", "pcEditLiveUrl": "", "appShareCenterUrl": "https://dev390.xylink.com/vods/sharecenter/app/", "pcMeetingDetailUrl": "http://dev390.xylink.com/meetingSchduler/detail/", "liveWebServer": "http://dev390.xylink.com", "webServerUrl": "http://dev390.xylink.com", "pcContactUrl": "http://dev390.xylink.com/static_source/nop/contact.html", "hardwareLiveUrl": "https://dev390.xylink.com/live/liveVideo/nemo", "meetingControl": "https://dev390.xylink.com/page/cloudConf", "clientDownloadUrl": "https://dev390.xylink.com/app/", "meetingControlMemebersUrl": "https://dev390.xylink.com/page/cloudConf/members", "commandClientPrefix": "http://dev390.xylink.com/static_source/localcdn", "matrixApplicationServer": "http://dev390.xylink.com", "shortConnectUrl": "http://dev390.xylink.com/api/rest/v3/en/vote/shortUrl", "deviceSettingsWebViewPrefix": "https:///static_source/settings/", "educlient": "http://dev390.xylink.com/static_source/educlient/callnumber/index.html", "eduFolderUrl": "http://dev390.xylink.com/static_source/educlient/folder/index.html", "commandClientPrefixHTTP": "http://cdn.xylink.com", "commandClientCurrentUrl": "http://dev390.xylink.com/static_source/localcdn", "whiteBoard": "dev390.xylink.com", "appResourceUrl": "https://cdn.xylink.com/app/enterprise/resource.html", "appCombinationUrl": "https://cdn.xylink.com/app/enterprise/business-combination.html", "appResourcePayUrl": "https://cdn.xylink.com/app/enterprise/pay.html", "appCrmUrl": "https://cdn.xylink.com/app/crm/index.html", "clientIm": "https://cdn.xylink.com/im_front/index.html", "joinEntQRCodeUrl": "https://qr16.cn/BaWkDO", "appVoucherUrl": "https://cdn.xylink.com/app/voucher/index.html", "dispathcerDispath": "", "dispathcerMonitor": "", "dispathcerRefactorSlcd": ""}