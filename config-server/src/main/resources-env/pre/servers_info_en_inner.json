{"accessServer": "wss://cloud.xylink.com", "vodBroker": "", "vodBrokerHost": "https://vodupload.xylink.com", "vodPub": "https://cloud.xylink.com", "vodUpload": "https://vodupload.xylink.com", "logServer": "https://log.xylink.com", "appDownload": "http://www.xylink.com/download", "officialSite": "http://www.xiaoyuonline.com", "faq": "", "newFetaure": "", "ipeiban": "", "aiUpload": "https://www.ainemo.com", "serviceLine": "400-900-3567", "albumServerPrefix": "https://cloud.xylink.com/api/rest/v3/albumitem/", "albumServerClearPrefix": "https://cloud.xylink.com/api/rest/v3/clearalbumitem/", "imageUpload": "https://cloud.xylink.com", "opennemos": "", "netTestServer": "{\"name\": \"nettest\", \"ip\": \"**************\", \"port\": 80}", "stunserver": "**************:15000", "nemoConfigPageUrl": "https://cloud.xylink.com/page/nemoconfig/", "nemoConfigHelpPageUrl": "", "pstnOutNumbers": "01052598821", "contentSourceServer": "https://cloud.xylink.com", "semanticServer": "https://cloud.xylink.com", "featurenemo": "", "serviceNumber": "114,10010,10000,10086,95xxx,96xxx,12306,12580,12315,1010xxxx,400xxxxxxx,800xxxxxxx", "nemoPublicnemo": "https://cloud.xylink.com/page/nemo/opennemos/public/", "liveCustomerService": "", "yellowPageUrl": "https://cloud.xylink.com/page/yellowpage/", "cloudMeetingRoom": "https://precdn.xylink.com/noa/minecmr.html", "nemoNumberHelpUrl": "", "tos": "https://cloud.xylink.com/client/agreement", "privacyPolicyUrl": "https://cloud.xylink.com/privacy/agreement", "customerServiceNemo": "166166", "nemoPstnChargeUrl": "http://**************/console/nemophone/index", "conferenceControlUrl": "http://cloud.xylink.com/page/cloudConf/", "nemoManualUrl": "https://cdn.xylink.com/xiaoyuonline/nemo/manual.html", "liveVideoUrl": "", "sharingServerUrl": "", "liveEntryUrl": "", "shareCenterUrl": "https://cloud.xylink.com/vods/sharecenter/", "pcclientLiveEntryUrl": "https://cloud.xylink.com/live/liveVideo/pcclient/", "pcScheduledMeetingUrl": "https://cloud.xylink.com/meetingSchduler/", "appInviteMeetingUrl": "https://cloud.xylink.com/meetingSchduler/invite/app/", "pcEditScheduledMeetingUrl": "https://cloud.xylink.com/meetingSchduler/edit/", "pcJoinMeetingUrl": "https://cloud.xylink.com/page/client/joinMeeting/", "pcInviteMeetingUrl": "https://cloud.xylink.com/page/client/inviteMeeting/", "pcHostMeetingUrl": "https://cloud.xylink.com/page/client/hostMeeting/", "pcCalendarUrl": "https://cloud.xylink.com/page/client/calendar/", "pcEditLiveUrl": "", "appShareCenterUrl": "https://cloud.xylink.com/vods/sharecenter/app/", "pcMeetingDetailUrl": "https://cloud.xylink.com/meetingSchduler/detail/", "liveWebServer": "https://cloud.xylink.com", "webServerUrl": "https://cloud.xylink.com", "pcContactUrl": "https://precdn.xylink.com/nop/contact.html", "hardwareLiveUrl": "https://cloud.xylink.com/live/liveVideo/nemo", "meetingControl": "https://cloud.xylink.com/page/cloudConf", "clientDownloadUrl": "http://cloud.xylink.com/download", "meetingControlMemebersUrl": "https://cloud.xylink.com/page/cloudConf/members", "commandClientPrefix": "https://precdn.xylink.com", "matrixApplicationServer": "https://matrix.xylink.com", "shortConnectUrl": "https://cloud.xylink.com/api/rest/v3/en/vote/shortUrl", "deviceSettingsWebViewPrefix": "https://precdn.xylink.com/settings/", "educlient": "https://precdn.xylink.com/educlient/callnumber/index.html", "eduFolderUrl": "https://precdn.xylink.com/educlient/folder/index.html", "commandClientPrefixHTTP": "http://precdn.xylink.com", "whiteBoard": "preshare.xylink.com", "appResourceUrl": "https://precdn.xylink.com/app/enterprise/resource.html", "appCombinationUrl": "https://precdn.xylink.com/app/enterprise/business-combination.html", "appResourcePayUrl": "https://precdn.xylink.com/app/enterprise/pay.html", "appCrmUrl": "https://precdn.xylink.com/app/crm/index.html", "clientIm": "https://precdn.xylink.com/im_front/index.html", "joinEntQRCodeUrl": "http://qr16.cn/BaWkDO", "appVoucherUrl": "https://precdn.xylink.com/app/voucher/index.html"}