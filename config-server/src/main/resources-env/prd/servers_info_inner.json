{"accessServer": "wss://cloud.xylink.com", "vodBroker": "", "vodBrokerHost": "https://vodupload.xylink.com", "vodPub": "http://cloud.xylink.com", "vodUpload": "https://vodupload.xylink.com", "logServer": "https://log.xylink.com", "appDownload": "https://www.ainemo.com/app", "officialSite": "http://www.zaijia.com", "bbs": "http://bbs.zaijia.com", "faq": "", "newFetaure": "", "shopping": "http://www.zaijia.com/buy", "ipeiban": "", "aiUpload": "https://www.ainemo.com", "serviceLine": "400-900-3567", "albumServerPrefix": "https://cloud.xylink.com/api/rest/v3/albumitem/", "albumServerClearPrefix": "https://cloud.xylink.com/api/rest/v3/clearalbumitem/", "imageUpload": "https://cloud.xylink.com", "opennemos": "", "netTestServer": "{\"name\": \"nettest\", \"ip\": \"**************\", \"port\": 80}", "stunserver": "**************:15000", "nemoConfigPageUrl": "https://cloud.xylink.com/page/nemoconfig/", "nemoConfigHelpPageUrl": "", "pstnOutNumbers": "01052598821", "contentSourceServer": "https://cloud.xylink.com", "semanticServer": "https://cloud.xylink.com", "featurenemo": "", "serviceNumber": "114,10010,10000,10086,95xxx,96xxx,12306,12580,12315,1010xxxx,400xxxxxxx,800xxxxxxx", "nemoPublicnemo": "https://cloud.xylink.com/page/nemo/opennemos/public/", "liveCustomerService": "", "yellowPageUrl": "https://cloud.xylink.com/page/yellowpage/", "cloudMeetingRoom": "https://precdn.xylink.com/noa/minecmr.html", "nemoNumberHelpUrl": "", "tos": "http://www.zaijia.com/forum/tos", "customerServiceNemo": "166166", "nemoPstnChargeUrl": "http://**************/console/nemophone/index", "conferenceControlUrl": "https://cloud.xylink.com/page/cloudConf/", "sharingServerUrl": "", "liveEntryUrl": "", "webServerUrl": "https://cloud.xylink.com", "pcclientLiveEntryUrl": "https://cloud.xylink.com/live/liveVideo/pcclient/", "commandClientPrefix": "https://precdn.xylink.com", "matrixApplicationServer": "https://matrix.xylink.com", "shortConnectUrl": "https://cloud.xylink.com/api/rest/v3/en/vote/shortUrl", "deviceSettingsWebViewPrefix": "https://precdn.xylink.com/settings/"}