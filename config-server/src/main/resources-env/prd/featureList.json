{
  "features":
  [
    {
      "featureName": "digitalmic",
      "softVersion": "0.0.0",
      "hardVersion": "1.2.2",
      "model": "NV1001",
      "os": "0.0.0",
      "reboot": false
    },
    {
      "featureName": "networktool",
      "softVersion": "0.0.0",
      "hardVersion": "0.0.0",
      "model": "NV1001",
      "os": "0.0.0",
      "reboot": false
    },
    {
      "featureName": "advancedFeature",
      "softVersion": "0.0.0",
      "hardVersion": "0.0.0",
      "model": "NV1001",
      "os": "0.0.0",
      "reboot": false,
      "snList": "2D15030901C99C36"
    },
    {
      "featureName": "enableIce",
      "softVersion": "0.0.0",
      "hardVersion": "0.0.0",
      "model": "NV1001",
      "os": "0.0.0",
      "reboot": false,
      "snList": "nobody"
    },
    {
      "featureName": "flowControl",
      "softVersion": "0.0.0",
      "hardVersion": "0.0.0",
      "model": "",
      "type" : "all",
      "os": "0.0.0",
      "reboot": false,
      "snList": ""
    },
    {
      "featureName": "changeUseMode",
      "softVersion": "0.0.0",
      "hardVersion": "0.0.0",
      "model": "NV1001",
      "os": "0.0.0",
      "reboot": false,
      "snList": ""
    },
    {
      "featureName": "enableAudioTest",
      "softVersion": "0.0.0",
      "hardVersion": "0.0.0",
      "model": "NV1001",
      "os": "0.0.0",
      "reboot": false,
      "snList": ""
    },
    {
      "featureName": "embedAEC",
      "softVersion": "0.0.0",
      "hardVersion": "0.0.0",
      "model": "samsung:SM-G900; HUAWEI:H60-L01; HUAWEI:HUAWEI NXT-AL10; DATANG:DATANG T98; HUAWEI:HUAWEI ALE-CL00; vivo:vivo X6SA; HUAWEI:HUAWEI ATH-TL00H",
      "os": "0.0.0",
      "type" : "app",
      "reboot": false,
      "value" : "true"
    },
    {
      "featureName": "audioSource",
      "softVersion": "0.0.0",
      "hardVersion": "0.0.0",
      "model": "samsung:SM-G900; smartisan:SM705; motorola:XT1085; HUAWEI:H60-L01; Xiaomi:MI 4LTE; HUAWEI:HUAWEI NXT-AL10; DATANG:DATANG T98; HUAWEI:HUAWEI ALE-C100; vivo:vivo X6SA; HUAWEI:HUAWEI ATH-TL00H; Xiaomi:HM NOTE 1LTE; samsung:
      SM-G9350",
      "os": "0.0.0",
      "type" : "app",
      "reboot": false,
      "value" : "7"
    },
    {
      "featureName": "outDRCGain",
      "softVersion": "0.0.0",
      "hardVersion": "0.0.0",
      "model": "DATANG:DATANG T98",
      "os": "0.0.0",
      "type" : "app",
      "reboot": false,
      "value" : "0.125f"
    },
    {
      "featureName": "enableMultiPart720p",
      "softVersion": "0.0.0",
      "hardVersion": "0.0.0",
      "model": "NV1001",
      "os": "0.0.0",
      "reboot": false,
      "snList": ""
    },
    {
      "featureName": "enableDoubleStream",
      "softVersion": "0.0.0",
      "hardVersion": "0.0.0",
      "model": "",
      "os": "0.0.0",
      "type": "all",
      "reboot": false,
      "snList": ""
    },
    {
      "featureName": "audio_spkpos",
      "softVersion": "0.0.0",
      "hardVersion": "0.0.0",
      "model": "Apple Inc.:MacBook8,1; Apple Inc.:MacBookPro10,1; Apple Inc.:MacBookPro11,4; Apple Inc.:MacBookPro12,1; Apple Inc.:MacBookAir7,2",
      "os": "0.0.0",
      "type" : "pc",
      "reboot": false,
      "value" : "front_right"
    }
  ]
}