package com.xylink.configserver.enums;

/**
 * <AUTHOR>
 * @since 2021/1/9 11:03 上午
 */
public enum ConfigValueType {
    BOOLEAN("Boolean"),
    INTEGER("Integer"),
    LONG("Long"),
    STRING("String"),
    DOUBLE("Double"),
    ARRAY("Array"),
    JSON("Json");

    private final String value;

    ConfigValueType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

}
