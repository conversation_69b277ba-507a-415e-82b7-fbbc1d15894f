package com.xylink.configserver.enums;

public enum Edition {
    ANDROID,
    GAE,
    GWT,
    JEE,
    JSE,
    OSGI;

    public static final Edition CURRENT = JEE;

    private Edition() {
    }

    public String getFullName() {
        switch(this) {
            case ANDROID:
                return "Android";
            case GAE:
                return "Google App Engine";
            case GWT:
                return "Google Web Toolkit";
            case JEE:
                return "Java Enterprise Edition";
            case JSE:
                return "Java Standard Edition";
            case OSGI:
                return "OSGi";
            default:
                return null;
        }
    }

    public String getMediumName() {
        switch(this) {
            case ANDROID:
                return "Android";
            case GAE:
                return "GAE";
            case GWT:
                return "GWT";
            case JEE:
                return "Java EE";
            case JSE:
                return "Java SE";
            case OSGI:
                return "OSGi";
            default:
                return null;
        }
    }

    public String getShortName() {
        switch(this) {
            case ANDROID:
                return "Android";
            case GAE:
                return "GAE";
            case GWT:
                return "GWT";
            case JEE:
                return "JEE";
            case JSE:
                return "JSE";
            case OSGI:
                return "OSGi";
            default:
                return null;
        }
    }
}
