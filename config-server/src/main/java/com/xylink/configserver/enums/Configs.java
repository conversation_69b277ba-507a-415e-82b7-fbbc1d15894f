package com.xylink.configserver.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.*;

public interface Configs {
    Set<String> configsWithClientName = Collections.unmodifiableSet(new HashSet<String>() {
        {
            this.add("mediaConfig");
            this.add("RemoteSDKConfig");
            this.add("audioConfig");
        }
    });
    Set<String> DEVICE_4K_CONFIG_NAME = Collections.unmodifiableSet(new HashSet<String>() {
        {
            this.add(SHOW_4K_RESOLUTION);
            this.add(ENABLE_4K_RESOLUTION);
        }
    });
    Set<String> DEVICE_4K_RELATED_CONFIG = Collections.unmodifiableSet(new HashSet<String>() {
        {
            this.add(NemoConfig.UI_DISPLAY_CUSTOMIZATION);
            this.add(NemoConfig.MEDIA_CONFIG);
            this.add(NemoConfig.REMOTE_SDK_CONFIG);
            this.add(ENABLE_4K_RESOLUTION);
        }
    });
    String COMMON_CONFIG_KEY = "common";


    String SMART_MUTE_PERSON_COUNT = "smartMutePerson";
    String CHIEF_DEVICE = "chiefDeviceId";
    String PRESENTER_PASSWORD = "presenterPassword";
    String AUTO_RECORD = "autoRecord";
    String AUTO_HANGUP = "autoHangup";
    String SHOW_RECORD_TIP = "showRecordTip";
    String HIGH_RESOLUTION_RECORD = "hrRecord";
    String ONLY_MAIN_IMAGE = "onlyMainImage";
    String ADD_DEVICE_NAME = "addDeviceName";
    String SHOW_4K_RESOLUTION = "show4kResolution";
    String ENABLE_4K_RESOLUTION = "enable4kResolution";

    static boolean check(String key) {
        if (StringUtils.isBlank(key)) {
            return false;
        }
        switch (key) {
            case SMART_MUTE_PERSON_COUNT:
            case CHIEF_DEVICE:
            case PRESENTER_PASSWORD:
            case AUTO_RECORD:
            case AUTO_HANGUP:
            case SHOW_RECORD_TIP:
            case HIGH_RESOLUTION_RECORD:
            case ONLY_MAIN_IMAGE:
            case ADD_DEVICE_NAME:
                return true;
            default:
                return false;
        }
    };


    public interface H5Config {
        String REPLACE_XIAOYU_STRING = "replaceXiaoyuString";
        String HIDE_MEETING_SHARE = "hideMeetingShare";
        String HIDE_VOD_SHARE = "hideVodShare";
        String SMS_ACTIVICATION = "smsActivation";
        String SMS_LOCK = "smsLock";
        String APP_ROOM_CONFIG_HIDE_DESC = "appRoomConfigHideDesc";
        String CLIENT_DOWNLOAD_URL = "clientDownloadUrl";
        String OFFICAL_SITE = "officalSite";
        String EMAIL_LOGO = "emailLogo";
        String ENTERPRISE_NAME = "enterpriseName";
    }

    public static enum UIDisplay {
        SELECT_LANGUAGE("selectLanguage", "选择语言"),
        BIG_SCREEN("bigScreen", "大屏投放"),
        NEMO_NUMBER_CALL("nemoNumberCall", "小鱼号呼叫"),
        BIND_ADMINISTRATOR_PHONE("bindAdministratorPhone", "关联管理员手机号"),
        AUTO_ANSWER("autoAnswer", "自动应答"),
        WATCH_PERMISSION("watchPermission", "观看权限"),
        IMAGE_CAROUSEL_INTERVAL("imageCarouselInternal", "图片轮播间隔"),
        FACE_TRACK("faceTrack", "人脸自动跟踪"),
        VOICE_REMINDER("voiceReminder", "语音智能提醒"),
        AUTO_VIDEO_INCALL("autoVideoInCall", "通话中自动开启视频"),
        SERVER_ADDRESS("serverAddress", "服务器地址"),
        USE_MODE("useMode", "使用模式"),
        MIC_TEST("micTest", "麦克定位测试"),
        AUTO_RECORD("autoRecord", "自动录制"),
        WATCH_RING("watchRing", "回家提醒"),
        ENTERPRISE_ADDRESS("enterpriseAddress", "企业通讯录"),
        NOTIFICATION_CENTER("notificationCenter", "消息通知中心，默认腾讯通的小鱼是true，其他小鱼都是false"),
        USER_MANUAL("manual", "用户手册"),
        NEMO_NUMBER_CALL_PASSWORD("setNemoNumberCallPassword", "呼叫小鱼号密码设置"),
        SHOW_PROJECTION_INCALL("showProjectionInCall", "是否在通话中显示投屏按钮"),
        SHOW_HEALTHDATA_INCALL("showHealthDataInCall", "通话中显示健康数据按钮"),
        SHOW_ENABLE_FECC("showEnableFecc", "FECC开关"),
        SHOW_ENABLE_MIC("showEnableMic", "麦克风开关"),
        SHOW_ENABLE_SPEAKER("showEnableSpeaker", "本地扬声器开关"),
        SHOW_ENABLE_IDLE_POWER_SAVING("showEnableIdlePowerSaving", "待机省电开关"),
        SHOW_AUDIO_TEST("showAudioTest", "音频测试"),
        SHOW_CONTACTS_ON_HOME("showContactsOnHome", "主界面显示联系人"),
        SHOW_CONTACT_OBSERVER_ONLY("showContactObserverOnly", "家庭圈成员只能观看"),
        SHOW_OBSERVER_AUTO_MUTE("showObserverAutoMute", "观看时自动静音"),
        SHOW_DISABLE_REMINDER("showDisableReminder", "通话中禁止振铃"),
        SHOW_DISABLE_RECORD_MONITOR("showDisableRecordMonitor", "观看时禁止录制"),
        SHOW_VIDEO_PROJECTION_DLG("showVideoProjectionDlg", "视频投大屏时，是否显示确认对话框"),
        SHOW_MANAGER_ON_HOME("showNemoManagerOnHome", "主界面显示管理员"),
        SHOW_BIND_CODE("showBindQRCode", "显示绑定的二维码"),
        SHOW_DISPLAY_NAME("showDisplayName", "显示设备名称(关于)"),
        SHOW_INVITE_INCALL("showInviteInCall", "通话中加人"),
        SETTING_ITEM_CONTACT_ONHOME("settingItemContactOnHome", "是否显示'主界面联系人'的选项");

        private String key;
        private String desc;

        private UIDisplay(String key, String desc) {
            this.key = key;
            this.desc = desc;
        }

        public String getKey() {
            return this.key;
        }

        public String getDesc() {
            return this.desc;
        }

        public static Map<String, String> toMap() {
            Map<String, String> map = new HashMap();
            Configs.UIDisplay[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                Configs.UIDisplay uiDisplay = var1[var3];
                map.put(uiDisplay.key, uiDisplay.desc);
            }

            return map;
        }
    }

    public interface UserConfigOnNemo {
        String RECEIVE_CALL_NEMO_NOTIFICATION = "receiveCallNemoNotification";
        String RECEIVE_WATCH_NEMO_NOTIFICATION = "receiveWatchNemoNotification";
        String HAS_OBSERVER_PERMISSION = "hasObserverPermission";
    }

    public static enum AutoPlayConfigValue {
        ON(0),
        ONLY_WIFI(1),
        OFF(2);

        private int value;

        private AutoPlayConfigValue(int value) {
            this.value = value;
        }

        public int getValue() {
            return this.value;
        }
    }

    public interface UserConfig {
        String AUTO_PLAY_CONFIG = "autoPlayConfig";
        String ENABLE_LOW_QUALITY = "enableLowCharge";
        String STORAGE_MINUTES = "storageInMinutes";
        String VOD_DOWNLOADABLE = "vodDownloadable";
        String WATER_MARK_CLEARABLE = "watermarkClearable";
        String ENABLE_HR_RECORD = "enableHrRecord";
        String ENABLE_CREATE_MEETING = "enableCreateMeeting";
        String ENABLE_CREATE_LIVE = "enableCreateLive";
        String SHOW_HOST_MEETING = "showHostMeeting";
        String SHOW_INVITE_MEETING = "showInviteMeeting";
        String SHOW_SHARED_FOLDER = "showSharedFolder";
        String SHOW_ATTENDEE = "showAttendee";
        String SHOW_RECORDING= "showRecording";
        String SHOW_CLOUD_RECORDING = "showCloudRecording";
        String SHOW_LOCAL_RECORDING = "showLocalRecording";
    }

    public static enum AutoAnswerValue {
        NO("0"),
        ONLY_CONTACT("2"),
        ALL("1");

        private String value;

        private AutoAnswerValue(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }

        public static Configs.AutoAnswerValue fromValue(String value) {
            Configs.AutoAnswerValue[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                Configs.AutoAnswerValue autoAnswerValue = var1[var3];
                if (autoAnswerValue.getValue().equals(value)) {
                    return autoAnswerValue;
                }
            }

            return null;
        }
    }

    public static enum AlbumCarsouelInterval {
        ANYTIME("0", "随时"),
        MIN20("1", "20"),
        MIN40("2", "40"),
        MIN60("3", "60"),
        NEVER("4", "从不");

        private String value;
        private String desc;

        private AlbumCarsouelInterval(String value, String des) {
            this.value = value;
            this.desc = des;
        }

        public String getValue() {
            return this.value;
        }

        public String getDesc() {
            return this.desc;
        }

        public static Configs.AlbumCarsouelInterval fromValue(String value) {
            Configs.AlbumCarsouelInterval[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                Configs.AlbumCarsouelInterval interval = var1[var3];
                if (interval.value.equals(value)) {
                    return interval;
                }
            }

            return MIN20;
        }

        public Map<String, String> toValueMap() {
            Map<String, String> ret = new HashMap();
            ret.put("value", this.value);
            ret.put("desc", this.desc);
            return ret;
        }
    }

    public static enum NemoUseMode {
        FAMILY("0"),
        ENTERPRISE("1");

        private String value;

        private NemoUseMode(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }

        public static boolean isEnterpriseNemo(String value) {
            Configs.NemoUseMode[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                Configs.NemoUseMode mode = var1[var3];
                if (mode.getValue().equals(value) && mode == ENTERPRISE) {
                    return true;
                }
            }

            return false;
        }
    }

    public static enum NemoType {
        PRIVATE("0"),
        EXPERIENCE("1"),
        PUBLIC("2");

        private String value;

        private NemoType(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }

        public static Configs.NemoType fromValue(String value) {
            Configs.NemoType[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                Configs.NemoType v = var1[var3];
                if (v.getValue().equals(value.trim())) {
                    return v;
                }
            }

            return null;
        }

        public static boolean isOpenNemo(String type) {
            return PUBLIC.getValue().equals(type);
        }
    }

    public static enum AllowStrangerAutoAnswer {
        NO("false"),
        ALLOW("true");

        private String value;

        private AllowStrangerAutoAnswer(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }

    public static enum AutoRecordConfigValue {
        ONLY_CHILDREN(0),
        ALL(1),
        OFF(2);

        private int value;

        private AutoRecordConfigValue(int value) {
            this.value = value;
        }

        public int getValue() {
            return this.value;
        }

        public static Configs.AutoRecordConfigValue fromValue(int value) {
            Configs.AutoRecordConfigValue[] var1 = values();
            int var2 = var1.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                Configs.AutoRecordConfigValue c = var1[var3];
                if (c.getValue() == value) {
                    return c;
                }
            }

            return ALL;
        }
    }

    public interface NemoConfig {
        String AUTO_RECORD_CONFIG = "enableAutoRecord";
        String FACE_TRACK_CONFIG = "enableFaceTrack";
        String WATCH_RING_CONFIG = "enableWatchRing";
        String NUM_IN_CIRCLE = "numInCircle";
        String NUM_IN_MULTI_CALL = "numInMultipleCall";
        String AUTO_RECORD = "autoRecord";
        String DIGITAL_MIC = "digitalmic";
        String NETWORK_TOOL = "networktool";
        String ADVANCED_FEATURE = "advancedFeature";
        String ALLOW_STRANGER = "allowStranger";
        String NEMO_TYPE = "nemoType";
        String ALLOW_STRANGER_AUTO_ANSWER = "allowStrangerAutoAnswer";
        String PAY_URL = "payUrl";
        String TRIAL_NEMO_TEXT = "trialExpirationText";
        String ICE_CONFIG = "enableIce";
        String TIME_AND_CITY = "timeAndCity";
        String NEMO_AVATAR = "nemoAvatar";
        String OPEN_NEMO_CONFIG = "openNemoConfig";
        String ENABLE_ALBUM_CAROUSEL = "enableAlbumCarousel";
        String XINTONG_AUTORECORD = "autoMeetingRecord";
        String XINTONG_RECORD_RESOLUTION = "autoMeetingRecordResolution";
        String NEMO_USE_MODE = "useMode";
        String CONTACT_AUTO_ANSWER = "contactAutoAnswer";
        String HTTP_REDIRECT = "httpRedirect";
        String ADD_CONTACT_INCALL = "addContactInCall";
        String ALBUM_CAROUSEL_INTERVAL = "albumCarouselInterval";
        String RECORDING_INCALL = "recordingInCall";
        String VIDEO_MUTE_INCALL = "videoMuteInCall";
        String CHANGE_USE_MODE = "changeUseMode";
        String ADD_NEMO_AFTER_CALL = "addNemoAfterCall";
        String ENABLE_VOICE_REMINDER = "enableVoiceReminder";
        String SUPPORT_SMART_HOMEAPPS = "supportSmartHomeApps";
        String NEMO_RELATED_USER_ID = "associatedUserId";
        String SPECIAL_CONTACTS = "specialContacts";
        String ENABLE_TV_DLNA = "enableLeTVDlna";
        String LETV_SUPPORT_VERSION = "leTVSupportVersion";
        String ENABLE_WHITE_BOARD = "enableWhiteboard";
        String ENABLE_MEETING_CONTROL = "enableMeetingControl";
        String UI_DISPLAY_CUSTOMIZATION = "UIDisplayCustomization";
        String CUSTOMER_SERVICE_NUMBER = "customerServicePhone";
        String CUSTOMER_SERVICE_NEMO = "customerServiceNemo";
        String NEMONUMBER_CALL_PASSWORD = "nemoNumberCallPassword";
        String LETV_SUPPORT = "letvSupport";
        String CVTOUCH_SUPPORT = "cvtouchSupport";
        String ENABLE_FECC = "enableFecc";
        String ENABLE_MIC = "enableMic";
        String ENABLE_SPEAKER = "enableSpeaker";
        String ENABLE_IDEL_POWER_SAVING = "enableIdlePowerSaving";
        String ENTERPRISE_AUTO_ANSWER = "autoAnswer";
        String CALLED_PAY = "calleePay";
        String CONTACT_OBSERVER_ONLY = "contactObserverOnly";
        String OBSERVER_AUTO_MUTE = "observerAutoMute";
        String DISABLE_REMINDER_IN_CALL = "disableReminderIncall";
        String DISABLE_RECORDING_WHEN_MONITOR = "disableRecordWhenMonitor";
        String THIRD_APP_ENABLE_AUDIO = "thirdAppsEnableAudio";
        String THIRD_APP_ENABLE_VIDEO = "thirdAppEnableVideo";
        String THIRD_APP_BACKGROUND_RUN = "thirdAppBackgroundRun";
        String SEND_INTELLIGENCE_DATA = "enableIntelligenceDataSend";
        String NEMO_NUMBER_CALL = "allowNemoNumberCall";
        String MEDIA_CONFIG = "mediaConfig";
        String AUDIO_CONFIG = "audioConfig";
        String REMOTE_SDK_CONFIG = "RemoteSDKConfig";
        String AUTO_AV_ON_INCALL = "autoAVOnInCall";
        String CAROUSEL_IMAGES = "carouselImages";
        String ENABLE_LIVE = "enableLive";
        String ENABLE_SHARECENTER = "enableShareCenter";
        String OFFICIAL_SITE = "officialSet";
        String NO_DISTRUB_INCALL = "noDisturbIncall";
        String ENTERPRISE_LOGO = "enterpriseLogo";
        String SPLASH_PICTURE = "splashPicture";
    }
}
