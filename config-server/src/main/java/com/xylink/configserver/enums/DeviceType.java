package com.xylink.configserver.enums;

public enum DeviceType {
    SOFT(1),
    HARD(2),
    BROWSER(3),
    TEL(4),
    DESKTOP(5),
    GW_H323(6),
    B<PERSON>_ENDPOINT_DEVICE(7),
    TVBOX(8),
    SHUTTLE(9),
    WECHA<PERSON>(10),
    GW_H323_MNG(11),
    DIRECT_DISPATCH(12),
    PSTN(13),
    GW_H323_HYGW(14),
    GW_PSTN_MNG(15),
    ASSISTANT(16),
    WEBRTC(17),
    WECHATOFFICIALACCOUNTS(18);

    private int value;

    private DeviceType(int type) {
        this.value = type;
    }

    public int getValue() {
        return this.value;
    }

    public static DeviceType valueOf(int valueInt) {
        DeviceType[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            DeviceType type = var1[var3];
            if (type.value == valueInt) {
                return type;
            }
        }

        return null;
    }

    public boolean isHardDevice() {
        return isHardDevice(this.value);
    }

    public static boolean isHardDevice(int valueInt) {
        return valueInt == HARD.value || valueInt == GW_H323.value || valueInt == BIG_ENDPOINT_DEVICE.value || valueInt == TVBOX.value || valueInt == SHUTTLE.value;
    }

    public static boolean isHardDeviceExceptBigEndpoint(int valueInt) {
        return valueInt == HARD.value || valueInt == GW_H323.value || valueInt == TVBOX.value || valueInt == SHUTTLE.value;
    }

    public static String asUri(String id, int dt) {
        if (dt >= 5 && dt != 8 && dt != 17) {
            if (dt == 5) {
                return id + "@DESK";
            } else if (dt == 7) {
                return id + "@BRUCE";
            } else if (dt == 6) {
                return id + "@H323";
            } else {
                return dt == 11 ? id + "@GW_H323_MNG" : id + "@" + dt;
            }
        } else {
            return id + "@" + valueOf(dt).name();
        }
    }
}
