package com.xylink.configserver.enums;

import com.xylink.configserver.data.model.GwDevice;
import com.xylink.configserver.data.model.GwManager;

public enum GateWayRequestTypeEnum {
    /**
     * 1-h323网关
     */
    H323(1),
    /**
     * 2-pstn网关manager
     */
    PSTNGWM(2),
    /**
     * 3-红云网关
     */
    HYWG(3);

    private int value;

    public static boolean supportType(GwDevice.GwType gwType){
        return GwDevice.GwType.H323.equals(gwType) || GwDevice.GwType.HYGW.equals(gwType);
    }

    public static boolean supportType(GwManager.GwMangerType gwMangerType) {
        return GwManager.GwMangerType.PSTNGWM.equals(gwMangerType);
    }

    public static int mapped(GwDevice.GwType gwType) {
        int type;
        switch (gwType) {
            case H323:
                type = GateWayRequestTypeEnum.H323.getValue();
                break;
            case HYGW:
                type = GateWayRequestTypeEnum.HYWG.getValue();
                break;
            default:
                throw new IllegalArgumentException("not found gwType[" + gwType.name() + "] mapped in GateWayRequestTypeEnum");
        }
        return type;
    }

    public static int mapped(GwManager.GwMangerType gwMangerType) {
        int type;
        switch (gwMangerType) {
            case PSTNGWM:
                type = GateWayRequestTypeEnum.PSTNGWM.getValue();
                break;
            default:
                throw new IllegalArgumentException("not found gwMangerType[" + gwMangerType.name() + "] mapped in GateWayRequestTypeEnum");
        }
        return type;
    }

    GateWayRequestTypeEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}
