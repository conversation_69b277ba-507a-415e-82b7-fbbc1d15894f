package com.xylink.configserver.enums;

public enum ErrorStatus {
    SUCCESS(1, "success"),
    FAIL(10000, "fail"),
    INTERNAL_ERROR(700, "internal.error"),
    INTERNAL_DATABASE_ERROR(800, "internal.database.error"),
    INVALID_PARAMETER(1001, "invalid.parameter"),
    LOGIN_INVALID_ACCOUNT_OR_PASSWORD(2001, "login.invalid.account.psw"),
    LOGIN_ACCOUNT_NOT_REGISTERED(2002, "validate.user.not.registered"),
    LOGIN_ACCOUNT_NOT_ACTIVATED(2003, "validate.user.not.actived"),
    LOGIN_ACCOUNT_EMPTY(2004, "validate.empty.account"),
    LOGIN_ACCOUNT_INVALID_LENGTH(2005, "validate.email.tooLong"),
    LOGIN_EMPTY_DVICE_SN(2006, "login.empty.device_sn"),
    LOGIN_DEVICE_NOT_BIND(2007, "login.device.not.bind"),
    LOGIN_INVALID_DEVICE(2008, "invalid_device_sn"),
    LOGIN_INVALID_MANY_PHONE(2009, "invalid_PHONE_TOO_MANY"),
    LOGIN_INVALID_SECURITY_KEY(2010, "validate.relogin"),
    LOGIN_OUT_OF_DATE(2011, "validate.relogin"),
    INVALID_ACCOUNT(2012, "invalid.account"),
    INVALID_PASSWORD(2013, "invalid.password"),
    INVALID_NEW_PASSWORD(2014, "invalid.new.password"),
    LOGIN_ENTERPRISE_NEMO_NOT_BIND(2015, "login.enterprise.device.not.bind"),
    LOGIN_ACCOUNT_IN_BLACK_LIST(2016, "login.account.in.blacklist"),
    LOGIN_INVALID_DEVICE_TYPE(2017, "invalid.device_type"),
    LOGIN_ACCOUNT_NOT_IN_ENTERPRISE(2018, "login.account.not.in.enterprise"),
    LOGIN_TVBOX_INVALID_MODEL(2019, "invalid.device.model"),
    Device_NOT_TRUSTED(2020, "device.not.trusted"),
    LOGIN_ACCOUNT_MAIL(2021, "mail.not.validate"),
    LOGIN_SOFT_ENDPOINT(2022, "machine code is null"),
    LOGIN_INVALID_DEVICE_NOT_ACTIVITED(2023, "invalid_device_not_activited"),
    LOGIN_LICENSE_MAX_COUNT_EXCEED(2024, "login.license.max.count.exceed"),
    LOGIN_PERMISSION_EXPIRED(2025, "login.permission.expired"),
    LOGIN_NO_PERMISSION_LEFT(2026, "login.no.permission.left"),
    LOGIN_ON_OTHER_DEVICE(2027, "login.on.other.device"),
    LOGIN_ENTERPRISE_NO_PERMISSION(2028, "login.enterprise.no.permission"),
    VALIDATE_ACCOUNT_IN_BLACK_LIST(2029, "Too much attempts of wrong password, you are not allowed to join the call for now. Please wait for %s seconds to retry"),
    LOGIN_ACCOUNT_PASSWORD_EXPIRED(2040, "login.account.password.expired"),
    LOGIN_NO_WEBRTC_ACCOUNT_AVAILABLE(2041, "login.no.webrtc.account.available"),
    LOGIN_EMAIL_ALREADY_REGISTERED(2100, "validate.email.already.registered"),
    INVALID_PHONE_FORMAT(2030, "validate.phone.wrong"),
    INVALID_PHONE_REGISTERING(2031, "validate.phone.registering"),
    INVALID_PHONE_USING(2032, "validate.phone.using"),
    INVALID_ACTIVATION_CODE_FORMAT(2033, "validate.phone.activationcode.wrong"),
    INVALID_ACTIVATION_CODE(2034, "validate.phone.activationcode.wrong"),
    INVALID_VERIFICATION_CODE_REQUEST_TOO_FREQUENTLY(2035, "validate.verification.request.toofrequent"),
    INVALID_PHONE_NOT_REGISTERED(2036, "validate.phone.not.registered"),
    BINDCODE_ERROR(2037, "bindcode.error"),
    BINDCODE_OUT_OF_DATE(2038, "bindcode out of date"),
    INVALID_NO_ENTERPRISE_PROFILE(2039, "validate.phone.no.enterprise"),
    INVALID_PHONE_FORMAT_TAIWAN(2050, "validate.phone.wrong.taiwan.no.leading.zero"),
    INVALID_SN_NUM(2087, "sn not exist"),
    INVALID_ORDER_NUM(2088, "order not exist"),
    INVALID_HASHID(2089, "hashid not exist"),
    INVALID_NO_FRIEND(2060, "validate.no_friend"),
    INVALID_NOT_VALID_FRIEND(2061, "validate.invalid_friend"),
    INVALID_NOT_VALID_FRIEND_REQ(2062, "validate.invalid_friend_req"),
    LOGIN_ACCOUNT_RATE_LIMIT(2069, "login.account.rate.limit"),
    INVALID_NOT_YOURSELF(2090, "validate.not_you"),
    INVALID_IDENTIFIER(2070, "validate.no.identifier"),
    INVALID_IP(2101, "validate.invalid_ip"),
    INVALID_CANCEL_ENHANCED_PASSWORD(2110, "not support cancel enhanced.password"),
    DUPLICATE_ENTERPRISE_ISOLATION_DOMAIN(2201, "duplicate.enterprise.isolation.domain"),
    USER_NO_ONLINE_DEVICE(3010, "no.online.device"),
    USER_ALREADY_FRIEND(3060, "user.already_friend"),
    USER_INVALID_FRIEND_ID(3061, "user.not.exist"),
    USER_INVALID_PROFILE_FIELD_NAME(3062, "user.profile.invalid.field.name"),
    USER_INVALID_PROFILE_FIELD_VALUE(3063, "user.profile.invalid.field.value"),
    USER_IN_OTHER_ENTERPRISE(3064, "user.in.other.enterprise"),
    USER_APPLIED_JOIN_ENTERPRISE(3065, "user.applied.join.enterprise"),
    USER_APPLIED_JOIN_ENTERPRISE_REFUSED(3066, "user.applied.join.enterprise.refused"),
    USER_APPLIED_JOIN_ENTERPRISE_AGREED(3067, "user.applied.join.enterprise.agreed"),
    USER_APPLIED_JOIN_ENTERPRISE_INVALID(3068, "user.applied.join.enterprise.invalid"),
    ENTERPRISE_FORBID_CHANGE_NAME(3069, "enterprise.forbid.change.name"),
    USER_ALREADY_REGISTERED(3100, "user.already.registered"),
    USER_INVALID_ENHANCED_PASSWORD(3080, "user.configure.invalid.enhanced.password"),
    USER_IS_SUPER_ADMIN(3101, "user.is.super.admin"),
    VOD_ALREADY_IN_FAVORITE(3900, "vod.already.favorite"),
    VOD_FORBIDDEN(3901, "not.vod.owner"),
    VOD_NO_PERMISSION(3902, "not.vod.ownerOrManager"),
    STORAGE_NO_ENTERPRISE(3903, "storage.no.enterpirse"),
    STORAGE_NO_SPACE(3904, "storage.no.space"),
    ENTERPRISE_SHARECENTER_NAMED_USED(3905, "name.already.used"),
    VOD_DISPLAYNAME_CONFLICT(3906, "vod.displayname.conflict"),
    VOD_DISPLAYNAME_INVALID(3907, "vod.displayname.invalid"),
    DEVICE_ALREADY_BINDED(4001, "device.already.bind"),
    DEVICE_BINDED_OHTER_USER(4002, "device.binded.other"),
    DEVICE_NOT_BINDED(4003, "device.not.bind"),
    DEVICE_IN_BLACKLIST(4100, "device.register.deny"),
    DEVICE_CHANGE_PASSWORD_DENY(4101, "device.change.password.deny"),
    DEVICE_REGISTER_DENY(4102, "device.register.deny"),
    DEVICE_WRONG_PASSWORD_DENY(4103, "device.input.wrong.password.deny"),
    DEVICE_WRONG_PASSWORD_PROMPT(4104, "device.input.wrong.password.prompt"),
    DEVICE_BIND_DENY(4105, "device.bind.deny"),
    PHONE_VERIFY_DENY(4106, "phone.verify.deny"),
    OPEN_NEMO_CHANGE_ALLOWSTRANGER_DENY(4107, "change.allowstranger.deny"),
    DEVICE_INVALID_USEMODE(4108, "device.invalid.usemode"),
    DEVICE_IN_OTHER_ENTERPRISE(4109, "device.in.other.enterprise"),
    INVALID_DEVICE_SUBTYPE(4110, "invalid.device.subtype"),
    DEVICE_NOT_FOUND(4111, "no.device.found"),
    DEVICE_NO_MACHINE(4112, "no.machine.found"),
    DEVICE_NOT_RECHARGED_4K(4113, "device.not.recharged.4k"),
    DEVICE_CAN_NOT_OPERATE_4K(4114, "device.can.not.operate.4k"),
    PRIVILEGE_INVALID_NEMO(5001, "privilege.invalid.nemo"),
    PRIVILEGE_INVALID_USER(5002, "privilege.invalid.user"),
    PRIVILEGE_INVALID_PRIVILEGE(5003, "privilege.invalid.privilege"),
    UPLOAD_INVALID_URL(6001, "upload.invalid.url"),
    UPLOAD_INVALID_IO(6002, "upload.invalid.io"),
    UPLOAD_INVALID_METADATA(6003, "upload.invalid.metadata"),
    INVALID_MEMBER(7001, "invalid.member"),
    ALREADY_INCIRCLE(7002, "already in the circle"),
    DIFFERENT_USE_MODE(7003, "invalid.different.usemode"),
    NEMOID_DEVICEPO_NOT_EXISTS(8001, "nemoid.devicepo.not.exists"),
    NEMOID_NUMBERPO_NOT_EXISTS(8002, "nemoid.numberPO.not.exists"),
    USER_ALREADY_FRIEND_AND_IN_NEMOCIRCLE(8003, "noneed.associate.user.and.nemo"),
    NEMO_INVALID_REQUEST_TYPE(8004, "nemo.invalid.request.type"),
    NEMO_INVALID_REQUEST(8005, "nemo.invalid.request"),
    NEMO_REQUESTING_ITSELF(8006, "nemo.requesting.itself"),
    AUTHORITY_UNRECOGNIZED_RULES(9001, "authority.unrecognized.rules"),
    PAGE_REQUESTER_IN_SOME_CIRCLE(10001, "requester.in.some.circle"),
    PAGE_EXPERIENCE_NEMO_LIST_IS_EMPTY(10002, "experience.nemo.list.is.empty"),
    INVALID_OPENNEMO_CONFIG(10003, "opennemo.invalid.config"),
    ENTERPRISE_NOT_EXIST(12001, "enterprise is not exist"),
    INVALID_FEATURE_NUMBER(20001, "number.invalid"),
    NEMO_NO_FEATURE(20002, "nemo.no.feature"),
    ACCOUNT_NOT_EXIST(30001, "account.not.exist"),
    BALANCE_NOT_ENOUGH(30002, "balance.not.enough"),
    ORDER_HAS_PAID(30010, "order.has.paid"),
    NOT_ENOUGH_STORAGE(40001, "nemo.storage.full"),
    CONFERENCE_ROOM_EXPIRED(40002, "nemo.conferenceroom.expired"),
    CHARGE_RESOURCE_NOT_FOUND(40003, "nemo.chargepo.notfound"),
    CONFERENCE_ROOM_SHARECODE_INVALID(40004, "sharecode.invalid"),
    INVALID_NUMBER(4115, "invalid input number"),
    INVALID_NEMO_NUMBER(4116, "invalid nemo number"),
    INVALID_CLOUD_MEETING_NUMBER(4117, "invalid cloud meeting number"),
    /** @deprecated */
    @Deprecated
    INVALID_APP_CALL_PSTN(4118, "app can't call pstn number"),
    /** @deprecated */
    @Deprecated
    INVALID_APP_OR_WX_CALL_PSTN(4118, "app/wx can't call pstn number"),
    INVALID_DEVICE_CALL_PSTN(4118, "device(app,wx etc ...) can't call pstn number"),
    INVALID_NEMO_NUMBER_OCCUPIED(4119, "nemo number occupied"),
    NEMO_PHONE_WHITE_LIST_CHECK_FAILURE(4120, "white list check failure"),
    CONTACT_OBSERVER_ONLY_CALL_FAILURE(4121, "contactObserverOnly call failure"),
    H323_GATE_WAY_FAILURE(4122, "h323 gate way check failure"),
    UNKNOWN_ERROR(40201, "internal.error"),
    INVALID_INPUT(40202, "invalid.input"),
    INVALID_LIVE_ID(40203, "invalid.live.id"),
    INVALID_USER_ID(40204, "invalid.user.id"),
    INVALID_ANSWER(40205, "invalid.answer"),
    INVALID_ENTERPRISE_ID(40206, "invalid.enterprise.id"),
    INVALID_CONFERENCE_NO(40207, "invalid.conference.number"),
    INVALID_MEETING_ID(40208, "invalid.meeting.id"),
    INVALID_DEVICE_ID(40209, "invalid.device.id"),
    INVALID_QUESTION(40210, "invalid.question"),
    VOTE_NOT_FOUND(40211, "vote.not.found"),
    VOTE_EXPIRED(40212, "internal.error"),
    VOTE_DELETED(40213, "internal.error"),
    VOTE_STATUS_ERROR(40214, "vote.status.error"),
    VOTE_REPEAT(40215, "vote.answer.repeat"),
    QUESTION_NOT_FOUND(40217, "question.not.found"),
    USER_NOT_ANSWER(40218, "user.not.answer"),
    SERVER_EXCEPTION(50201, "internal.server.exception"),
    CRM_VOUCHER_BUSINESS_ERROR_11001(11001, "above receive limit"),
    CRM_VOUCHER_BUSINESS_ERROR_11002(11002, "receive expired"),
    CRM_VOUCHER_BUSINESS_ERROR_11003(11003, "receive over"),
    CRM_VOUCHER_BUSINESS_ERROR_11004(11004, "voucher group not exist"),
    CRM_VOUCHER_BUSINESS_ERROR_11005(11005, "voucher not exist"),
    CRM_VOUCHER_BUSINESS_ERROR_11006(11006, "failed to get receive user info"),
    CRM_VOUCHER_BUSINESS_ERROR_11007(11007, "failed to get dispense user info"),
    CRM_VOUCHER_BUSINESS_ERROR_11008(11008, "receive dispense own"),
    CRM_VOUCHER_BUSINESS_ERROR_11009(11009, "receive timeout"),
    CRM_VOUCHER_BUSINESS_ERROR_21001(21001, "failed to get dispense user info"),
    CRM_VOUCHER_BUSINESS_ERROR_21002(21002, "voucher wallet not exist"),
    CRM_VOUCHER_BUSINESS_ERROR_21003(21003, "no permission"),
    CRM_VOUCHER_BUSINESS_ERROR_21004(21004, "wallet not enough"),
    CRM_VOUCHER_BUSINESS_ERROR_21005(21005, "failed to get dispense user enterprise"),
    CRM_VOUCHER_BUSINESS_ERROR_21006(21006, "dispense timeout"),
    CRM_VOUCHER_BUSINESS_ERROR_31001(31001, "data rewrite md5"),
    CRM_VOUCHER_BUSINESS_ERROR_41001(41001, "param error!"),
    CRM_VOUCHER_BUSINESS_ERROR_41002(41002, "recharge type error，480/360！"),
    CRM_VOUCHER_BUSINESS_ERROR_41003(41003, "voucher wallet is error！please contact administrator"),
    CRM_VOUCHER_BUSINESS_ERROR_41004(41004, "insert into wallet date error！"),
    CRM_VOUCHER_BUSINESS_ERROR_41005(41005, "update wallet date error！"),
    CRM_DISTRIBUTOR_BUSINESS_ERROR_12001(12001, "distributor entrance check failed"),
    CRM_DISTRIBUTOR_BUSINESS_ERROR_12002(12002, "voucher entrance check failed"),
    CRM_DISTRIBUTOR_BUSINESS_ERROR_12003(12003, "training entrance check failed"),
    CRM_DISTRIBUTOR_BUSINESS_ERROR_12004(12004, "manufacturer entrance check failed"),
    OPENNEMO_CATEGORY_DELETE_FORBIDDEN(50001, "opennemo.category.delete.forbidden"),
    SDK_INVALID_APPKEY(60001, "sdk.invalid.key"),
    SDK_APPKEY_ALREADY_EXIST(60002, "sdk.key.already.exists"),
    OPENAPI_INVALID_SIGNATURE(60003, "openapi.invalid.signature"),
    OPENAPI_ENTERPRISEID_REQUIRED(60004, "enterpriseid.required"),
    OPENAPI_USER_OTHER_ENTERPRISE(60005, "user.in.other.enterprise"),
    OPENAPI_INVALID_APPID(60006, "invalid.appid"),
    OPENAPI_INVALID_MEETINGNUMBER(60007, "meetingnumber not exist"),
    OPENAPI_INVALID_ENTERPRISE_MEETINGNUMBER(60008, "meetingnumber not in enterprise"),
    OPENAPI_INVALID_ENTERPRISE_DEVICESN(60009, "invalid enterprise device SN"),
    OPENAPI_INVALID_ENTERPRISE_USER(60010, "invalid enterprise user id"),
    OPENAPI_SIGNATURE_REQUIRED(60011, "signature.required"),
    OPENAPI_INVALID_CALLNUMBER(60012, "invalid call number"),
    OPENAPI_USER_EXPIRED(60013, "user expired"),
    OPENAPI_VOD_TIME_INVALID(60014, "invalid time"),
    OPENAPI_VOD_NO_PERMISSION(60015, "no permission"),
    OPENAPI_NEMO_NOT_IN_ENTERPRISE(60016, "nemo not in enterprise"),
    OPENAPI_USER_NOT_IN_ENTERPRISE(60017, "user not in enterprise"),
    OPENAPI_VOD_INVALID_ID(60018, "invalid vod id"),
    OPENAPI_VOD_DOWNLOAD_TIMEOUT(60019, "vod download timeout"),
    OPENAPI_INVALID_VOD_START_INDEX(60020, "vod download invalid startindex"),
    OPENAPI_NEMONUMBER_REQUIRED(60021, "nemonumber.required"),
    OPENAPI_LIVE_INVALID_TITLE(60022, "live.title.invalid"),
    OPENAPI_LIVE_INVALID_DETAIL(60023, "live.detail.invalid"),
    OPENAPI_LIVE_INVALID_LOCATION(60024, "live.location.invalid"),
    OPENAPI_LIVE_NOT_EXIST(60025, "live.notexists"),
    OPENAPI_LIVE_EXISTS(60026, "live.exists"),
    OPENAPI_INVALID_MEETING_NUMBER_FORMAT(60027, "meeting.number.format.invalid"),
    OPENAPI_MEETING_NUMBER_EXIST(60028, "meeting.number.already.exist"),
    OPENAPI_REQUIRED_PARAMETER_IS_EMPTY(60029, "remindermeeting.requiredparameter.empty"),
    OPENAPI_NEMO_DEVICE_NOT_EXIST(60030, "remindermeeting.nemo.device.not.exist"),
    OPENAPI_INVALID_AUTOMUTE_MODE(60031, "confernece.number.invalid.automute"),
    OPENAPI_INVALID_SMARTMUTE_COUNT(60032, "conference.number.invalid.smartmute"),
    OPENAPI_INVALID_ENDTIME(60033, "create.meeting.invalid.endtime"),
    OPENAPI_MEETING_ROOM_PWD_REQUIRED(60034, "meeting.pwd.required"),
    OPENAPI_MEETING_ROOM_PWD_INVALID(60035, "meeting.pwd.invalid"),
    OPENAPI_LIVE_NO_MEETING(60036, "live.no_meeting"),
    OPENAPI_LIVE_NO_CONF(60037, "live.no_conf"),
    OPENAPI_LIVE_NO_PRIVILEGE(60038, "live.no_privilege"),
    OPENAPI_UNKNOWN_CALL_URL_TYPE(60039, "unknown call url type"),
    OPENAPI_INVALID_USER_COUNTRYCODE(60040, "user countryCode invalid"),
    OPENAPI_INVALID_USER_PHONE(60040, "user phone invalid"),
    OPENAPI_INVALID_USER_MAIL(60041, "user mail invalid"),
    OPENAPI_MAIL_BIND_OTHER_PHONE(60042, "user email bind other phone"),
    OPENAPI_MEETING_NOT_STARTED(60043, "confernce.not.started"),
    OPENAPI_OPERATOR_EXCEED_MAX_PORT_COUNT(60044, "exceed.max.count"),
    OPENAPI_LIVE_INAPPROPRIATE_TIME(60045, "live.inappropriate_time"),
    OPENAPI_HYSTRIX(60046, "the service is busy, please try again later"),
    USER_NEMO_RELATION_ALREADY_EXIST(70001, "relation.already.exists"),
    USER_NEMO_RELATION_INVALID_USER(70002, "relation.not.related.user"),
    USER_CONFERENCE_NUMBER_EXIST(80001, "user.meetingroom.exist"),
    ENTERPRISE_CONFERENCE_NUMBER_EXIST(80002, "enterprise.meetingroom.exist"),
    CONFERENCE_PASSWORD_INVALID(80003, "meetingroom.password.invalid"),
    SCHEDULED_MEETING_OWNER_INVALID(100001, "scheduledmeeting.owner.invalid"),
    SCHEDULED_MEETING_TIME_CONFLICT(100002, "scheduledmeeting.time.conflict"),
    SCHEDULED_MEETING_NOT_EXIST(100003, "scheduledmeeting.not.exist"),
    SCHEDULED_MEETING_INVALID_TIME_INTERVAL(100004, "scheduledmeeting.invalid.timeinterval"),
    SCHEDULED_MEETING_PARTICIPANT_REQUIRED(100005, "scheduledmeeting.participant.required"),
    SCHEDULED_MEETING_INVALID_MAINIMAGE(100006, "scheduledmeeting.invalid.mainimage"),
    LOCAL_CONTACT_ENTITY_ALREADY_EXIST(200001, "local.contact.entity.already.exist"),
    LOCAL_CONTACT_FORBID(200002, "local.contact.forbid"),
    LOCAL_CONTACT_ENTITY_NO_EXIST(200003, "local.contact.entity.no.exist"),
    CERTIFICATE_NOT_EXIST(300001, "private.cloud.certificate.not.exist"),
    RECORDING_MEETING_NOT_EXIST(400001, "recording.meeting.not.exist"),
    RECORDING_SERVICE_TIMEOUT(400002, "recording.service.timeout"),
    RECORDING_UNKNOWN_ERROR(400003, "recording.unknown.error"),
    RECORDING_SERVER_NO_RESOURCE(400004, "recording.server.noresoure"),
    SCHEDULED_EVENT_TIME_CONFLICT(500001, "scheduled.event.time.conflict"),
    SCHEDULED_EVENT_INVALID_TIME_INTERVAL(500002, "scheduled.event.invalid.time.interval"),
    SCHEDULED_EVENT_PARTICIPANT_REQUIRED(500003, "scheduled.event.participant.required"),
    SCHEDULED_EVENT_OWNER_INVALID(500004, "scheduled.event.owner.invalid"),
    SCHEDULED_EVENT_NOT_EXIST(500005, "scheduled.event.not.exist"),
    SCHEDULED_EVENT_PARTICIPANT_IN_GROUP(500006, "user.in.group.delete.not.allowed"),
    SCHEDULED_EVENT_SPECIFIED_NUMBER_EMPTY(500007, "scheduled.event.specified.number.empty"),
    SCHEDULED_EVENT_SPECIFIED_NUMBER_INVALID(500008, "scheduled.event.specified.number.notexist"),
    SCHEDULED_EVENT_INVALID_ENTERPRISE(500009, "scheduled.event.invalid.enterprise"),
    SCHEDULED_EVENT_NO_PERMISSION(500010, "scheduled.event.no.permission"),
    MAIL_EXIST(600001, "mail exist"),
    OBJ_EXIST(600011, "object exist"),
    MAIL_VALIDATE_EXPIRE(600002, "mail validate expire"),
    MAIL_NOT_EXIST(600003, "mail is not exist"),
    MAIL_IS_VALIDATED(600004, "mail is validated"),
    PHONE_IS_EXIST(600005, "phone is validated"),
    PHONE_AND_MAIL_IS_EXIST(600006, "phone is validated"),
    PHONE_AND_MAIL_DIFFERENT_ACCOUNT(600007, "phone and mail is belong to different account"),
    ACTIVATION_NOT_EXIST(700001, "activation code not exist"),
    ACTIVATION_OR_MACHINECODE_NOT_EXIST(700002, "activation or machine param is null"),
    ACTIVATION_CODE_USED(700003, "activation code used"),
    ACTIVATION_CODE_CYCLE_TYPE(700004, "cycle type error"),
    EMAIL_USER_PRE_REQUEST_NULL(800001, "email user request is null"),
    EAMIL_USER_PRE_EMAIL_EXIST(800002, "email user exist"),
    EAMIL_USER_PRE_SAVE_ERROR(800003, "save error"),
    EMAIL_USER_PRE_VALIDATE_PARAM_NULL(800004, "param is null"),
    EMAIL_USER_PRE_VALIDATE_TOKEN_NULL(800005, "email user validate token is null"),
    EMAIL_USER_PRE_VALIDATE_TOKEN_NOT_EXIST(800006, "email user validate token is not exsit"),
    EMAIL_USER_PRE_VALIDATE_PWD_NULL(800007, "email user validate pwd is null"),
    EMAIL_USER_PRE_VALIDATE_PWD_NOT_EXIST(800008, "email user validate pwd is not exsit"),
    EMAIL_USER_PRE_VALIDATE_TOKEN_NOT_EXP(800009, "email user validate expired"),
    EMAIL_USER_PRE_VALIDATE_TOKEN_VALIDATED(800010, "email user validate token is validated"),
    EMAIL_USER_PRE_VALIDATE_TOKEN_EXPIRED(800011, "email user validate token is EXPIRED"),
    EMAIL_USER_PRE_VALIDATE_ACCOUNT_EXIST(800012, "email user account exist"),
    EMAIL_USER_PRE_VALIDATE_TOKEN_INVALID(800013, "email user validate token is invalid"),
    EMAIL_USER_PRE_VALIDATE_UPDATE_STATUS_ERROR(800014, "email user validate token update status error"),
    EAMIL_USER_PRE_EMAIL_EXIST_VALIDATE_ENTERPRISE(8000015, "email user validate in this enterprise"),
    EAMIL_USER_PRE_EMAIL_EXIST_DELETE_ERROR(8000016, "delete email user error"),
    EAMIL_USER_CAN_NOT_BIND_EMAIL(8000017, "can not bind emil with only email user"),
    EMAIL_USER_SUPER_ADMIN_CAN_NOT_EDIT(8000018, "super admin can not edit"),
    DEVICE_ADD_SUCCESS(7799, "device.add.success"),
    DEVICE_ADD_UNACTIVATED(7790, "device.add.unactivated"),
    DOWNLOAD_URL_SUCCESS(777000, "VOD URL SUCCESS"),
    DOWNLOAD_URL_DECRYPTIONING(777001, "VOD IS DECRYPTIONING"),
    DOWNLOAD_URL_ERROR(777002, "PARAM IS ERROR"),
    CONFERENCE_TYPE_ERROR(997700, "conference type is not exist"),
    CONFERENCE_NOT_EXIST(997701, "conference not exist"),
    WECHAT_OPENID_REQUEST_NULL(9000001, "wechat openid is null"),
    MINI_PROGRAM_TOKEN_EXPIRED(9000002, "token is expired"),
    INVALID_PHONE_REGISTERING_WECHAT(9000003, "phone has already registered wechat"),
    MP_INNER_SERVICE_INVOKE_ERROR(900004, "micro program invoke inner service error"),
    MP_PARAM_ERROR(900005, "param error"),
    MP_DECRYPT_ERROR(900006, "decrypt error"),
    MP_INVITE_KEY_NOT_INVALID(900007, "inviteKey not invalid"),
    MP_INVITE_NUMBER_IS_NULL(900008, "inviteNumber is null"),
    MP_INVITE_ERROR(900009, "call invitation error"),
    MP_UPDATE_USER_NAME_ERROR(900010, "updateUserName input error"),
    MP_CALL_RATE_LIMIT(900011, "call limit"),
    MP_INNER_ERROR(900500, "mp service internal error"),
    MP_THIRD_PARTY_ERROR(900501, "invoke third party service error"),
    MP_TOKEN_STATUS_SCHEDULE_TIME_CONFLICT(901001, "time conflict"),
    MP_TOKEN_STATUS_REMIND_DEVICE_ERROR(901002, "notify device error"),
    MP_TOKEN_STATUS_USER_NOT_FOUND(901003, "user not found"),
    MP_TOKEN_STATUS_ENTERPRISE_NOT_FOUND(901004, "enterprise_not_found"),
    MP_TOKEN_STATUS_SCHEDULE_NOT_FOUND(901005, "schedule not found"),
    MP_TOKEN_STATUS_AUTH_DEVICE_NOT_FOUND(901007, "auth device not found"),
    MP_TOKEN_STATUS_USER_CREATE_ERROR(901008, "user create error"),
    MP_TOKEN_STATUS_ENTERPRISE_CREATE_ERROR(901009, "enterprise create error"),
    MP_TOKEN_STATUS_LOGIN_ERROR(901010, "sdk login error"),
    MP_TOKEN_STATUS_PHONE_LOGIN_ERROR(901011, "associate phone login error"),
    MP_TOKEN_STATUS_USER_NOT_ADMIN(901012, "user not admin"),
    MP_TOKEN_STATUS_USER_NOT_BIND_PHONE(901013, "user not bind phone"),
    MP_TOKEN_STATUS_ENTERPRISE_ASSOCIATED(901014, "binded before"),
    MP_TOKEN_STATUS_ENTERPRISE_ASSOCIATED_TO_OTHER(901015, "either of the enterprises was used"),
    MP_TOKEN_STATUS_ENTERPRISE_NOT_ASSOCIATED(901016, "no associate enterprise exist"),
    MP_TOKEN_STATUS_PHONE_ACCOUNT_NOT_FOUND(901017, "phone account not exist"),
    MP_TOKEN_STATUS_ASSOCIATED_ACCOUNT_REPEAT(901018, "associate account repeat"),
    MP_TOKEN_STATUS_ASSOCIATED_PHONE_REPEAT(901019, "associate phone repeat"),
    MP_TOKEN_STATUS_PHONE_ACCOUNT_REGISTER_ERROR(901020, "register phone account error"),
    MP_TOKEN_STATUS_ENTERPRISE_NOT_EXIST(901021, "enterprise not exist in buffet"),
    MP_TOKEN_STATUS_DEVICE_TYPE_EXIST(901022, "device type exist"),
    MP_TOKEN_STATUS_DEVICE_TYPE_NOT_EXIST(901023, "device type not exist"),
    MP_TOKEN_STATUS_JSCODE_ERROR(901024, "wechat jscode error"),
    MP_TOKEN_STATUS_PROVIDER_TOKEN_ERROR(901025, "provider access token error"),
    MP_TOKEN_STATUS_ADD_DEVICE_ERROR(901026, "add device 2 wx error"),
    MP_TOKEN_STATUS_SHARE_QRCODE_ERROR(901027, "get share meeting qrcode error"),
    MP_TOKEN_STATUS_GET_ADMIN_LIST_ERROR(901028, "get wx admin list error"),
    MP_TOKEN_STATUS_DECRYPT_MOBILE_ERROR(901029, "decrypt mobile error"),
    MP_TOKEN_STATUS_GET_WX_USER_INFO_ERROR(901030, "get wx user info error"),
    MP_TOKEN_STATUS_ADD_DEVICE_REPEAT(901031, "add device repeat");

    private int errorCode = 0;
    private String resId;

    private ErrorStatus() {
    }

    private ErrorStatus(String resId) {
        this.resId = resId;
    }

    private ErrorStatus(int errorCode, String resId) {
        this.errorCode = errorCode;
        this.resId = resId;
    }

    public int getErrorCode() {
        return this.errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getResId() {
        return this.resId;
    }

    public void setResId(String resId) {
        this.resId = resId;
    }
}
