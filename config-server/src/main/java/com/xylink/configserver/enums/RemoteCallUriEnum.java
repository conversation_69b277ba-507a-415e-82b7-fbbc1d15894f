package com.xylink.configserver.enums;

import org.springframework.http.HttpMethod;

/**
 * <AUTHOR>
 * @since 2021/11/25 2:37 下午
 */
public enum RemoteCallUriEnum {

    /**
     * errorUrl
     */
    ERROR_URL(HttpMethod.GET, "http://www.ainemo.com/errors/{}"),

    /**
     * contact - 根据用户/设备/部门id集合查询其所属主部门指定层级的上级部门
     * <a href="https://yapi.xylink.com/project/285/interface/api/64267">YApi</a>
     */
    CONTACT_FETCH(HttpMethod.POST, "/api/rest/department/internal/v1/getMainParentDepartmentIdAndLevelByMemberIds");

    private final HttpMethod method;
    private final String uri;

    RemoteCallUriEnum(HttpMethod method, String uri) {
        this.method = method;
        this.uri = uri;
    }

    public HttpMethod getMethod() {
        return method;
    }

    public String getUri() {
        return uri;
    }

    public String getUri(String restApiInternal) {
        return restApiInternal + uri;
    }

}
