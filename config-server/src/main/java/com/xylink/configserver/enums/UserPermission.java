package com.xylink.configserver.enums;

public enum UserPermission {
    CREATE_LIVE("role.setting.date_living"),
    CREATE_MEETING("role.setting.date_meeting"),
    CONTROL_MEETING("role.setting.control_meeting"),
    INVITE_MEETING("role.setting.active_meeting"),
    SHARED_FOLDER("role.setting.shared_folder"),
    ATTENDEE("role.setting.attendee"),
    RECORDING("role.setting.recording"),
    CLOUD_RECORDING("role.setting.cloud_recording"),
    LOCAL_RECORDING("role.setting.local_recording")
    ;

    private String roleKey;

    private UserPermission(String roleKey) {
        this.roleKey = roleKey;
    }

    public String getRoleKey() {
        return this.roleKey;
    }
}
