package com.xylink.configserver.enums;

public enum RequestType {
    HOME(0),
    ENTERPRISE(1);

    private int type;

    private RequestType(int type) {
        this.type = type;
    }

    public int getType() {
        return this.type;
    }

    public static RequestType valueOf(int type) {
        RequestType[] var1 = values();
        int var2 = var1.length;

        for(int var3 = 0; var3 < var2; ++var3) {
            RequestType value = var1[var3];
            if (value.getType() == type) {
                return value;
            }
        }

        throw new IllegalArgumentException("Invalid request type.");
    }

    public static RequestType otherType(RequestType type) {
        return HOME == type ? ENTERPRISE : HOME;
    }
}
