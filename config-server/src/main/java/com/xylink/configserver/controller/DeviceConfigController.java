package com.xylink.configserver.controller;

import com.xylink.configserver.aspect.PathVariableSecurityCheck;
import com.xylink.configserver.aspect.PathVariableSecurityCheckField;
import com.xylink.configserver.controller.base.BaseController;
import com.xylink.configserver.data.dto.BatchUpdateDeviceConfigResponseDTO;
import com.xylink.configserver.data.model.DeviceConfigUpdate;
import com.xylink.configserver.data.model.DeviceConfigUpdateWithSn;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.data.response.R;
import com.xylink.configserver.data.vo.DeviceConfigVo;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.service.DeviceConfigService;
import com.xylink.configserver.util.Jackson;
import com.xylink.configserver.util.Status;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
public class DeviceConfigController extends BaseController {

    @Autowired
    DeviceConfigService deviceConfigService;

    @GetMapping("/{version}/{en}/device/{deviceId}/configs")
    @PathVariableSecurityCheck
    public Map<String, String> getDeviceCap(@PathVariableSecurityCheckField(field = "deviceId") @PathVariable long deviceId){
        if (deviceId <= 0){
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST,ErrorStatus.INVALID_PARAMETER);
        }
        return deviceConfigService.getCombinedConfig(deviceId);
    }

    @PutMapping(value = "/internal/{version}/device/{deviceId}/configs")
    public Status updateInternalDeviceConfig(@RequestBody DeviceConfigUpdate[] configs, @PathVariable long deviceId) {

        log.info("update internal device config for deviceId:{},configs:{}", deviceId, Jackson.writeValueAsString(configs));

        deviceConfigService.updateInternalDeviceConfig(configs, deviceId);

        return Status.SUCCESS_OK;

    }


    @PostMapping (value = "/clientConfg/internal/device/nemoConfig/update/v1")
    public R<Void> updateInternalDeviceConfigBySn(@RequestBody DeviceConfigUpdate config, @RequestParam String sn) {

        log.info("update internal device config for sn:{},config:{}", sn, Jackson.writeValueAsString(config));

        deviceConfigService.updateInternalDeviceConfigBySn(config, sn);

        return R.ok();

    }

    @PutMapping(value = "/{version}/{en}/device/{deviceId}/configs")
    public void updateDeviceConfig(@RequestBody Map<String, String> configs,@PathVariable long deviceId) {
        deviceConfigService.updateDeviceConfig(configs,deviceId);
    }

    @PostMapping(value = "/clientConfg/internal/device/configs/delete/v1")
    public void deleteDeviceConfig(@RequestBody DeviceConfigVo[] deviceConfigList) {
        log.info("Request Param: {}", Jackson.writeValueAsString(deviceConfigList));
        deviceConfigService.deleteConfigByName(deviceConfigList);
    }

    @PostMapping(value = "/clientConfg/internal/device/configs/batchUpdate/v1")
    public R<BatchUpdateDeviceConfigResponseDTO<DeviceConfigUpdateWithSn>> internalBatchUpdateDeviceConfig(@RequestBody List<DeviceConfigUpdateWithSn> deviceConfigList) {
        log.info("Request request size: {}", deviceConfigList.size());
        //基础数据清洗
        if (CollectionUtils.isEmpty(deviceConfigList)) {
            log.info("Request is empty");
            return R.fail();
        }
        return  deviceConfigService.batchUpdateDeviceConfig(deviceConfigList);
    }


    @PostMapping (value = "/clientConfg/internal/device/nemoConfig/delete/v1")
    public R<Void> deleteDeviceConfigBySn(@RequestBody DeviceConfigUpdate config, @RequestParam String sn) {

        log.info("delete internal device config for sn:{},config:{}", sn, Jackson.writeValueAsString(config));

        deviceConfigService.deleteConfigByNameAndSn(config, sn);

        return R.ok();

    }
}