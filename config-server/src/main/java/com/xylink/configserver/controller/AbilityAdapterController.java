package com.xylink.configserver.controller;

import com.xylink.configserver.data.dto.abilityset.AbilityResultDTO;
import com.xylink.configserver.data.dto.abilityset.DeviceAbilitySetDto;
import com.xylink.configserver.data.dto.abilityset.ServerAbilitySetDto;
import com.xylink.configserver.service.AbilitySetService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

/**
 * 能力集
 *
 * <AUTHOR>
 * @since 2021/11/18 15:13
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/clientConfg")
public class AbilityAdapterController {

    private final AbilitySetService abilitySetService;


    @PostMapping("/external/unsafe/ability/v1")
    public List<Long> getServerAbility(@RequestBody Long[] abilities) {

        log.info("RP-AbilityAdapterController-getServerAbility:abilities:{}", Arrays.asList(abilities));

        return abilitySetService.getServerAbility();

    }

    /**
     * 获取组合能力集
     *
     * @param deviceAbilitySetDto 终端能力集
     * @return 组合能力集
     */
    @PostMapping("/external/ability/combined/v1")
    public List<Long> getCombinedAbility(@RequestBody DeviceAbilitySetDto deviceAbilitySetDto) {

        log.info("RP-AbilityAdapterController-getCombinedAbility:abilities:{}", deviceAbilitySetDto);

        return abilitySetService.getCombinedAbility(deviceAbilitySetDto);

    }

    /**
     * 获取组合能力集
     *
     * @param deviceAbilitySetDto 终端能力集
     * @return 组合能力集
     */
    @PostMapping("/external/ability/combined/v2")
    public AbilityResultDTO getCombinedAbilityStr(@RequestBody DeviceAbilitySetDto deviceAbilitySetDto) {

        log.info("RP-AbilityAdapterController-getCombinedAbility:abilities:{}", deviceAbilitySetDto);

        return abilitySetService.getCombinedAbilityStr(deviceAbilitySetDto);

    }

    /**
     * 变更服务端能力集
     */
    @PostMapping("/internal/ability/server/v1")
    public void addOrUpdateServerAbility(@RequestBody List<ServerAbilitySetDto> serverAbilitySetDtoList) {

        log.info("RP-AbilityAdapterController-addServerAbility:serverAbilitySetVo:{}", serverAbilitySetDtoList);

        abilitySetService.changeServerAbility(serverAbilitySetDtoList);

    }

    @PostMapping("/internal/ability/cache/v1/delete")
    public void removeAbilityCache() {

        abilitySetService.removeAbilityCache();

    }


}