package com.xylink.configserver.controller;

import com.xylink.configserver.aspect.PathVariableSecurityCheck;
import com.xylink.configserver.aspect.PathVariableSecurityCheckField;
import com.xylink.configserver.controller.base.BaseController;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.data.model.RestNemoConfig;
import com.xylink.configserver.data.model.UserDevice;
import com.xylink.configserver.enums.DeviceType;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.RestException;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.service.DeviceService;
import com.xylink.configserver.service.NemoConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

@Slf4j
@RestController
public class NemoConfigController extends BaseController {

    @Autowired
    NemoConfigService nemoConfigService;
    @Autowired
    DeviceService deviceService;

    @GetMapping("/{version}/nemo/{nemoId}/nemoconfig")
    @PathVariableSecurityCheck
    public List<RestNemoConfig> getNemoConfigs(@PathVariableSecurityCheckField(field = "deviceId") @PathVariable long nemoId, @RequestParam String hardVersion,
                                               @RequestParam String softVersion, @RequestParam String os,
                                               @RequestParam String model, HttpServletRequest request) {
        try {
            return nemoConfigService.getExternalNemoConfigs(nemoId, hardVersion, softVersion, os, model,request);
        } catch (ServiceException e) {
            log.error("get nemo configs error,nemoId:" + nemoId, e);
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, e.getStatus());
        }
    }

    @GetMapping({"/internal/{version}/{nemoId}/nemoconfig/{configName}","/internal/v1/{nemoId}/tvboxconfig/{configName}"})
    public String getNemoConfigByName(@PathVariable long nemoId,@PathVariable String configName) {
        return nemoConfigService.getNemoConfigByName(nemoId, configName);
    }

    @PutMapping(value = "/{version}/nemo/{nemoId}/nemoconfig")
    public void updateNemoConfig(@RequestBody RestNemoConfig[] configs,@PathVariable long nemoId) {
        log.info("update nemo config for nemo:{}, configs:{}" ,nemoId, configs);
        try {
            if(configs == null) {
                log.info("Nemo config is null, return。nemo:{}",nemoId);
                ErrorStatus errorStatus = ErrorStatus.INVALID_PARAMETER;
                throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, errorStatus.getErrorCode(),
                        errorStatus.getResId());
            }
            nemoConfigService.updateNemoConfig(configs,nemoId);
        } catch (ServiceException e) {
            log.error("update nemo configs error,nemoId:" + nemoId,e);
            throw getRestException(HttpStatus.SERVER_ERROR_INTERNAL, e.getStatus());
        }
    }

    @PutMapping(value = "/internal/{version}/{nemoId}/nemoconfig")
    public void updateInternalNemoConfig(@RequestBody RestNemoConfig[] configs,@PathVariable long nemoId) {
        log.info("update nemo config for nemo:{}, configs:{}" ,nemoId, configs);
        try {
            if(configs == null) {
                log.info("Nemo config is null, return。nemo:{}",nemoId);
                throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER);
            }
            nemoConfigService.updateInternalNemoConfig(configs,nemoId);
        } catch (ServiceException e) {
            log.error("update nemo configs error,nemoId:" + nemoId,e);
            throw getRestException(HttpStatus.SERVER_ERROR_INTERNAL, e.getStatus());
        }
    }
    @GetMapping(value = "/internal/{version}/{nemoId}/nemoconfig")
    public List<RestNemoConfig>  getInternalNemoConfig(@PathVariable long nemoId) {
        log.info("get internal nemo config for nemo:{}" ,nemoId);
        try {
            return nemoConfigService.getInternalNemoConfigs(nemoId);
        } catch (ServiceException e) {
            log.error("update nemo configs error,nemoId:" + nemoId,e);
            throw getRestException(HttpStatus.SERVER_ERROR_INTERNAL, e.getStatus());
        }
    }

    /**
     * 呼叫检查用，目前用来查询呼叫权限
     * @param nemoId
     * @return
     */
    @GetMapping(value = "/clientConfg/internal/{version}/nemoconfig")
    public List<RestNemoConfig>  getInternalNemoCallConfig(@RequestParam long nemoId) {
        log.info("get internal nemo call config for nemo:{}" ,nemoId);
        try {
            return nemoConfigService.getInternalNemoCallConfigs(nemoId);
        } catch (ServiceException e) {
            log.error("update nemo configs error,nemoId:" + nemoId,e);
            throw getRestException(HttpStatus.SERVER_ERROR_INTERNAL, e.getStatus());
        }
    }


    @PostMapping(value = "/clientConfg/internal/{version}/devices/configs")
    public Map<String,List<RestNemoConfig>>batchGetDeviceConfigs(@RequestBody List<Long>nemoIds){
        if (Objects.isNull(nemoIds) || nemoIds.size()<=0){
            return Collections.emptyMap();
        }
        Map<String,List<RestNemoConfig>> result  = new HashMap<>();
        nemoIds.stream().forEach(nemoId ->{
            log.info("get internal nemo config :" + nemoId);
            UserDevice po = deviceService.getUserDeviceByNemoId(nemoId);
            if (po != null && DeviceType.isHardDevice(po.getType())){
                List<RestNemoConfig> configs= nemoConfigService.getInternalNemoConfigs(nemoId);
                result.put(String.valueOf(nemoId),configs);
            }else {
                result.put(String.valueOf(nemoId),null);
            }

        });
        return result;

    }
}
