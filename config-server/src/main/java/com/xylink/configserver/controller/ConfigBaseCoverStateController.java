package com.xylink.configserver.controller;

import com.xylink.configserver.service.DefaultConfigService;
import com.xylink.configserver.util.ConfigBaseCoverUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 * @FilePath:com.xylink.configserver.util.ConfigBaseCoverUtil
 * @Description: 基底默认配置应急接口
 */
@RestController
@RequestMapping("/clientConfg/internal/config/noBase")
public class ConfigBaseCoverStateController {

    @Autowired
    private DefaultConfigService defaultConfigService;


    @GetMapping("/change")
    public void checkRedisHealthState(@RequestParam(value="noBase") boolean noBase) {
        ConfigBaseCoverUtil.changeNoBase(noBase);
        defaultConfigService.refreshDefaultConfigCache();
    }

}
