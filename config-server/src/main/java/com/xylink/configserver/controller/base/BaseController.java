package com.xylink.configserver.controller.base;

import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.RestException;

public class BaseController {

    protected RestException getRestException(HttpStatus httpStatus,
                                             ErrorStatus errorStatus) {
        return new RestException(httpStatus, errorStatus.getErrorCode(),
                errorStatus.getResId());
    }
}
