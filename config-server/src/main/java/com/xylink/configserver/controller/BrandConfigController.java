package com.xylink.configserver.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xylink.configserver.data.model.NemoDefaultBrandConfig;
import com.xylink.configserver.service.BrandConfigService;
import com.xylink.configserver.util.Jackson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@Slf4j
@RestController
public class BrandConfigController {

    @Autowired
    BrandConfigService brandConfigService;

    @PutMapping(value = "/internal/{version}/{en}/nemo/brandConfig/cache")
    public void clearBrandConfigCache(@RequestParam String brand,@RequestParam(value = "configType",defaultValue = "-1") int configType,@RequestParam(value = "brandModel",required = false) String brandModel) {
        log.info("begin clear BRAND_CONFIG_CACHE .brand:{} and configType is:{} and brand model is :{}",brand, configType,brandModel);
        brandConfigService.clearBrandConfigCache(brand,configType,brandModel);
    }

    @GetMapping("/internal/{version}/{en}/nemo/brand/configList")
    public Map<String, String> getDefaultBrandConfig() {
        return brandConfigService.getDefaultBrandConfigAll();
    }


    @PutMapping(value = "/internal/{version}/{en}/nemo/brand/config/create")
    public void createOrUpdateDefaultBrandConfig(@RequestBody NemoDefaultBrandConfig defaultBrandConfig) throws JsonProcessingException {
        log.info("createOrUpdateDefaultBrandConfig defaultBrandConfig:{}", Jackson.getObjectMapper().writeValueAsString(defaultBrandConfig));
        if (defaultBrandConfig == null){
            return;
        }
        brandConfigService.saveOrUpdateDefaultBrandConfig(defaultBrandConfig);
    }

    @DeleteMapping(value = "/internal/{version}/{en}/nemo/brand/config/delete")
    public void removeDefaultBrandModelConfig(@RequestParam String brand, @RequestParam String configType,
                                       @RequestParam(value = "brandModel",required = false) String brandModel, @RequestParam String configName,
                                       @RequestParam String clientConfigName){
        log.info("configBrand:{}, configType:{}, brandModel:{}, configName:{}, clientConfigName:{}",
                brand,configType,brandModel,configName,clientConfigName);
        brandConfigService.removeDefaultBrandConfig(brand,brandModel, StringUtils.isNoneBlank(configType)?Integer.parseInt(configType):0,configName,clientConfigName);
    }
}
