package com.xylink.configserver.controller;

import com.xylink.configserver.data.dto.SubtypeExportImportDto;
import com.xylink.configserver.data.model.NewDeviceBuildInfoRequest;
import com.xylink.configserver.data.response.R;
import com.xylink.configserver.service.DeviceBuildAssistantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * ClassName:DeviceBuildAssistantController
 * Package:com.xylink.configserver.controller
 * Description:
 *
 * <AUTHOR>
 * @Date 2024/10/17-14:55
 * @Version: v1.0
 */
@Slf4j
@RestController
public class DeviceBuildAssistantController {
    @Autowired
    private DeviceBuildAssistantService deviceBuildAssistantService;

    @PostMapping("/clientConfg/internal/deviceBuildAssistant/v1")
    public String newDeviceBuildReturn(@RequestBody NewDeviceBuildInfoRequest request, @RequestParam Integer subtype) {

        return deviceBuildAssistantService.newDeviceBuildReturn(request, subtype);
    }


    @GetMapping("/clientConfg/internal/subtypeBase/export/v1")
    public SubtypeExportImportDto subtypeBaseExport(@RequestParam Integer subtype) {

        return deviceBuildAssistantService.subtypeBaseExport(subtype);
    }

    @PostMapping ("/clientConfg/internal/subtypeBase/import/v1")
    public R<String> subtypeBaseImport(@RequestBody SubtypeExportImportDto subtypeExportImportDto) {

        return deviceBuildAssistantService.subtypeBaseImport(subtypeExportImportDto);
    }
}
