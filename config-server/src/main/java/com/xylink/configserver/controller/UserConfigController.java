package com.xylink.configserver.controller;

import com.xylink.configserver.aspect.PathVariableSecurityCheck;
import com.xylink.configserver.aspect.PathVariableSecurityCheckField;
import com.xylink.configserver.controller.base.BaseController;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.service.UserConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@Slf4j
public class UserConfigController extends BaseController {

    @Autowired
    UserConfigService userConfigService;

    @GetMapping("/{version}/{en}/user/{userId}/config")
    @PathVariableSecurityCheck
    public Map<String, String> getUserConfig(@PathVariableSecurityCheckField(field = "userId") @PathVariable long userId) {
        log.info("RP-UserConfigController-getUserConfig-userId:{}", userId);
        if (userId <= 0) {
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER);
        }
        return userConfigService.getCombinedConfig(userId, null);
    }

    @GetMapping("/clientConfg/internal/{en}/user/{userId}/configs/v1")
    public Map<String, String> getUserConfigByType(@PathVariable long userId,
                                                 @RequestParam Integer deviceType) {
        log.info("RP-UserConfigController-getUserPartConfig-userId:{}, deviceType:{}", userId, deviceType);

        return userConfigService.getCombinedConfig(userId, deviceType);
    }

}
