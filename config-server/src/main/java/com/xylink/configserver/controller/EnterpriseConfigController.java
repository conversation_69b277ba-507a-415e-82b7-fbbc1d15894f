package com.xylink.configserver.controller;

import com.xylink.configserver.controller.base.BaseController;
import com.xylink.configserver.data.dto.BaseConfigDto;
import com.xylink.configserver.service.EnterpriseConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-07-15 09:24
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class EnterpriseConfigController extends BaseController {

    private final EnterpriseConfigService enterpriseConfigService;

    /**
     * 只查询默认配置和企业维度配置
     *
     * @param userId userId
     * @return config
     */
    @PostMapping("/clientConfg/external/enterpriseConfig/v4")
    public Map<String, Map<String, Object>> getEnterpriseConfigV4(@RequestParam Long userId,
            @RequestParam Integer deviceType,
            @RequestBody(required = false) List<BaseConfigDto> requiredConfigList) {

        log.info("RP-EnterpriseConfigController-getEnterpriseConfig:userId={},deviceType={},requiredConfigList={}",
                userId, deviceType, requiredConfigList);

        return enterpriseConfigService.getEnterpriseConfig(requiredConfigList, userId, deviceType);

    }

    /**
     * 查询企业维度配置，缺省取默认
     */
    @PostMapping("/clientConfg/internal/enterprise-config/v5")
    public Map<String, Map<String, Object>> getEnterpriseConfigV5(
            @RequestParam String enterpriseId,
            @RequestParam Integer configType,
            @RequestBody(required = false) List<BaseConfigDto> requiredConfigList) {

        log.info("RP-EnterpriseConfigController-getEnterpriseConfig:enterpriseId={},deviceType={},requiredConfigList={}",
                 enterpriseId, configType, requiredConfigList);

        return enterpriseConfigService.getEnterpriseConfig(enterpriseId, configType, requiredConfigList);

    }

    /**
     * 提供manager扩展使用，增加对单台sn 设备的配置查询
     */
    @PostMapping("/clientConfg/internal/enterprise-config/v6")
    public Map<String, Map<String, Object>> getEnterpriseConfigV6(
            @RequestParam String enterpriseId,
            @RequestParam String sn,
            @RequestParam(required = false) Integer configType,
            @RequestBody(required = false) List<BaseConfigDto> requiredConfigList) {

        log.info(
                "RP-EnterpriseConfigController-getEnterpriseConfig:enterpriseId={},sn={},configType={},requiredConfigList={}",
                enterpriseId, sn, configType, requiredConfigList);
        // sn为空检验
        if (!StringUtils.hasText(sn)) {
            throw new IllegalArgumentException("sn is required");
        }
        return enterpriseConfigService.getEnterpriseAndMergeNemoConfig(enterpriseId, sn, configType,
                requiredConfigList);

    }

}