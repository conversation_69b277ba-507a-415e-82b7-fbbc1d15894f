package com.xylink.configserver.controller;

import com.xylink.configserver.service.HealthCareService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/23 11:08
 */
@RestController
@RequestMapping("/clientConfg/internal/health-care")
@RequiredArgsConstructor
public class HealthCareController {

    private final HealthCareService healthCareService;

    @GetMapping("/redis")
    public List<String> checkRedisHealthState() {

        return healthCareService.checkRedisState();

    }

}
