package com.xylink.configserver.controller;

import com.xylink.configserver.controller.base.BaseController;
import com.xylink.configserver.data.model.GwDevice;
import com.xylink.configserver.enums.DeviceType;
import com.xylink.configserver.service.DeviceConfigService;
import com.xylink.configserver.service.DeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020-05-23 15:03
 */
@Slf4j
@RestController
public class H323ConfigController extends BaseController {

    @Resource
    private DeviceConfigService deviceConfigService;
    @Resource
    private DeviceService deviceService;

    @GetMapping("/clientConfg/{version}/{en}/h323/configs")
    public Map<String, String> getDeviceCap(@RequestParam("securityKey") String securityKey, @RequestParam("deviceType") int deviceType) {
        GwDevice.GwType gwType = GwDevice.GwType.UNKNOWN;
        if (DeviceType.GW_H323.getValue() == deviceType) {
            gwType = GwDevice.GwType.H323;
        } else if (DeviceType.GW_H323_HYGW.getValue() == deviceType) {
            gwType = GwDevice.GwType.HYGW;
        }
        GwDevice gwDevicePO = deviceService.getGwDeviceBySkAndType(securityKey, gwType);
        return deviceConfigService.getCombinedH323Config(gwDevicePO);
    }
}
