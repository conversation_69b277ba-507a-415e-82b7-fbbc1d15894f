package com.xylink.configserver.controller;


import com.baomidou.mybatisplus.core.toolkit.AES;
import com.xylink.configserver.controller.base.BaseController;
import com.xylink.configserver.data.model.ConditionEntity;
import com.xylink.configserver.data.model.ContainerConfigTableModel;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.data.vo.InquireHelperVO;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.service.ConfigInquireHelperService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;


/**
 * 内部使用,硬终端配置项速查
 *该类为辅助工具类,速查配置生效表
 **/
@RestController
public class ConfigInquireHelperController extends BaseController {

    private static final String key = "xylinkjcfwzzxcvb";

    @Autowired
    private ConfigInquireHelperService configInquireHelperService;


    /**
     * 查询分层配置生效维度
     */
    @PostMapping(path = "/clientConfg/internal/v1/device/inquire/configs")
    public ContainerConfigTableModel inquireConfig(@RequestBody InquireHelperVO inquireHelperVO){
        if (ObjectUtils.isEmpty(inquireHelperVO) ||
                (inquireHelperVO.getQueryType() == 0 && (ObjectUtils.isEmpty(inquireHelperVO.getUserDeviceId()))&& ObjectUtils.isEmpty(inquireHelperVO.getDeviceSn()))
                ||
                (inquireHelperVO.getQueryType() == 1 && ObjectUtils.isEmpty(inquireHelperVO.getUserProfileId()))) {
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_DEVICE_ID);
        }
        return configInquireHelperService.inquireConfig(inquireHelperVO);
    }


    @PostMapping(path = "/clientConfg/internal/v1/device/inquire/condition")
    public List<Map<String, Object>> selectCondition(@RequestBody ConditionEntity condition){
        if (ObjectUtils.isEmpty(condition) || ObjectUtils.isEmpty(condition.getCondition())) {
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_DEVICE_ID);
        }
        String sql = condition.getCondition();
        String decrypt = AES.decrypt(sql, key);
        return configInquireHelperService.selectCondition(decrypt);

    }

}