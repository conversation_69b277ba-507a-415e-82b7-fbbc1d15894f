package com.xylink.configserver.controller;


import com.xylink.configserver.data.dto.DeptChangeDTO;
import com.xylink.configserver.data.model.DeptConfigPO;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.service.DeptConfigService;
import com.xylink.configserver.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/clientConfg/internal/enterprise/dept/config")
public class DeptConfigController {

    @Autowired
    private DeptConfigService deptConfigService;


    /**
     * 修改部门配置: H5 -> buffet -> clientConfig
     */
    @PostMapping("/change")
    public void change(@RequestBody DeptChangeDTO changeDTO){
        if (Objects.isNull(changeDTO) || StringUtils.isBlank(changeDTO.getDeptId()) || CollectionUtil.isEmpty(changeDTO.getConfigs())){
            throw new ServiceException("invalid param", ErrorStatus.INVALID_PARAMETER);
        }
        log.info("==========> update dept config:{}", changeDTO);
        deptConfigService.changeConfig(changeDTO);
    }

    /**
     * 删除部门配置: 这里不再发消息
     */
    @PostMapping("/delete")
    public void delete(@RequestParam(required = false, defaultValue = "default_enterprise") String enterpriseId,
                       @RequestParam(required = false) String configName,
                       @RequestParam String deptId){
        if (StringUtils.isBlank(deptId)){
            throw new ServiceException("invalid param", ErrorStatus.INVALID_PARAMETER);
        }
        log.info("==========> delete dept config, enterpriseId:{}, deptId:{}, configName:{}",
                enterpriseId, deptId, configName);
        deptConfigService.deleteByDeptId(enterpriseId, deptId, configName);
    }


    /**
     * 查询部门配置(只返回部门维度配置, 不做配置覆盖, buffet自己维护一份default值返回给前端)
     */
    @GetMapping("/query")
    public List<DeptConfigPO> query(@RequestParam(required = false, defaultValue = "default_enterprise") String enterpriseId,
                                    @RequestParam(required = false) String deptId){
        return deptConfigService.getSourceDeptConfig(enterpriseId, deptId);
    }

}
