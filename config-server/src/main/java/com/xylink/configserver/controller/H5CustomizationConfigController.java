package com.xylink.configserver.controller;

import com.xylink.configserver.controller.base.BaseController;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.service.EnterpriseNemoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
public class H5CustomizationConfigController extends BaseController {

    @Autowired
    EnterpriseNemoService enterpriseNemoService;

    @GetMapping("/internal/{version}/enterprise/h5config")
    public Map<String, String> getEnterpriseH5CustomizationConfig(@RequestParam(value = "userId",required = false) String userId,
                                                                  @RequestParam(value = "deviceSN",required = false) String deviceSN,
                                                                  @RequestParam(value = "enterpriseId",required = false) String enterpriseId,
                                                                  @RequestParam(value = "distributorId",required = false) String distributorId) {
        if(StringUtils.isNotBlank(enterpriseId)) {
            return enterpriseNemoService.getEnterpriseH5Customization(enterpriseId, distributorId);
        } else if(StringUtils.isNotBlank(userId)) {
            long userProfileId = Long.parseLong(userId);
            return enterpriseNemoService.getUserEnterpriseH5Customization(userProfileId);
        } else if(StringUtils.isNotBlank(deviceSN)) {
            return enterpriseNemoService.getEnterpriseH5Customization(deviceSN);
        } else {
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST,ErrorStatus.INVALID_PARAMETER);
        }
    }


    @GetMapping("/clientConfg/internal/{version}/enterprise/smsTemplate")
    public Map<String, String> getEnterpriseSmsTemplateCustomizationConfig(@RequestParam(value = "userId",required = false) String userId,
                                                                  @RequestParam(value = "enterpriseId",required = false) String enterpriseId,
                                                                  @RequestParam(value = "distributorId",required = false) String distributorId,
                                                                  @RequestParam(value = "lang",required = false, defaultValue = "zh_CN") String lang) {
        if(StringUtils.isNotBlank(enterpriseId)) {
            return enterpriseNemoService.getEnterpriseSmsTemplateCustomization(enterpriseId, distributorId, lang);
        } else if(StringUtils.isNotBlank(userId)) {
            long userProfileId = Long.parseLong(userId);
            return enterpriseNemoService.getUserEnterpriseSmsTemplateCustomization(userProfileId , lang);
        } else {
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST,ErrorStatus.INVALID_PARAMETER);
        }
    }

}
