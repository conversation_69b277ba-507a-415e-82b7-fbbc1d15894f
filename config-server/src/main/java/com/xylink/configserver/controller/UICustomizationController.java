package com.xylink.configserver.controller;

import com.xylink.configserver.aspect.PathVariableSecurityCheck;
import com.xylink.configserver.aspect.PathVariableSecurityCheckField;
import com.xylink.configserver.controller.base.BaseController;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.service.UICustomizationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class UICustomizationController extends BaseController {

    @Autowired
    UICustomizationService uiCustomizationService;

    @GetMapping(value = "/{version}/{en}/devices/{deviceId}/uicustomization",produces="application/json;charset=UTF-8")
    @PathVariableSecurityCheck
    public String getUICustomizationCap(@PathVariableSecurityCheckField(field = "deviceId") @PathVariable long deviceId){
        if (deviceId <= 0){
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER);
        }
        return uiCustomizationService.getUICustomization(deviceId);
    }

    @GetMapping(value = "/internal/{version}/{en}/devices/{deviceId}/uicustomization",produces="application/json;charset=UTF-8")
    public String getInternalUiCustomizationCap(@PathVariable long deviceId){
        if (deviceId <= 0){
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER);
        }
        return uiCustomizationService.getInternalUiCustomization(deviceId);
    }
}
