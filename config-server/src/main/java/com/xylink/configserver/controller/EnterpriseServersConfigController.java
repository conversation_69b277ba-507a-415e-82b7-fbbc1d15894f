package com.xylink.configserver.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xylink.configserver.controller.base.BaseController;
import com.xylink.configserver.data.dto.ExcludeIncludeServerConfigDto;
import com.xylink.configserver.data.model.DefaultServerConfig;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.service.EnterpriseServersConfigService;
import com.xylink.configserver.util.Jackson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
public class EnterpriseServersConfigController extends BaseController {

    private final EnterpriseServersConfigService enterpriseServersConfigService;

    @GetMapping({"/clientConfg/{version}/getDeviceServerConfig","/clientConfg/{version}/getUserServerConfig"})
    public Map<String,Object> getServerList() {
        try {
            return enterpriseServersConfigService.getServerConfigs();
        }catch(ServiceException e) {
            log.error("Fail to getServerList", e);
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST,e.getStatus());
        }catch(Exception e) {
            log.error("Unexpected exception when getServerList.", e);
            throw getRestException(HttpStatus.SERVER_ERROR_INTERNAL,ErrorStatus.INTERNAL_ERROR);
        }
    }

    @GetMapping({"/clientConfg/internal/{version}/getDeviceServerConfig"})
    public Map<String,Object> getInternalDeviceServerConfig(@RequestParam(value="deviceId") long deviceId,@RequestParam(value="configName",required=false) String configName) {
        try {
            return enterpriseServersConfigService.getInternalDeviceServerConfig(deviceId,configName);
        }catch(ServiceException e) {
            log.error("Fail to getServerList", e);
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST,e.getStatus());
        }catch(Exception e) {
            log.error("Unexpected exception when getServerList.", e);
            throw getRestException(HttpStatus.SERVER_ERROR_INTERNAL,ErrorStatus.INTERNAL_ERROR);
        }
    }

    @GetMapping({"/clientConfg/internal/{version}/getUserServerConfig"})
    public Map<String,Object> getInternalUserServerConfig(@RequestParam(value="userId") long userId,@RequestParam(value="configType",required=false,defaultValue = "1") int configType,@RequestParam(value="configName",required=false) String configName) {
        try {
            return enterpriseServersConfigService.getInternalUserServerConfig(userId,configType,configName);
        }catch(ServiceException e) {
            log.error("Fail to getServerList", e);
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST,e.getStatus());
        }catch(Exception e) {
            log.error("Unexpected exception when getServerList.", e);
            throw getRestException(HttpStatus.SERVER_ERROR_INTERNAL,ErrorStatus.INTERNAL_ERROR);
        }
    }

    @GetMapping(value = {"/clientConfg/{version}/getDefaultServerConfig"})
    public Map<String, Object> getServerConfig(@RequestParam(value = "configName", required = false) String configName) {
        try {
            return enterpriseServersConfigService.getDefaultServerConfig(configName);
        } catch (ServiceException e) {
            log.error("Fail to getServerList", e);
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, e.getStatus());
        } catch (Exception e) {
            log.error("Unexpected exception when getServerList.", e);
            throw getRestException(HttpStatus.SERVER_ERROR_INTERNAL, ErrorStatus.INTERNAL_ERROR);
        }
    }

    @PostMapping(value = {"/clientConfg/external/serverConfig/default/v1", "/clientConfg/internal/serverConfig/default/v1"})
    public Map<String, Object> getServerConfigBatch(@RequestBody ExcludeIncludeServerConfigDto excludeIncludeServerConfigDto) {

        log.info("RP-EnterpriseServersConfigController-getServerConfigBatch:{}", excludeIncludeServerConfigDto);

        return enterpriseServersConfigService.getDefaultServerConfigBatchByExcInc(excludeIncludeServerConfigDto);
    }

    @GetMapping(value = {"/clientConfg/wx/{version}/getDefaultServerConfig"})
    public Map<String, Object> getWxServerConfig(@RequestParam(value = "configName", required = false) String configName) {
        try {
            return enterpriseServersConfigService.getDefaultServerConfig(configName);
        } catch (ServiceException e) {
            log.error("Fail to wx getServerList", e);
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, e.getStatus());
        } catch (Exception e) {
            log.error("Unexpected exception when getWxServerList.", e);
            throw getRestException(HttpStatus.SERVER_ERROR_INTERNAL, ErrorStatus.INTERNAL_ERROR);
        }
    }

    @GetMapping("/serverConfig/init")
    public void init() {
        enterpriseServersConfigService.initDefaultServerConfig();
    }

    @PostMapping("/clientConfg/internal/{version}/serverConfig")
    public List<DefaultServerConfig> updateDefaultServerConfig(@RequestBody List<DefaultServerConfig> defaultServerConfigs) {

        try {
            log.info("Update default server config request : {}", Jackson.getObjectMapper().writeValueAsString(defaultServerConfigs));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }

        return enterpriseServersConfigService.saveOrUpdateBatch(defaultServerConfigs);

    }
}
