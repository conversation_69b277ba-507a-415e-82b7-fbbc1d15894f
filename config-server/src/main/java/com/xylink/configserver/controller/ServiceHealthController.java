package com.xylink.configserver.controller;

import com.xylink.configserver.data.dto.ServiceInspectionDTO;
import com.xylink.configserver.service.ServiceHealthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
public class ServiceHealthController {

    private final ServiceHealthService serviceHealthService;

    @GetMapping("/health")
    public String health() {
        return "ok";
    }

    @GetMapping("/internal/inspection/v1")
    public List<ServiceInspectionDTO> inspection() {

        return serviceHealthService.inspect();

    }

}
