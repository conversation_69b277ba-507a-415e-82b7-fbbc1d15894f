package com.xylink.configserver.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.data.model.ServerEndpointConfigReq;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.RestException;
import com.xylink.configserver.service.LibraServerEndpoinConfigService;
import com.xylink.configserver.util.Jackson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @FilePath:com.xylink.configserver.controller.ServerEndpointConfigController
 * @Description: TODO
 */

@Slf4j
@RestController
public class ServerEndpointConfigController {

    @Autowired
    private LibraServerEndpoinConfigService libraServerEndpoinConfigService;

    /**
     * 查询服务终端配置
     * @param serverEndpointConfigReq
     * @return
     */
    @PostMapping("/clientConfg/internal/endpoint/configs/v1")
    public Map findServerEndpoinConfigs(@RequestBody ServerEndpointConfigReq serverEndpointConfigReq) {
        try {
            log.info("findServerEndpoinConfigs:{}", Jackson.getObjectMapper().writeValueAsString(serverEndpointConfigReq));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        checkParam(serverEndpointConfigReq);
        if (Objects.isNull(serverEndpointConfigReq.getDeviceIds()) && Objects.isNull(serverEndpointConfigReq.getUserIds()) && Objects.isNull(serverEndpointConfigReq.getTelephones()) && Objects.isNull(serverEndpointConfigReq.getDeviceNumbers())){
            log.info("deviceIds and userIds and tels and numbers is null");
            ErrorStatus errorStatus = ErrorStatus.INVALID_PARAMETER;
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, errorStatus.getErrorCode(),
                    errorStatus.getResId());
        }
        int total = Arrays.asList(serverEndpointConfigReq.getDeviceIds(),serverEndpointConfigReq.getUserIds(),serverEndpointConfigReq.getDeviceNumbers(),serverEndpointConfigReq.getTelephones()).stream().filter(list -> Objects.nonNull(list)).mapToInt(List::size).sum();
        if (total>100){
            log.info("tolal exceed 100");
            ErrorStatus errorStatus = ErrorStatus.INVALID_PARAMETER;
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, errorStatus.getErrorCode(),
                    errorStatus.getResId());
        }
        return libraServerEndpoinConfigService.findServerEndpoinConfigs(serverEndpointConfigReq);
    }

    /**
     * 添加或修改用户id、设备id服务终端配置
     *
     * @param serverEndpointConfigReq
     */
    @PostMapping("/clientConfg/internal/endpoint/configs/saveOrUpdate/v1")
    public void addOrUpdateServerEndpoinConfig(@RequestBody ServerEndpointConfigReq serverEndpointConfigReq) {
        try {
            log.info("addOrUpdateServerEndpoinConfig:{}", Jackson.getObjectMapper().writeValueAsString(serverEndpointConfigReq));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        checkParam(serverEndpointConfigReq);
        if (Objects.isNull(serverEndpointConfigReq.getDeviceIds()) && Objects.isNull(serverEndpointConfigReq.getUserIds())){
            log.info("deviceIds and userIds is null");
            ErrorStatus errorStatus = ErrorStatus.INVALID_PARAMETER;
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, errorStatus.getErrorCode(),
                    errorStatus.getResId());
        }
        if (StringUtils.isBlank(serverEndpointConfigReq.getConfigValue())){
            log.info("configvalue is null");
            ErrorStatus errorStatus = ErrorStatus.INVALID_PARAMETER;
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, errorStatus.getErrorCode(),
                    errorStatus.getResId());
        }
        libraServerEndpoinConfigService.addOrUpdateServerEndpoinConfig(serverEndpointConfigReq);
    }

    private void checkParam(ServerEndpointConfigReq serverEndpointConfigReq){
        if (Objects.isNull(serverEndpointConfigReq)){
            log.info("serverEndpointConfigReq is null");
            ErrorStatus errorStatus = ErrorStatus.INVALID_PARAMETER;
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, errorStatus.getErrorCode(),
                    errorStatus.getResId());
        }
        if (StringUtils.isBlank(serverEndpointConfigReq.getConfigName())){
            log.info("configname is null");
            ErrorStatus errorStatus = ErrorStatus.INVALID_PARAMETER;
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, errorStatus.getErrorCode(),
                    errorStatus.getResId());
        }
    }

}
