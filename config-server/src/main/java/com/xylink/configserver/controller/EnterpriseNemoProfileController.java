package com.xylink.configserver.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xylink.configserver.controller.base.BaseController;
import com.xylink.configserver.data.enums.ResultCodeEnum;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.RestException;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.service.EnterpriseNemoProfileService;
import com.xylink.configserver.util.Jackson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
public class EnterpriseNemoProfileController extends BaseController {

    @Autowired
    EnterpriseNemoProfileService enterpriseNemoProfileService;

    @GetMapping("/internal/{version}/{en}/enterprisenemo/profileV2")
    public EnterpriseNemoProfileUpdateReq getEnterpriseProfile(@RequestParam(value="enterpriseId") String enterpriseId) {
        return enterpriseNemoProfileService.getEnterpriseProfile(enterpriseId);
    }

    @GetMapping("/clientConfg/internal/{version}/{en}/enterprisenemo/profileV2")
    public List<EnterpriseNemoConfig> getSimpleEnterpriseProfile(@RequestParam(value = "enterpriseId") String enterpriseId, @RequestParam(name = "configName", required = false) String configName) {
        return enterpriseNemoProfileService.getSimpleConfigsByEnterpriseId(enterpriseId,configName);
    }

    @GetMapping("/clientConfg/internal/{version}/{en}/enterprisenemoprofile")
    public EnterpriseNemoProfile getEnterpriseProfileOnly(@RequestParam(value = "enterpriseId", required = false) String enterpriseId, @RequestParam(value = "customizedKey", required = false) String customizedKey) {
        if (StringUtils.isNotBlank(enterpriseId)){
            return enterpriseNemoProfileService.getByEnterpriseId(enterpriseId);
        }else if (StringUtils.isNotBlank(customizedKey)){
            return enterpriseNemoProfileService.getByCustomizedKey(customizedKey);
        }
        return null;
    }

    @DeleteMapping("/internal/{version}/{en}/enterprisenemo/profileV2")
    public void  deleteEnterpriseNemoProfileConfig(@RequestBody EnterpriseNemoProfileUpdateReq req){
        try {
            log.info("deleteEnterpriseNemoProfileConfig EnterpriseNemoProfileUpdateReq:{}", Jackson.getObjectMapper().writeValueAsString(req));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        if(req == null) {
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER);
        }
        if(req.getConfigs() != null) {
            try {
                log.info("notify remove device config from enterprise profile :" + req.getProfileId());
                enterpriseNemoProfileService.removeEnterpriseProfileConfig(req);
            } catch (ServiceException e) {
                log.error("Failed to apply enterprise profile config.", e);
            }
        }
    }

    @PutMapping("/internal/{version}/{en}/enterprisenemo/profileV2")
    public void updateEnterpriseProfile(@RequestBody EnterpriseNemoProfileUpdateReq req) {
        try {
            log.info("updateEnterpriseProfile EnterpriseNemoProfileUpdateReq:{}",Jackson.getObjectMapper().writeValueAsString(req));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        if(req == null) {
            throw this.getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER);
        }
        String profileId = req.getProfileId();
        String enterpriseId = req.getEnterpriseId();
        if (StringUtils.isBlank(profileId) && StringUtils.isBlank(enterpriseId)) {
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ResultCodeEnum.INVALID_PARAMETER.getCode(), "无效企业");
        } else if (StringUtils.isBlank(profileId) && StringUtils.isNotBlank(enterpriseId)) {
            EnterpriseNemoProfile profile = enterpriseNemoProfileService.getByEnterpriseId(enterpriseId);
            if (profile == null) {
                throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ResultCodeEnum.INVALID_PARAMETER.getCode(), "企业未关联profile");
            }
            profileId = profile.getId();
        }

        if(req.getEnterpriseId() != null && req.getProfile() != null) {
            //called by buffet
            profileId = enterpriseNemoProfileService.updateEnterpriseNemoProfileByEnterpriseId(req.getEnterpriseId().trim(), req.getProfile());
        } else if(req.getProfile() != null) {
            //called by nconsole
            try {
                profileId = enterpriseNemoProfileService.updateEnterpriseNemoProfileByProfileId(profileId, req.getProfile());
            }
            catch (ServiceException e) {
                throw this.getRestException(HttpStatus.SERVER_ERROR_INTERNAL, e.getStatus());
            }
        }

        if(req.getConfigs() != null) {
            try {
                log.info("apply enterprise profie config" + profileId);
                enterpriseNemoProfileService.applyEnterpriseProfileConfig(req.getConfigs(), profileId);
            } catch (ServiceException e) {
                log.error("Failed to apply enterprise profile config.", e);
            }
        }

        if(req.getFeatures() != null && !req.getFeatures().isEmpty()) {
            enterpriseNemoProfileService.applyEnterpriseProfileFeature(req.getFeatures(), profileId);
        }
    }

    @PutMapping("/clientConfg/internal/{version}/enterprisenemo/profileV3")
    public void updateEnterpriseProfileV2(@RequestBody EnterpriseNemoProfileFieldUpdateReq req) {
        try {
            log.info("updateEnterpriseProfile EnterpriseNemoProfileFieldUpdateReq:{}",Jackson.getObjectMapper().writeValueAsString(req));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        if(req == null) {
            throw this.getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER);
        }
        enterpriseNemoProfileService.updateEnterpriseNemoProfileByFieldUpdate(req);
    }

    @GetMapping("/internal/{version}/enterprisenemo/profile")
    public Map<String, String> getEnterpriseConfigs(@RequestParam(value="enterpriseId") String enterpriseId) {
        return enterpriseNemoProfileService.getEnterpriseConfig(enterpriseId);
    }

    @PutMapping("/internal/{version}/enterprisenemo/profile")
    public void updateEnterpriseProfle(@RequestBody EnterpriseNemoProfileUpdateReqV1 req) {
        try {
            log.info("Update enterprise nemo profile,updateEnterpriseProfle:{}", Jackson.getObjectMapper().writeValueAsString(req));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        if(req == null) {
            throw this.getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER);
        }
        String profileId = req.getProfileId();
        if(req.getEnterpriseId() != null && req.getProfile() != null) {
            //called by buffet
            profileId = enterpriseNemoProfileService.updateEnterpriseNemoProfileByEnterpriseId(req.getEnterpriseId().trim(), req.getProfile());
        } else if(req.getProfile() != null) {
            //called by nconsole
            profileId = enterpriseNemoProfileService.updateEnterpriseNemoProfileByProfileId(profileId, req.getProfile());
        }

        if(req.getConfigs() != null) {
            try {
                enterpriseNemoProfileService.applyEnterpiseProfileConfig(req.getConfigs(), profileId);
            } catch (ServiceException e) {
                log.error("Failed to apply enterprise profile config.", e);
            }
        }

        if(req.getFeatures() != null) {
            enterpriseNemoProfileService.applyEnterpriseProfileFeature(req.getFeatures(), profileId);
        }
    }
}
