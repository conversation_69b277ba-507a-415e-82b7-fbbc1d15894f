package com.xylink.configserver.controller;

import com.xylink.configserver.data.model.FetchDeviceDefaultModel;
import com.xylink.configserver.service.FetchDeviceDefaultValuesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * ClassName:FetchDeviceDefaultValusController
 * Package:com.xylink.configserver.controller
 * Description:
 * 该类主要实现生成单个subtype的所有默认配置，是一个工具类。
 * 主要为动态配置提供前期工作作辅助。
 * <AUTHOR>
 * @Date 2025/2/11-15:05
 * @Version: v1.0
 */
@Slf4j
@RestController
public class FetchDeviceDefaultValuesController {

    @Autowired
    private FetchDeviceDefaultValuesService fetchDeviceDefaultValuesService;

    @GetMapping("/clientConfg/internal/fetchSubtypeDefault/v1")
    public Map<String, Map<String,String>> fetchDeviceDefaultValues(String subtype){

        return fetchDeviceDefaultValuesService.fetchDeviceDefaultValues(subtype);
    }


    @GetMapping("/clientConfg/internal/fetchAllSubtypeDefault/v1")
    public List<FetchDeviceDefaultModel> fetchAllSubtypeDefault(){

        return fetchDeviceDefaultValuesService.fetchAllSubtypeDefault();
    }
}
