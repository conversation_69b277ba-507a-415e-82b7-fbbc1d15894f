package com.xylink.configserver.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xylink.configserver.controller.base.BaseController;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.data.model.deviceseries.*;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.service.deviceseries.*;
import com.xylink.configserver.util.Jackson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.EnumUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-18 14:41
 */
@Slf4j
@RestController
@RequestMapping("/clientConfg/internal")
public class DeviceSeriesSubtypeController extends BaseController {
    @Resource
    private DeviceSubtypeService deviceSubtypeService;
    @Resource
    private DeviceSeriesService deviceSeriesService;
    @Resource
    private DeviceSubtypeSeriesConfigDictionaryService deviceSubtypeSeriesConfigDictionaryService;
    @Resource
    private EnterpriseDeviceSeriesService enterpriseDeviceSeriesService;

    /**
     * 终端列表
     *
     * @param key
     * @param pageSize
     * @param page
     * @return
     */
    @GetMapping("/{version}/{en}/subtypes")
    public Page<DeviceSubtypeListDto> subtypes(@RequestParam(name = "key", required = false) String key, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, @RequestParam(name = "page", defaultValue = "1") Integer page) {
        page = page <= 0 ? 1 : page;
        pageSize = pageSize <= 0 ? 10 : pageSize;
        Page<DeviceSubtypeListDto> pager = new Page<>();
        pager.setCurrent(page);
        pager.setSize(pageSize);
        return deviceSubtypeService.page(pager, key);
    }

    /**
     * 终端详情
     *
     * @param subtype
     * @return
     */
    @GetMapping("/{version}/{en}/subtypes/{subtype}/info")
    public DeviceSubtypeDetailDto subtypeDetail(@PathVariable("subtype") Integer subtype) {
        if (subtype <= 0) {
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER);
        }
        return deviceSubtypeService.detail(subtype);
    }

    /**
     * 系列列表
     *
     * @return
     */
    @GetMapping("/{version}/{en}/series")
    public List<DeviceSeriesDto> series() {
        return deviceSeriesService.getAllSeries();
    }

    /**
     * 系列详情
     *
     * @param seriesId
     * @return
     */
    @GetMapping("/{version}/{en}/series/{seriesId}/info")
    public DeviceSeriesDto seriesDetail(@PathVariable("seriesId") Long seriesId, @RequestParam(name = "includeNotSelectedConfig", defaultValue = "true") boolean includeNotSelectedConfig) {
        if (seriesId <= 0) {
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER);
        }
        DeviceSeriesDto series = deviceSeriesService.getDeviceSeriesDto(seriesId);
        if (!includeNotSelectedConfig) {
            series.setConfigs(ConfigDictionaryUtils.excludeNotSelectedTree(series.getConfigs()));
        }
        return series;
    }

    /**
     * 系列可选配置项
     *
     * @return
     */
    @GetMapping("/{version}/{en}/series/configs/dictionary")
    public List<DeviceSeriesSubtypeConfigsDto> seriesConfigsDictionary() {
        return deviceSubtypeSeriesConfigDictionaryService.getTreeSeriesConfigDictionary();
    }

    /**
     * 新增或者修改系列
     *
     * @param deviceSeriesDto
     */
    @PostMapping("/{version}/{en}/series/configs")
    public void seriesConfigsSaveOrUpdate(@Validated @RequestBody DeviceSeriesDto deviceSeriesDto) {
        try {
            log.info("Update series configs.{}", Jackson.getObjectMapper().writeValueAsString(deviceSeriesDto));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        deviceSeriesService.saveOrUpdateSeries(deviceSeriesDto);
    }

    /**
     * 新增或者修改终端配置
     *
     * @param deviceSubtypeDetailUpdateDto
     */
    @PostMapping("/{version}/{en}/subtype/configs")
    public void subtypeConfigsSaveOrUpdate(@Validated @RequestBody DeviceSubtypeDetailUpdateDto deviceSubtypeDetailUpdateDto) {
        try {
            log.info("Update subtype configs.{}", Jackson.getObjectMapper().writeValueAsString(deviceSubtypeDetailUpdateDto));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        deviceSubtypeService.saveOrUpdateDeviceSubtype(deviceSubtypeDetailUpdateDto);
    }

    /**
     * 获取企业系列配置项
     *
     * @param enterpriseId
     * @param seriesId
     * @return
     */
    @GetMapping("/{version}/{en}/enterprise/series/configs")
    public EnterpriseDeviceSeriesConfigDto enterpriseSeriesConfigs(@RequestParam("enterpriseId") String enterpriseId, @RequestParam("seriesId") Long seriesId) {
        log.info("Query enterprise:{} seriesId:{} configs", enterpriseId, seriesId);
        return enterpriseDeviceSeriesService.queryEnterpriseDeviceSeriesConfigs(enterpriseId, seriesId);
    }

    /**
     * 修改企业系列配置项
     *
     * @param enterpriseDeviceSeriesConfigUpdateDto
     */
    @PostMapping("/{version}/{en}/enterprise/series/configs")
    public void enterpriseSeriesConfigs(@Validated @RequestBody EnterpriseDeviceSeriesConfigUpdateDto enterpriseDeviceSeriesConfigUpdateDto) {
        try {
            log.info("Change enterprise configs:{}", Jackson.getObjectMapper().writeValueAsString(enterpriseDeviceSeriesConfigUpdateDto));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        enterpriseDeviceSeriesService.updateEnterpriseDeviceSeriesConfigs(enterpriseDeviceSeriesConfigUpdateDto);
    }

    /**
     * 管理平台获取终端的配置项
     *
     * @param deviceId
     * @return
     */
    @GetMapping("/{version}/{en}/devices/configs")
    public DeviceSubtypeDto getDeviceConfigs(@RequestParam("deviceId") long deviceId, @RequestParam("platform") String platform) {
        log.info("Query device:{} configs by platform:{}", deviceId, platform);
        PlatformEnum platformEnum = EnumUtils.getEnum(PlatformEnum.class, platform);
        if (platformEnum == null) {
            throw this.getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER);
        }
        return deviceSubtypeService.getByDeviceId(deviceId, platformEnum);
    }


}
