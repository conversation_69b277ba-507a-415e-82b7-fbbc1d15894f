package com.xylink.configserver.controller;

import com.xylink.configserver.controller.base.BaseController;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.service.EnterpriseNemoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
public class ClientCustomizationController extends BaseController {

    @Autowired
    EnterpriseNemoService enterpriseNemoService;

    @GetMapping("/{version}/{en}/clientcustomization")
    public Map<String, String> getClientCustomization(@RequestParam String customizedkey,@RequestParam String deviceType) {
        if(deviceType == null) {
            return new HashMap<>();
        }
        try {
            return enterpriseNemoService.getEnterpriseUICustomization(customizedkey, Integer.parseInt(deviceType));
        } catch (Exception e) {
            throw getRestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST,ErrorStatus.INVALID_PARAMETER);
        }
    }
}
