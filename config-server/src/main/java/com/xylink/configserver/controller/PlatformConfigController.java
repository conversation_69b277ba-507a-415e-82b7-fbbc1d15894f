package com.xylink.configserver.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xylink.configserver.controller.base.BaseController;
import com.xylink.configserver.data.model.NemoDefaultPlatformConfig;
import com.xylink.configserver.service.PlatformConfigService;
import com.xylink.configserver.util.Jackson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-05-06 21:44
 */
@Slf4j
@RestController
public class PlatformConfigController extends BaseController {

    @Resource
    private PlatformConfigService platformConfigService;

    /**
     * 获取平台配置项列表
     *
     * @return
     */
    @GetMapping("/clientConfg/internal/{version}/{en}/nemo/platform/configList")
    public List<NemoDefaultPlatformConfig> getDefaultBrandConfig() {
        return platformConfigService.list();
    }

    /**
     * 更新平台配置项
     *
     * @param nemoDefaultPlatformConfig
     */
    @PutMapping("/clientConfg/internal/{version}/{en}/nemo/platform")
    public void saveOrUpdate(@RequestBody NemoDefaultPlatformConfig nemoDefaultPlatformConfig) {
        try {
            log.info("Update platform config:{}", Jackson.getObjectMapper().writeValueAsString(nemoDefaultPlatformConfig));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        platformConfigService.updateConfig(nemoDefaultPlatformConfig);
    }
}
