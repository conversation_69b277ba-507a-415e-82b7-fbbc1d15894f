package com.xylink.configserver.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xylink.configserver.controller.base.BaseController;
import com.xylink.configserver.data.model.AddNemoFeatureRequest;
import com.xylink.configserver.enums.SpecialFeatureStatus;
import com.xylink.configserver.service.SpecialNemoFeatureService;
import com.xylink.configserver.util.Jackson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
public class NemoSpecialFeatureController extends BaseController {

    @Autowired
    SpecialNemoFeatureService specialNemoFeatureService;

    @PostMapping("/internal/{version}/nemo/specialfeature/{featureId}")
    public void updateNemoSepcialFeature(@RequestBody AddNemoFeatureRequest req, @PathVariable String featureId, @RequestParam(value="isLogin",defaultValue="false") boolean isLogin ) {
        try {
            log.info("Process nemo feature request:{} for feature.", Jackson.getObjectMapper().writeValueAsString(req));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        if(req.getStatus() == SpecialFeatureStatus.ENABLED.getValue()) {
            for(String sn : req.getSns()) {
                specialNemoFeatureService.addSpecialFeatureNemo(featureId, sn,isLogin);
            }
        } else {
            for(String sn : req.getSns()) {
                specialNemoFeatureService.disableSpecialFeatureNemo(featureId, sn,isLogin);
            }
        }
    }
}
