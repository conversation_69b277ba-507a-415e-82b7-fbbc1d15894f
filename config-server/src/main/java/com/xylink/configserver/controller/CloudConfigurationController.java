package com.xylink.configserver.controller;

import com.xylink.configserver.data.entity.BaseCloudControllerConfigEntity;
import com.xylink.configserver.data.entity.EntCloudControllerConfigEntity;
import com.xylink.configserver.data.response.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.xylink.configserver.service.EntCloudControllerConfigService;

import java.util.Map;
import java.util.Set;

/**
 * ClassName:CloudConfigurationController
 * Package:com.xylink.configserver.controller
 * Description:
 *
 * <AUTHOR>
 * @Date 2025/5/8-10:48
 * @Version: v1.0
 */
@RestController
public class CloudConfigurationController {

    @Autowired
    private EntCloudControllerConfigService entCloudControllerConfigService;


    //云控配置查询逻辑，登录前，终端用
    @GetMapping("/clientConfg/external/cloud/config/before")
    public Map<String,Map<String,String>> getCloudConfigLoginBefore(Integer configType, String customizedKey) {
        return entCloudControllerConfigService.getCloudConfigLoginBeforeOrAfter(configType, customizedKey);
    }


    //云控配置查询逻辑，登录后，终端用
    @GetMapping("/clientConfg/external/cloud/config/after")
    public Map<String,Map<String,String>> getCloudConfigLoginAfter(Integer configType, String customizedKey) {
        return entCloudControllerConfigService.getCloudConfigLoginBeforeOrAfter(configType, customizedKey);
    }
    //云控配置内部接口，查询ck，manager用
    @GetMapping("/clientConfg/internal/cloud/config/cks")
    public R<Set<String>> getCloudConfigCKs() {
        Set<String> cloudConfigCKs = entCloudControllerConfigService.getCloudConfigCKs();
        return R.ok(cloudConfigCKs);
    }


    //云控配置内部接口，查询clientconfigName，manager用,根据type 查询
    @GetMapping("/clientConfg/internal/cloud/config/clientConfigNames")
    public R<Set<String>> getCloudConfigClientConfigNames(Integer type) {
        Set<String> cloudConfigClientConfigNames = entCloudControllerConfigService.getCloudConfigClientConfigNames(type);
        return R.ok(cloudConfigClientConfigNames);
    }


    //云控配置内部接口，查询configName，manager用
    @GetMapping("/clientConfg/internal/cloud/config/configNames")
    public R<Set<String>> getCloudConfigConfigNames(Integer type,String clientConfigName) {
        Set<String> cloudConfigConfigNames = entCloudControllerConfigService.getCloudConfigConfigNames(type, clientConfigName);
        return R.ok(cloudConfigConfigNames);
    }


    //云控配置内部接口，查询value，manager用
    @GetMapping("/clientConfg/internal/cloud/config/value")
    public R<String> getCloudConfigValue(Integer type,String clientConfigName,String configName,String customizedKey) {
        String cloudConfigValue = entCloudControllerConfigService.getCloudConfigValue(type, clientConfigName, configName, customizedKey);
        return R.ok(cloudConfigValue);
    }

    //云控配置内部接口，修改value，manager用
    @PostMapping("/clientConfg/internal/cloud/value/update")
    public R<Void> updateCloudConfigValue(@RequestBody EntCloudControllerConfigEntity configEntity) {
        return entCloudControllerConfigService.updateCloudConfigValue(configEntity);
    }

    //云控配置内部接口，新增configName，manager用
    @PostMapping("/clientConfg/internal/cloud/config/add")
    public R<Void> addCloudConfigValue(@RequestBody BaseCloudControllerConfigEntity configEntity) {
        return entCloudControllerConfigService.addCloudConfigValue(configEntity);
    }





}
