package com.xylink.configserver.controller;

import com.xylink.configserver.common.R;
import com.xylink.configserver.data.entity.BasicCertInfo;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.data.vo.RootCertListResponse;
import com.xylink.configserver.data.vo.RootCertResponse;
import com.xylink.configserver.exception.RestException;
import com.xylink.configserver.service.RootCertMaintainService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ClassName:RootCertMaintainController
 * Package:com.xylink.configserver.controller
 * Description:终端根证书维护 控制器
 *
 * <AUTHOR>
 * @Date 2024/11/26-10:34
 * @Version: v1.0
 */
@Slf4j
@RestController
public class RootCertMaintainController {

    @Autowired
    private RootCertMaintainService rootCertMaintainService;


    @GetMapping("/clientConfg/external/nemo/rootCert/list")
    public RootCertListResponse getRootCertList(@RequestParam String mark) {
        //sn 暂时没啥用，uaa 进行签名校验
        log.info("RP-RootCertMaintainController-getRootCertList:sn:{}", mark);
        return rootCertMaintainService.getRootCertList(mark);
    }


    @GetMapping("/clientConfg/external/nemo/root/cert")
    public RootCertResponse getRootCert(@RequestParam String certId) {
        log.info("RP-RootCertMaintainController-getRootCert:id:{}", certId);
        return rootCertMaintainService.getRootCert(certId);
    }


    /**
     * desc:获取根证书列表
     *
     * @return List<BasicCertInfo>
     * <AUTHOR>
     * @Date 2024/12/3-10:18
     * @Version: v1.0
     * 调用方： manager
     */
    @GetMapping("/clientConfg/internal/rootCert/list/v1")
    public R<List<BasicCertInfo>> getRootCertList() {
        List<BasicCertInfo> rootCertListByPage = rootCertMaintainService.getRootCertListByPage();
        log.info("RP-RootCertMaintainController-getRootCertList:rootCertListByPage size:{}", rootCertListByPage.size());
        return R.ok(rootCertListByPage);
    }

    /**
     * desc:获取单个证书详情
     *
     * @param certId
     * @return BasicCertInfo
     * <AUTHOR>
     * @Date 2024/12/3-10:20
     * @Version: v1.0
     * 调用方： manager
     */
    @GetMapping("/clientConfg/internal/rootCert/detail/v1")
    public R<BasicCertInfo> rootCertDetail(@RequestParam String certId) {
        log.info("RP-RootCertMaintainController-rootCertDetail:certId:{}", certId);
        BasicCertInfo rootCertDetail = rootCertMaintainService.getRootCertDetail(certId);
        log.info("RP-RootCertMaintainController-rootCertDetail:rootCertDetail:{}", rootCertDetail);
        return R.ok(rootCertDetail);
    }


    @PostMapping("/clientConfg/internal/rootCert/addOrUpdate/v1")
    public R<Void> addOrUpdateRootCert(@RequestBody BasicCertInfo basicCertInfo) {
        log.info("RP-RootCertMaintainController-addOrUpdateRootCert:basicCertInfo:{}", basicCertInfo);
        if (StringUtils.isBlank(basicCertInfo.getId())) {
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, "certId is null");
        }
        try {
            rootCertMaintainService.addOrUpdateRootCert(basicCertInfo);
        } catch (Exception e) {
            log.error("RP-RootCertMaintainController-addOrUpdateRootCert:error:{}", e.getMessage());
            return R.fail();
        }
        return R.ok();
    }

    @PostMapping("/clientConfg/internal/rootCert/delete/v1")
    public R<Void> deleteRootCert(@RequestParam String certId) {
        log.info("RP-RootCertMaintainController-deleteRootCert:certId:{}", certId);

        rootCertMaintainService.deleteRootCert(certId);

        return R.ok();
    }

}
