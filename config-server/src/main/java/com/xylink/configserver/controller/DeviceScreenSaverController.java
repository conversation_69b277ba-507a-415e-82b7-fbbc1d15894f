package com.xylink.configserver.controller;

import com.xylink.configserver.data.model.DeviceInfo;
import com.xylink.configserver.data.model.DeviceScreenSaver;
import com.xylink.configserver.service.DeviceScreenSaverService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.support.RequestContextUtils;

import javax.servlet.http.HttpServletRequest;

@RestController
public class DeviceScreenSaverController {

    @Autowired
    DeviceScreenSaverService deviceScreenSaverService;

    @GetMapping("/{version}/{en}/device/screensaver")
    public DeviceScreenSaver getDeviceScreenSaver(@RequestParam(value="sn") String deviceSn,
                                                  @RequestParam(value="model") String deviceModel, HttpServletRequest request) {
        DeviceInfo deviceInfo = new DeviceInfo();
        deviceInfo.setSn(deviceSn);
        deviceInfo.setModel(deviceModel);
        deviceInfo.setLocale(RequestContextUtils.getLocale(request).getDisplayName());
        return deviceScreenSaverService.getScreenSaver(deviceInfo);
    }


}
