package com.xylink.configserver.controller;

import com.xylink.configserver.data.exception.RestException;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.data.model.NemoDefaultConfig;
import com.xylink.configserver.data.vo.config.ConfigInfoVo;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.service.DefaultConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/8/2 15:39
 */
@RestController
@RequestMapping("/clientConfg/internal")
@RequiredArgsConstructor
@Slf4j
public class DefaultConfigController {

    private final DefaultConfigService defaultConfigService;

    /**
     * <AUTHOR>
     * nconsole展现libra_default_config
     * @since 2021-07-03
     */
    @GetMapping(path = "/{version}/default/config")
    public List<NemoDefaultConfig> getDefaultNemoConfig(
            Integer configType,
            @RequestParam(value = "configName", required = false) String configName,
            @RequestParam(value = "clientConfigName", required = false) String clientConfigName,
            @PathVariable String version) {
        return defaultConfigService.getDefaultNemoConfig(configType, clientConfigName, configName);
    }

    /**
     * <AUTHOR>
     * nconsole新增或更新libra_default_config全局配置
     * @since 2021-07-03
     */
    @PostMapping(path = "/{version}/default/config")
    public void saveOrUpdateDefaultNemoConfig(@RequestBody List<NemoDefaultConfig> nemoDefaultConfigs,
                                              @PathVariable String version) {
        if (CollectionUtils.isEmpty(nemoDefaultConfigs)) {
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER.getErrorCode(),
                                    ErrorStatus.INVALID_PARAMETER.getResId());
        }
        defaultConfigService.saveOrUpdateDefaultNemoConfig(nemoDefaultConfigs);
    }

    /**
     * 获取全量配置项列表
     */
    @GetMapping("/config/default/configNameList/v1")
    public List<ConfigInfoVo> getAllConfigNameList() {

        log.info("RP-DefaultConfigController-getAllConfigNameList");

        return defaultConfigService.getAllConfigNameList();

    }

}
