package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.configserver.data.model.NemoConfig;
import com.xylink.configserver.data.model.NemoConfigChangeContent;
import com.xylink.configserver.data.model.RestNemoConfig;
import com.xylink.configserver.service.ContentFormatter;
import org.springframework.stereotype.Service;

@Service
public class DefaultContentFormatter implements ContentFormatter {

    private static ObjectMapper jsonMapper = new ObjectMapper();

    @Override
    public String formatChangeNemoConfigContent(long nemoId,
                                                RestNemoConfig[] configs) {
        NemoConfigChangeContent content = new NemoConfigChangeContent();
        content.setNemoId(nemoId);
        NemoConfig[] contents = new NemoConfig[configs.length];
        for(int i = 0; i < configs.length; i++) {
            contents[i] = new NemoConfig(configs[i].getName(), configs[i].getValue());
        }
        content.setConfig(contents);
        try {
            return jsonMapper.writeValueAsString(content);
        } catch (JsonProcessingException e) {
            return null;
        }
    }
}
