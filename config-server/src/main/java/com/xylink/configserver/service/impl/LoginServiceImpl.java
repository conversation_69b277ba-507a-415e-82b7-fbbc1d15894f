package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xylink.configserver.data.model.UserDevice;
import com.xylink.configserver.enums.DeviceType;
import com.xylink.configserver.mapper.UserDeviceMapper;
import com.xylink.configserver.service.LoginService;
import com.xylink.configserver.util.RestApiContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class LoginServiceImpl extends ServiceImpl<UserDeviceMapper, UserDevice> implements LoginService {

    @Autowired
    UserDeviceMapper userDeviceMapper;

    @Override
    public boolean isValidSecurityKey(String securityKey, int deviceType) {

        if (StringUtils.isEmpty(securityKey)) {
            return false;
        }
        if (DeviceType.valueOf(deviceType) == DeviceType.GW_H323) {
            RestApiContext.setCurrentDeviceType(DeviceType.GW_H323);
            Long gwDeviceExpiredTimestamp = userDeviceMapper.getGWDeviceExpiredTimestamp(securityKey);
            return gwDeviceExpiredTimestamp != null && gwDeviceExpiredTimestamp > System.currentTimeMillis();
        }else {
            UserDevice userDevice = userDeviceMapper.getUserDeviceBySk(securityKey);
            if (null == userDevice){
                return false;
            }
            RestApiContext.setCurrentDeviceType(DeviceType.valueOf(userDevice.getType()));
            RestApiContext.setCurrentDevice(userDevice);
            long userDeviceExpiredTimestamp = userDevice.getExpirationTime();
            return  userDeviceExpiredTimestamp > System.currentTimeMillis();

        }
    }
}
