package com.xylink.configserver.service.impl;

import com.xylink.configserver.data.model.LibraDeviceSubtypeModel;
import com.xylink.configserver.mapper.LibraDeviceSubtypeModelMapper;
import com.xylink.configserver.service.DeviceSubtypeModelCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * desc: 从本地缓存中查询设备是否支持k4并定时更新本地缓存
 *
 * <AUTHOR>
 * @since 2020/12/29 2:11 下午
 */
@Service
@Slf4j
public class DeviceSubtypeModelCacheServiceImpl implements DeviceSubtypeModelCacheService {

    private final Map<Integer, LibraDeviceSubtypeModel> k4DeviceSubtypeCacheMap = new HashMap<>(128);

    private final LibraDeviceSubtypeModelMapper libraDeviceSubtypeModelMapper;

    public DeviceSubtypeModelCacheServiceImpl(LibraDeviceSubtypeModelMapper libraDeviceSubtypeModelMapper) {
        this.libraDeviceSubtypeModelMapper = libraDeviceSubtypeModelMapper;
    }

    @Override
    public boolean getCanK4BySubtype(int subtype) {
        if (k4DeviceSubtypeCacheMap.containsKey(subtype)) {
            return k4DeviceSubtypeCacheMap.get(subtype).getCanK4();
        } else {
            List<LibraDeviceSubtypeModel> deviceList = libraDeviceSubtypeModelMapper.selectBySubtype(subtype);

            //无效subtype
            if (deviceList == null) {
                return false;
            }
            //重复subtype
            if (deviceList.size() > 1) {
                log.error("重复subtype！");
            }

            //有效subtype存入缓存
            LibraDeviceSubtypeModel value = deviceList.get(0);
            k4DeviceSubtypeCacheMap.put(subtype, value);
            return value.getCanK4();
        }
    }

    public LibraDeviceSubtypeModel getSpecificModel(int subtype) {
        return k4DeviceSubtypeCacheMap.get(subtype);
    }

    @Scheduled(fixedDelay = 1000 * 60 * 60)
    private void timedUpdate() {
        List<LibraDeviceSubtypeModel> allDevice = libraDeviceSubtypeModelMapper.selectAll();
        //更新所有缓存数据
        allDevice.forEach(model -> k4DeviceSubtypeCacheMap.put((int) model.getSubType(), model));
        log.info("refresh libra device subtype model from database.");
    }

}
