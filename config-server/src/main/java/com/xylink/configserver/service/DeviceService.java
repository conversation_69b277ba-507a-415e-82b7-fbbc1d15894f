package com.xylink.configserver.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xylink.configserver.data.model.*;

import java.util.List;
import java.util.Set;

public interface DeviceService extends IService<UserDevice> {

    boolean isEnterpriseNemo(String deviceSN);

    long getNemoFirstBindTime(String deviceSN);

    boolean isEnterpriseNemoWithFixedModel(String sn);

    UserDevice getUserDeviceByNemoId(long nemoId);

    UserDevice getUserDeviceBySk(String securityKey);

    List<Long> getCommunityUserIds(long id);

    DeviceInfo getPresenceDeviceInfo(UserDevice device);

    long getNemoNumberByDeviceId(long deviceId);

    List<UserDevice> getUserDeviceByDevSn(String deviceSn);

    List<UserDevice> getEnterpriseProfileDeviceByType(String profileId,Integer deviceType,Integer start, Integer limit);

    List<UserDevice> getEnterpriseProfileDeviceBySubType(String profileId,Integer deviceType,Integer start, Integer limit);

    GwDevice getGwDeviceBySkAndType(String securityKey, GwDevice.GwType h323);

    void validateDeviceSn(String deviceSn, int subType);

    UserDevice getInUseUserHardDeviceByDevSn(String sn);

    String getUserProfileEnterpriseByUserId(long userProfileId);

    UserDevice getUser2DeviceByUserId(long userId);

    List<UserProfile> getUserProfileByEnterpriseId(String enterpriseId);

    UserPO getUserById(String userId);

    void updateUserPasswordExpireTimeById(String id, long passwordExpireTime);

    List<UserDevice> getEnterpriseDevices(String enterpriseId, int deviceType);

    Set<UserDevice> getEnterpriseNemos(String enterpriseProfileId);

    List<UserProfile> getUserProfileIds(List<String> telphones);
    List<UserDevice> getDeviceIds(List<String> numbers);
}
