package com.xylink.configserver.service.impl;

import com.ainemo.message.sender.service.MessageSender;
import com.ainemo.protocol.Device;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.xylink.configserver.data.bo.AbilityChangedMsgBO;
import com.xylink.configserver.data.dto.abilityset.AbilityResultDTO;
import com.xylink.configserver.data.dto.abilityset.DeviceAbilitySetDto;
import com.xylink.configserver.data.dto.abilityset.ServerAbilitySetDto;
import com.xylink.configserver.data.enums.OperationTypeEnum;
import com.xylink.configserver.data.enums.ResultCodeEnum;
import com.xylink.configserver.data.exception.RestException;
import com.xylink.configserver.data.model.DeviceAbilityModel;
import com.xylink.configserver.data.model.ServerAbilityEntity;
import com.xylink.configserver.data.model.UserDevice;
import com.xylink.configserver.enums.DeviceType;
import com.xylink.configserver.mapper.DeviceAbilityMapper;
import com.xylink.configserver.mapper.GwDeviceMapper;
import com.xylink.configserver.mapper.ServerAbilityMapper;
import com.xylink.configserver.mapper.UserDeviceMapper;
import com.xylink.configserver.service.AbilitySetService;
import com.xylink.configserver.service.DeviceService;
import com.xylink.configserver.service.NotificationService;
import com.xylink.configserver.util.Constants;
import com.xylink.configserver.util.DeviceUtil;
import com.xylink.configserver.util.Jackson;
import com.xylink.configserver.util.RestApiContext;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021/11/18 17:27
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AbilitySetServiceImpl implements AbilitySetService {

    private final ServerAbilityMapper serverAbilityMapper;

    private final DeviceService deviceService;

    private final DeviceAbilityMapper deviceAbilityMapper;

    private final MessageSender messageSender;

    private final UserDeviceMapper userDeviceMapper;

    private final NotificationService notificationService;

    @Setter(onMethod_ = {@Autowired, @Qualifier("redisCraftTemplate")})
    private RedisTemplate<String, List<Long>> redisTemplate;

    private final GwDeviceMapper gwDeviceMapper;

    //private final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(
    //        6, 6, 1, TimeUnit.MINUTES,
    //        new LinkedBlockingQueue<>(1),
    //        new ThreadFactoryBuilder().setNameFormat("AbilitySetServiceImpl-removeAbilityCache-%d").build(),
    //        new ThreadPoolExecutor.CallerRunsPolicy());

    @Override
    public List<Long> getServerAbility() {
        ValueOperations<String, List<Long>> valueOperations = redisTemplate.opsForValue();

        // 20240524：考虑redis宕机场景
        List<Long> redisServerAbility;
        boolean redisCrash = false;
        try {
            redisServerAbility = valueOperations.get(Constants.SERVER_ABILITY_MATCH_UP);
            if (redisServerAbility != null) {
                return redisServerAbility;
            }
        } catch (Exception e) {
            log.error("Can't fetch redis connections.Ignore cache in redis.");
            // 标记redis不可用
            redisCrash = true;
        }

        Long[] restLong;
        //去重查询分组
        List<Integer> dbServerAbility = serverAbilityMapper.selectCategoryNoDistinct();
        if (CollectionUtils.isEmpty(dbServerAbility)) {
            //分组为空,返回空数据
            restLong = new Long[0];
        } else {
            //分组不为空,计算每组分组总值
            restLong = new Long[dbServerAbility.size()];
            for (int i = 0; i < dbServerAbility.size(); i++) {
                Long sum = serverAbilityMapper.selectAndSumDecimalValue(i);
                sum=null==sum?0:sum;
                //将每组总值存储
                if (sum == null) {
                    sum = 0L;
                }
                restLong[i] = sum;
            }

        }
        redisServerAbility = Arrays.asList(restLong);

        try {
            if (!redisCrash) {
                valueOperations.set(Constants.SERVER_ABILITY_MATCH_UP, redisServerAbility, Duration.ofDays(1));
            }
        } catch (Exception e) {
            log.error("Can't fetch redis connections.Ignore refresh cache.");
        }
        log.info("serverAbility:{}", redisServerAbility);

        return redisServerAbility;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> getCombinedAbility(DeviceAbilitySetDto deviceAbilitySetDto) {

        if (deviceAbilitySetDto == null) {
            throw RestException.newInstanceOfHttpStatus400(ResultCodeEnum.INVALID_PARAMETER, "nullable data deviceAbilitySetDto");
        }

        // the gw device of h323 is not exists in the table of libra_user_device
        final Long deviceId = deviceAbilitySetDto.getDeviceId();
        DeviceType currentDeviceType = RestApiContext.getCurrentDeviceType();
        if (DeviceType.GW_H323.equals(currentDeviceType)) {
            if (gwDeviceMapper.selectByNumber(deviceId) == null) {
                throw RestException.newInstanceOfHttpStatus400(ResultCodeEnum.DEVICE_IS_NOT_EXISTS, "设备不存在");
            }
        } else {
            if (deviceService.getById(deviceId) == null) {
                throw RestException.newInstanceOfHttpStatus400(ResultCodeEnum.DEVICE_IS_NOT_EXISTS, "设备不存在");
            }
        }

        final List<Long> deviceAbilityList = deviceAbilitySetDto.getDeviceAbility();
        final List<Long> serverAbilityList = getServerAbility();
        if (CollectionUtils.isEmpty(deviceAbilityList) || CollectionUtils.isEmpty(serverAbilityList)) {
            List<Long> emptyAbilityList = new ArrayList<>(4);
            emptyAbilityList.add(0L);
            return emptyAbilityList;
        }

        // 更新终端能力集
        updateDeviceAbility(deviceAbilitySetDto);

        // 计算聚合能力集
        boolean redisCrash = false;
        ValueOperations<String, List<Long>> redisOpsValue = redisTemplate.opsForValue();
        final String redisDeviceAbilityKey = buildDeviceAbilityRedisKey(deviceId);
        try {
            List<Long> redisDeviceAbility = redisOpsValue.get(redisDeviceAbilityKey);
            if (redisDeviceAbility != null && redisDeviceAbility.equals(deviceAbilityList)) {
                List<Long> combinedAbilityList = redisOpsValue.get(buildCombinedAbilityRedisKey(deviceId));
                if (combinedAbilityList != null) {
                    return combinedAbilityList;
                }
            }
        } catch (Exception e) {
            log.error("Redis error, and don't calculate combined ability set");
            redisCrash = true;
        }

        List<Long> combinedAbilityList = getSupportedAbility(deviceAbilityList, serverAbilityList);

        // 终端能力集与合并能力集的处理非原子操作，需后置更新
        if (!redisCrash) {
            try {
                redisOpsValue.set(buildCombinedAbilityRedisKey(deviceId), combinedAbilityList, Duration.ofMinutes(5));
                redisOpsValue.set(redisDeviceAbilityKey, deviceAbilityList, Duration.ofMinutes(5));
            } catch (Exception e) {
                log.error("Redis error, and don't update combined ability cache.");
            }
        }

        return combinedAbilityList;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AbilityResultDTO getCombinedAbilityStr(DeviceAbilitySetDto deviceAbilitySetDto) {

        AbilityResultDTO abilityResultDTO = new AbilityResultDTO();
        List<Long> combinedAbility = getCombinedAbility(deviceAbilitySetDto);
        List<String> res = Lists.newArrayListWithExpectedSize(combinedAbility.size());
        if (CollectionUtils.isEmpty(combinedAbility)) {
            res.add("0");
        } else {
            for (Long ability : combinedAbility) {
                if (ability == null) {
                    ability = 0L;
                }

                res.add(Long.toBinaryString(ability));
            }
        }

        abilityResultDTO.setStrServerAbilitySet(res);
        return abilityResultDTO;

    }

    private void updateDeviceAbility(DeviceAbilitySetDto deviceAbilitySetDto) {

        Long deviceId = deviceAbilitySetDto.getDeviceId();
        List<Long> deviceAbilityList = deviceAbilitySetDto.getDeviceAbility();

        DeviceAbilityModel deviceAbilityModel = deviceAbilityMapper.selectById(deviceId);
        if (deviceAbilityModel == null) {
            deviceAbilityMapper.insert(new DeviceAbilityModel(deviceId, deviceAbilityList.toString(), true));
        } else {
            if (!deviceAbilityList.toString().equals(deviceAbilityModel.getDeviceAbility())) {
                deviceAbilityModel.setDeviceAbility(deviceAbilityList.toString());
                deviceAbilityMapper.updateById(deviceAbilityModel);
            }
        }

        removeAbilityCache();

    }

    private List<Long> getSupportedAbility(List<Long> deviceAbilityList, List<Long> serverAbilityList) {

        int maxIndex = Math.max(deviceAbilityList.size(), serverAbilityList.size()) - 1;
        int minIndex = Math.min(deviceAbilityList.size(), serverAbilityList.size()) - 1;
        List<Long> res = new ArrayList<>(maxIndex * 2);
        for (int index = 0; index <= maxIndex; ++index) {
            if (index > minIndex) {
                res.add(0L);
                continue;
            }
            res.add(deviceAbilityList.get(index) & serverAbilityList.get(index));
        }

        return res;

    }

    @Override
    public void removeAbilityCache() {

        log.info("expired caches of all ability types");

        try {
            redisTemplate.delete(Constants.SERVER_ABILITY_MATCH_UP);
            // todo error logic
            redisTemplate.delete(Constants.ABILITY_DEVICE);
            // todo error logic
            redisTemplate.delete(Constants.ABILITY_COMBINED);
        } catch (Exception e) {
            log.error("Redis error, ignore to clear cache.");
        }
    }

    @Override
    public void removeServerAbility() {
        log.info("expired caches of server ability types");
        redisTemplate.delete(Constants.SERVER_ABILITY_MATCH_UP);
        redisTemplate.delete(Constants.ABILITY_COMBINED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeServerAbility(List<ServerAbilitySetDto> serverAbilitySetDtoList) {

        if (CollectionUtils.isEmpty(serverAbilitySetDtoList)) {
            return;
        }

        List<ServerAbilitySetDto> sendMsgList = Lists.newArrayListWithExpectedSize(serverAbilitySetDtoList.size());
        for (ServerAbilitySetDto serverAbilitySetDto : serverAbilitySetDtoList) {
            if (serverAbilitySetDto == null) {
                continue;
            }

            String abilityName = serverAbilitySetDto.getAbilityName();
            String operationType = serverAbilitySetDto.getOperationType();
            if (StringUtils.isNotBlank(operationType) &&
                    OperationTypeEnum.DELETE.equals(OperationTypeEnum.valueOf(operationType))) {
                // delete
                log.info("delete ability:{}", serverAbilitySetDto);
                int delete = serverAbilityMapper.delete(Wrappers.<ServerAbilityEntity>lambdaQuery()
                        .eq(ServerAbilityEntity::getAbilityName, abilityName));
                if (delete > 0) {
                    sendMsgList.add(serverAbilitySetDto);
                }
            } else {
                ServerAbilityEntity serverAbilityEntity =
                        serverAbilityMapper.selectOne(Wrappers.<ServerAbilityEntity>lambdaQuery()
                                .select()
                                .eq(ServerAbilityEntity::getAbilityName, abilityName)
                                .last("limit 1"));
                if (serverAbilityEntity == null) {
                    // add
                    addServerAbility(serverAbilitySetDto);
                    sendMsgList.add(serverAbilitySetDto);
                } else {
                    // update
                    int dbAbilityState = serverAbilityEntity.getState();
                    updateServerAbility(serverAbilityEntity, serverAbilitySetDto);
                    if (!Objects.equals(dbAbilityState, serverAbilitySetDto.getState())) {
                        sendMsgList.add(serverAbilitySetDto);
                    }
                }
            }
        }

        removeServerAbility();

        //// send msg: non-persistent
        //// todo handling when transaction was committed
        //if (!CollectionUtils.isEmpty(sendMsgList)) {
        //    threadPoolExecutor.execute(() -> notifyServerAbilityChangedToAllDevices(getServerAbility()));
        //}

    }

    private void notifyServerAbilityChangedToAllDevices(List<Long> serverAbility) {

        log.info("send messages of ability changed");

        // devices
        long count = deviceAbilityMapper.selectCount(Wrappers.emptyWrapper());
        if (count == 0) {
            return;
        }
        long page;
        if (count % 500 == 0) {
            page = count / 500;
        } else {
            page = count / 500 + 1;
        }

        long offset = -1;
        for (int i = 0; i < page; i++) {
            List<DeviceAbilityModel> deviceAbilityModels = deviceAbilityMapper.selectLimit(offset, 500);
            if (CollectionUtils.isEmpty(deviceAbilityModels)) {
                return;
            }
            int currBatchSize = deviceAbilityModels.size();
            DeviceAbilityModel deviceAbilityModel = deviceAbilityModels.get(currBatchSize - 1);
            if (deviceAbilityModel != null) {
                offset = deviceAbilityModel.getDeviceId();
            }

            List<Long> did = Lists.newArrayListWithExpectedSize(currBatchSize);
            for (DeviceAbilityModel abilityModel : deviceAbilityModels) {
                if (abilityModel == null) {
                    continue;
                }

                did.add(abilityModel.getDeviceId());
            }
            List<UserDevice> userDevices = userDeviceMapper.selectByIds(did);

            // device
            List<Device> msgHardDevices = Lists.newArrayListWithExpectedSize(500);
            List<Device> msgSoftDevices = Lists.newArrayListWithExpectedSize(500);
            for (UserDevice userDevice : userDevices) {
                if (userDevice == null) {
                    continue;
                }

                // todo 时间紧张，暂时这样拆分处理，后续优化一下性能
                if (DeviceUtil.isSoftDevice(userDevice.getType())) {
                    msgSoftDevices.add(new Device(userDevice.getUserProfileID(),
                            userDevice.getId().toString(),
                            userDevice.getType()));
                } else {
                    msgHardDevices.add(new Device(userDevice.getUserProfileID(),
                            userDevice.getId().toString(),
                            userDevice.getType()));
                }
            }

            log.info("msgHardDevices: {}", msgHardDevices);

            // msg
            AbilityChangedMsgBO abilityChangedMsgBO = new AbilityChangedMsgBO();
            abilityChangedMsgBO.setServerAbility(serverAbility);
            notificationService.sendMsgToHardDevice(Jackson.writeValueAsString(abilityChangedMsgBO), msgHardDevices);
            notificationService.sendMsgToSoftDevice(Jackson.writeValueAsString(abilityChangedMsgBO), msgSoftDevices, false);
        }

    }

    private void updateServerAbility(ServerAbilityEntity serverAbilityEntity, ServerAbilitySetDto serverAbilitySetDto) {
        if (serverAbilityEntity == null || serverAbilitySetDto == null) {
            return;
        }
        if (Objects.equals(serverAbilityEntity.getAbilityDescribe(), serverAbilitySetDto.getDescribe())
                || Objects.equals(serverAbilityEntity.getState(), serverAbilitySetDto.getState())) {
            return;
        }

        log.info("update ability:{}", serverAbilitySetDto);
        serverAbilityEntity.setState(serverAbilitySetDto.getState());
        serverAbilityEntity.setAbilityDescribe(serverAbilitySetDto.getDescribe());
        serverAbilityMapper.updateById(serverAbilityEntity);
    }

    private void addServerAbility(ServerAbilitySetDto serverAbilitySetDto) {
        log.info("add ability:{}", serverAbilitySetDto);
        ServerAbilityEntity lastOne =
                serverAbilityMapper.selectOne(Wrappers.<ServerAbilityEntity>lambdaQuery()
                        .select().orderByDesc(ServerAbilityEntity::getId)
                        .last("limit 1"));

        int category;
        int offset;
        if (lastOne == null) {
            category = 0;
            offset = 0;
        } else {
            category = lastOne.getCategory();
            offset = lastOne.getOffset() + 1;
        }
        if (offset > 31) {
            offset = 0;
            category += 1;
        }

        ServerAbilityEntity serverAbilityEntity = ServerAbilityEntity.builder()
                .offset(offset)
                .abilityName(serverAbilitySetDto.getAbilityName())
                .state(serverAbilitySetDto.getState())
                .decimalValue((long) Math.pow(2, offset))
                .category(category)
                .abilityDescribe(serverAbilitySetDto.getDescribe()).build();
        log.info("new server ability info:{}", serverAbilityEntity);
        serverAbilityMapper.insert(serverAbilityEntity);
    }

    private static String buildDeviceAbilityRedisKey(Long deviceId) {
        return Constants.ABILITY_DEVICE + deviceId;
    }

    private static String buildCombinedAbilityRedisKey(Long deviceId) {
        return Constants.ABILITY_COMBINED + deviceId;
    }

}
