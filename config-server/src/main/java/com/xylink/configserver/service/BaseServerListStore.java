package com.xylink.configserver.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.configserver.data.model.ServerInfo;
import com.xylink.configserver.util.FileUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.util.Map;

@Slf4j
public abstract class BaseServerListStore {

    private static final String NETSERVER_KEY = "netTestServer";

    public Map<String, Object> processServerList(InputStream featuresConfigFile) {
        Map<String, Object> info = FileUtil.parseJsonFile(featuresConfigFile, Map.class);
        if (info != null) {
            if(info.containsKey(NETSERVER_KEY)) {
                ServerInfo nettool = getNetTestServer(info.get(NETSERVER_KEY));
                if(nettool != null) {
                    info.put(NETSERVER_KEY, nettool);
                } else {
                    info.remove(NETSERVER_KEY);
                }
            }
        }
        return info;
    }

    private ServerInfo getNetTestServer(Object json) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.readValue((String) json, ServerInfo.class);
        } catch (Exception e) {
            log.error("Failed to get nettest server");
            return null;
        }

    }
}
