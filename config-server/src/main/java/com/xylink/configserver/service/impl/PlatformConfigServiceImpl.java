package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xylink.configserver.data.model.DeviceConfig;
import com.xylink.configserver.data.model.NemoDefaultPlatformConfig;
import com.xylink.configserver.mapper.NemoDefaultPlatformMapper;
import com.xylink.configserver.service.PlatformConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2020-05-06 18:03
 */
@Slf4j
@Service
@DS("master")
public class PlatformConfigServiceImpl extends ServiceImpl<NemoDefaultPlatformMapper, NemoDefaultPlatformConfig> implements PlatformConfigService {

    @Resource
    private NemoDefaultPlatformMapper mapper;

    @Override
    public NemoDefaultPlatformConfig updateConfig(NemoDefaultPlatformConfig request) {
        if (request == null || StringUtils.isBlank(request.getConfigValue())) {
            return request;
        }
        NemoDefaultPlatformConfig config = this.getUniqPlatformConfig(request.getConfigPlatform(), request.getConfigName(), request.getClientConfigName(), request.getConfigType());
        if (Objects.isNull(config)) {
            log.info("create new default platform config " + request);
            config = new NemoDefaultPlatformConfig();
            config.setConfigPlatform(request.getConfigPlatform());
            config.setConfigType(request.getConfigType());
            config.setConfigName(request.getConfigName());
            config.setConfigValue(request.getConfigValue());
            config.setClientConfigName(request.getClientConfigName());
        } else {
            log.info(" default platform config exists " + config + " and change value to :" + request.getConfigValue());
            config.setConfigValue(request.getConfigValue());
        }
        this.saveOrUpdate(config);
        return config;
    }

    @Override
    @DS("slave")
    public NemoDefaultPlatformConfig getUniqPlatformConfig(String configPlatform, String configName, String clientConfigName, int configType) {
        return mapper.getUniqPlatformConfig(configPlatform, configName, clientConfigName, configType);
    }

    @Override
    public List<NemoDefaultPlatformConfig> getByPlatformAndConfigType(String configPlatform, int configType) {
        return mapper.getByPlatformAndConfigType(configPlatform, configType);
    }

    @Override
    public void addPlatformConfig(String configPlatform, int configType, Map<String, Map<String, String>> targetConfigs) {
        List<NemoDefaultPlatformConfig> deviceTypePlatformConfig = this.getByPlatformAndConfigType(configPlatform, configType);
        if (Objects.nonNull(deviceTypePlatformConfig) && !deviceTypePlatformConfig.isEmpty()) {
            DeviceConfig.configPOs2All(deviceTypePlatformConfig, targetConfigs);
        }
    }
}
