package com.xylink.configserver.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xylink.configserver.data.entity.BaseCloudControllerConfigEntity;
import com.xylink.configserver.data.entity.EntCloudControllerConfigEntity;
import com.xylink.configserver.data.response.R;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.mapper.EntCloudControllerConfigMapper;
import com.xylink.configserver.service.BaseCloudControllerConfigService;
import com.xylink.configserver.service.EntCloudControllerConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class EntCloudControllerConfigServiceImpl
        extends ServiceImpl<EntCloudControllerConfigMapper, EntCloudControllerConfigEntity>
        implements EntCloudControllerConfigService {

    @Autowired
    private BaseCloudControllerConfigService baseCloudControllerConfigService;

    @Override
    public Map<String,Map<String,String>> getCloudConfigLoginBeforeOrAfter(Integer configType, String customizedKey) {
        // 查询baseCloudControllerConfigService
        List<BaseCloudControllerConfigEntity> baseCloudControllerConfigEntities = baseCloudControllerConfigService
                .list(new LambdaQueryWrapper<BaseCloudControllerConfigEntity>()
                        .eq(BaseCloudControllerConfigEntity::getConfigType, configType));
        Map<String, Map<String, String>> defaultConfigsByCategory = getConfigsByCategory(baseCloudControllerConfigEntities);

        // 查询entCloudControllerConfigService
        List<EntCloudControllerConfigEntity> entCloudControllerConfigEntities = this
                .list(new LambdaQueryWrapper<EntCloudControllerConfigEntity>()
                        .eq(EntCloudControllerConfigEntity::getConfigType, configType)
                        .eq(EntCloudControllerConfigEntity::getCustomizedKey, customizedKey));
        Map<String, Map<String, String>> entConfigsByCategory = getConfigsByCategory(entCloudControllerConfigEntities);
        // 合并配置，entConfigsByCategory 高于defaultConfigsByCategory
        for (Map.Entry<String, Map<String, String>> entry : entConfigsByCategory.entrySet()) {
            String clientConfigName = entry.getKey();
            Map<String, String> entConfigMap = entry.getValue();
            if (defaultConfigsByCategory.containsKey(clientConfigName)) {
                Map<String, String> defaultConfigMap = defaultConfigsByCategory.get(clientConfigName);
                defaultConfigMap.putAll(entConfigMap);
            } else {
                defaultConfigsByCategory.put(clientConfigName, entConfigMap);

            }
        }
        // 返回结果
        return defaultConfigsByCategory;
    }

    // 按类别分组获取配置// 按类别分组获取配置
    private <T extends BaseCloudControllerConfigEntity> Map<String, Map<String, String>> getConfigsByCategory(List<T> configItems) {
        // 过滤掉configvalue为空的
        configItems = configItems.stream()
                .filter(item -> item.getConfigValue() != null && !item.getConfigValue().isEmpty())
                .collect(Collectors.toList());
        // 返回结构为 map<clientConfigName,map<configName,configValue>>
        Map<String, Map<String, String>> configMap = configItems.stream()
                .collect(Collectors.groupingBy(BaseCloudControllerConfigEntity::getClientConfigName,
                        Collectors.toMap(BaseCloudControllerConfigEntity::getConfigName,
                                BaseCloudControllerConfigEntity::getConfigValue)));
        return configMap;
    }

    @Override
    public Set<String> getCloudConfigCKs() {
        //查询entCloudControllerConfig表，获取所有ck并去重
        List<EntCloudControllerConfigEntity> entCloudControllerConfigEntities = this.list();
        // 去重
        Set<String> cks = entCloudControllerConfigEntities.stream()
                .map(EntCloudControllerConfigEntity::getCustomizedKey)
                .collect(Collectors.toSet());
        // 返回结果
        return cks;
    }

    @Override
    public Set<String> getCloudConfigClientConfigNames(Integer configType) {
        //查询baseCloudControllerConfig表，获取所有clientConfigName并去重
        List<BaseCloudControllerConfigEntity> baseCloudControllerConfigEntities = baseCloudControllerConfigService
                .list(new LambdaQueryWrapper<BaseCloudControllerConfigEntity>()
                        .eq(BaseCloudControllerConfigEntity::getConfigType, configType));
        // 去重
        Set<String> clientConfigNames = baseCloudControllerConfigEntities.stream()
                .map(BaseCloudControllerConfigEntity::getClientConfigName)
                .collect(Collectors.toSet());
        // 返回结果
        return clientConfigNames;   
    }

    @Override
    public Set<String> getCloudConfigConfigNames(Integer type,String clientConfigName) {
        //查询baseCloudControllerConfig表，获取所有clientConfigName并去重
        List<BaseCloudControllerConfigEntity> baseCloudControllerConfigEntities = baseCloudControllerConfigService
                .list(new LambdaQueryWrapper<BaseCloudControllerConfigEntity>()
                        .eq(BaseCloudControllerConfigEntity::getConfigType, type));
        // 过滤出clientConfigName相同的配置
        List<BaseCloudControllerConfigEntity> filteredConfigs = baseCloudControllerConfigEntities.stream()
                .filter(config -> clientConfigName.equals(config.getClientConfigName()))
                .collect(Collectors.toList());
        // 获取configName并去重
        Set<String> configNames = filteredConfigs.stream()
                .map(BaseCloudControllerConfigEntity::getConfigName) 
                .collect(Collectors.toSet());
        // 返回结果
        return configNames;
    }

    @Override
    public String getCloudConfigValue(Integer type, String clientConfigName, String configName, String customizedKey) {
        //精确查询entCloudControllerConfig表
        BaseCloudControllerConfigEntity entCloudControllerConfigEntity = this.getOne(new LambdaQueryWrapper<EntCloudControllerConfigEntity>()
                .eq(EntCloudControllerConfigEntity::getConfigType, type)
                .eq(EntCloudControllerConfigEntity::getClientConfigName, clientConfigName)
                .eq(EntCloudControllerConfigEntity::getConfigName, configName)
                .eq(EntCloudControllerConfigEntity::getCustomizedKey, customizedKey));
        // 如果没有查询到，则查询baseCloudControllerConfig表
        if (entCloudControllerConfigEntity == null) {
            entCloudControllerConfigEntity = baseCloudControllerConfigService.getOne(new LambdaQueryWrapper<BaseCloudControllerConfigEntity>()
                    .eq(BaseCloudControllerConfigEntity::getConfigType, type)
                    .eq(BaseCloudControllerConfigEntity::getClientConfigName, clientConfigName)
                    .eq(BaseCloudControllerConfigEntity::getConfigName, configName));
        }

        // 如果还是没有查询到，则返回null
        if (entCloudControllerConfigEntity == null) {
            return null;
        }
       
        // 返回结果
        return entCloudControllerConfigEntity.getConfigValue();
    }

    @Override
    public R<Void> addCloudConfigValue(BaseCloudControllerConfigEntity configEntity) {
        // 符合性检查
        if (configEntity.getConfigType() == null || configEntity.getClientConfigName() == null
                || configEntity.getConfigName() == null ) {
            throw new ServiceException("参数不合法", ErrorStatus.INVALID_PARAMETER);
        }
        // 检查baseCloudControllerConfig表中是否存在相同的配置
        BaseCloudControllerConfigEntity existingConfig = baseCloudControllerConfigService
                .getOne(new LambdaQueryWrapper<BaseCloudControllerConfigEntity>()
                        .eq(BaseCloudControllerConfigEntity::getConfigType, configEntity.getConfigType())
                        .eq(BaseCloudControllerConfigEntity::getClientConfigName, configEntity.getClientConfigName())
                        .eq(BaseCloudControllerConfigEntity::getConfigName, configEntity.getConfigName()));
        if (existingConfig != null) {
            //抛出异常
            throw new ServiceException("配置已存在", ErrorStatus.OBJ_EXIST);
        }
        // 插入新配置
        baseCloudControllerConfigService.save(configEntity);
        return R.ok();
    }

    @Override
    public R<Void> updateCloudConfigValue(EntCloudControllerConfigEntity configEntity) {
        // 符合性检查
        if (configEntity.getConfigType() == null || configEntity.getClientConfigName() == null
                || configEntity.getConfigName() == null || configEntity.getCustomizedKey() == null) {
            throw new ServiceException("参数不合法", ErrorStatus.INVALID_PARAMETER);
        }
        //查询entCloudControllerConfig表，查询是否存在相同的配置
        EntCloudControllerConfigEntity existingConfig = this.getOne(new LambdaQueryWrapper<EntCloudControllerConfigEntity>()
                .eq(EntCloudControllerConfigEntity::getConfigType, configEntity.getConfigType())
                .eq(EntCloudControllerConfigEntity::getClientConfigName, configEntity.getClientConfigName())
                        .eq(EntCloudControllerConfigEntity::getCustomizedKey, configEntity.getCustomizedKey())
                .eq(EntCloudControllerConfigEntity::getConfigName, configEntity.getConfigName()));
        if (existingConfig != null) {
            //更新配置
            existingConfig.setConfigValue(configEntity.getConfigValue());
            this.updateById(existingConfig);
        } else {
            //如果不存在，则插入新配置
            this.save(configEntity);
        }
        return R.ok();
    }
}