package com.xylink.configserver.service;

import com.xylink.configserver.data.model.DeviceInfo;
import com.xylink.configserver.data.model.FeatureProvision;

import java.util.List;

public interface FeatureListStore {

    List<FeatureProvision> getDeviceFeatureProvision(DeviceInfo device);

    List<FeatureProvision> getAppFeatureProvision(String manufacture, String model);

    List<FeatureProvision> getPCFeatureProvision(String manufacture, String model);
}
