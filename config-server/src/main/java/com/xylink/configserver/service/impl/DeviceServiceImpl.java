package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xylink.configserver.data.enums.ResultCodeEnum;
import com.xylink.configserver.data.exception.RestException;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.enums.DeviceSubType;
import com.xylink.configserver.enums.GateWayRequestTypeEnum;
import com.xylink.configserver.mapper.NemoModelMapper;
import com.xylink.configserver.mapper.UserDeviceMapper;
import com.xylink.configserver.proxy.GatewayProxy;
import com.xylink.configserver.service.DeviceService;
import com.xylink.configserver.service.PresenceService;
import com.xylink.configserver.service.common.CommonVariableService;
import com.xylink.configserver.util.DeviceSnParse;
import com.xylink.configserver.util.PrivateCloud;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
@Service
public class DeviceServiceImpl extends ServiceImpl<UserDeviceMapper, UserDevice> implements DeviceService {

    @Autowired
    UserDeviceMapper userDeviceMapper;

    @Autowired
    NemoModelMapper nemoModelMapper;

    @Autowired
    PresenceService presenceService;

    @Autowired
    GatewayProxy gatewayProxy;

    @Value("${common.en.isDev}")
    boolean isDev = false;

    @Autowired
    private CommonVariableService commonVariableService;

    @Override
    public boolean isEnterpriseNemo(String nemoSN) {
        if (!commonVariableService.isMultiEnterprise()) {
            return true;
        }
        boolean isEnterpriseNemo = false;
        boolean isEnBox = false;
        List<String> enterpriseNemoList = userDeviceMapper.getEnterpriseNemoBySn(nemoSN);
        if (null != enterpriseNemoList && enterpriseNemoList.size()>0){
            isEnterpriseNemo = true;
        }
        List<String> enBoxPOList = userDeviceMapper.getEnBoxPOBySn(nemoSN);
        if (null != enBoxPOList && enBoxPOList.size()>0){
            isEnBox = true;
        }

        return DeviceSnParse.isEnterpriseNemoDecideBySn(nemoSN) || isEnterpriseNemo
                || isEnBox;
    }

    @Override
    public long getNemoFirstBindTime(String deviceSN) {
        Long firstBingdTime = userDeviceMapper.getNemoFirstBindTime(deviceSN);
        return firstBingdTime == null ? 0:firstBingdTime;
    }

    @Override
    public boolean isEnterpriseNemoWithFixedModel(String nemoSN) {
        NemoModel nModel = nemoModelMapper.getNemoModel(nemoSN);
        if (nModel != null) {
            return nModel.getModel() != null && (nModel.getModel().trim().startsWith("NE") || nModel.getModel().trim().startsWith("AE"));
        }
        return false;
    }

    @Override
    public UserDevice getUserDeviceByNemoId(long nemoId) {
        return userDeviceMapper.getUserDeviceByNemoId(nemoId);
    }

    @Override
    public UserDevice getUserDeviceBySk(String securityKey) {
        return userDeviceMapper.getUserDeviceBySk(securityKey);
    }

    @Override
    public List<Long> getCommunityUserIds(long id) {
        return userDeviceMapper.getCommunityUserIds(id);
    }

    @Override
    public DeviceInfo getPresenceDeviceInfo(UserDevice device) {
        if (device==null){
            return null;
        }
        Map<String, String> info = presenceService.getDeviceInfo(device.getUserProfileID(), String.valueOf(device.getId()), device.getType(), null);
        DeviceInfo deviceInfo = new DeviceInfo();
        deviceInfo.setHardVersion(info.get("hwVersion"));
        deviceInfo.setLocale(info.get("locale"));
        deviceInfo.setOS(info.get("osVersion"));
        deviceInfo.setSn(info.get("sn"));
        deviceInfo.setSoftVersion(info.get("clientVersion"));
        deviceInfo.setMacAddress(info.get("macAddress"));
        deviceInfo.setInnerIp(info.get("innerIp"));
        return deviceInfo;
    }

    @Override
    public long getNemoNumberByDeviceId(long deviceId) {
        return userDeviceMapper.getNemoNumberByDeviceId(deviceId);
    }

    @Override
    public List<UserDevice> getUserDeviceByDevSn(String deviceSn) {
        return userDeviceMapper.getUserDeviceByDevSn(deviceSn);
    }

    @Override
    public List<UserDevice> getEnterpriseProfileDeviceByType(String profileId, Integer deviceTypeOrSubType, Integer start, Integer limit) {
        return userDeviceMapper.getEnterpriseProfileNemosByType(profileId,deviceTypeOrSubType,start,limit);
    }

    @Override
    public List<UserDevice> getEnterpriseProfileDeviceBySubType(String profileId, Integer deviceTypeOrSubType, Integer start, Integer limit) {
        return userDeviceMapper.getEnterpriseProfileNemosBySubType(profileId,deviceTypeOrSubType,start,limit);
    }

    @Override
    public GwDevice getGwDeviceBySkAndType(String sk, GwDevice.GwType gwType) {
        if(sk == null){
            return null;
        }
        if(gwType == null){
            gwType = GwDevice.GwType.UNKNOWN;
        }
        List<GwDevice> list = userDeviceMapper.getGwDeviceBySkAndType(sk, gwType.name());
        GwDevice source = null;
        if (!CollectionUtils.isEmpty(list)) {
            source = list.get(0);
        }
        if (!processPreCheck(source)) {
            return source;
        }
        BillResponse<BillGateWayResourceResponse> response = gatewayProxy.getResource(buildRequestByGwDevice(source));
        if (!response.isOk()) {
            log.error("get BillGateWayResourceResponse from bill error." + response.getErrorMsg());
        }
        return processGwDevice(source, response.getData());
    }

    @Override
    public void validateDeviceSn(String deviceSn, int subType) {

        // NP5000包含从设备，从设备sn为非法sn，按产品设计要求放开校验逻辑
        if (DeviceSubType.NP5000_SLAVE.getValue() == subType) {
            return;
        }

        if (!isValidDevice(deviceSn)) {
            throw RestException.newInstanceOfHttpStatus400(ResultCodeEnum.INVALID_DEVICE_SN,
                    "Invalid Hardware device sn.");
        }
    }

    @Override
    public UserDevice getInUseUserHardDeviceByDevSn(String deviceSn) {
        List<UserDevice> userDevices = userDeviceMapper.getInUseUserDeviceByDevSnAndTypes(deviceSn, "2,8,7");
        return (null == userDevices||userDevices.size()<1)?null:userDevices.get(0);
    }

    @Override
    public String getUserProfileEnterpriseByUserId(long userProfileId) {
        return userDeviceMapper.getUserProfileEnterpriseByUserId(userProfileId);
    }

    // 获取用户信息转为device信息
    @Override
    public UserDevice getUser2DeviceByUserId(long userId) {
        List<UserDevice> userProfile2Devices = userDeviceMapper.getUserProfile2Device(userId);
        if (userProfile2Devices.size() > 0){
            return userProfile2Devices.get(0);
        }
        return null;
    }

    @Override
    public List<UserProfile> getUserProfileByEnterpriseId(String enterpriseId) {
        return userDeviceMapper.getUserProfileByEnterpriseId(enterpriseId);
    }

    @Override
    public UserPO getUserById(String userId) {
        return userDeviceMapper.getUserById(userId);
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void updateUserPasswordExpireTimeById(String id, long passwordExpireTime) {
        userDeviceMapper.updateUserPasswordExpireTimeById(id,passwordExpireTime);
    }

    @Override
    public List<UserDevice> getEnterpriseDevices(String enterpriseId, int deviceType) {
        return userDeviceMapper.getEnterpriseDevices(enterpriseId,deviceType);
    }

    @Override
    public Set<UserDevice> getEnterpriseNemos(String enterpriseProfileId) {
        return userDeviceMapper.getEnterpriseNemos(enterpriseProfileId);
    }

    @Override
    public List<UserProfile> getUserProfileIds(List<String> telphones) {
        String telphonesStr = "'"+StringUtils.join(telphones,"','")+"'";
        return userDeviceMapper.getUserProfileIds(telphonesStr);
    }

    @Override
    public List<UserDevice> getDeviceIds(List<String> numbers) {
        String numbersStr = "'"+StringUtils.join(numbers,"','")+"'";
        return userDeviceMapper.getDeviceIds(numbersStr);
    }

    public boolean isValidDevice(String sn) {
        if (isDev) {
            return true;
        }
        if (!StringUtils.isEmpty(sn) && sn.length() == 16) {

            String prefix = sn.substring(0, 12);
            String encryptStr = prefix + ")G$d";
            String md5Sub = DigestUtils.md5Hex(encryptStr).substring(28);
            String correctSn = prefix + md5Sub.toUpperCase();
            return sn.equals(correctSn);
        }

        log.error("sn校验不通过：{}", sn);

        return false;
    }

    private BillGateWayResourceRequest buildRequestByGwDevice(GwDevice source) {
        int type = GateWayRequestTypeEnum.mapped(source.getType());
        return new BillGateWayResourceRequest(source.getEnterpriseId(), type);
    }

    private boolean processPreCheck(GwDevice source) {
        return !PrivateCloud.isPrivate() && Objects.nonNull(source) && GateWayRequestTypeEnum.supportType(source.getType()) && source.getIsNative();
    }

    private GwDevice processGwDevice(GwDevice source, BillGateWayResourceResponse influential) {
        if (Objects.nonNull(influential)) {
            source.setMaxInCount((int) influential.getCount());
            source.setExpiredTimestamp(influential.getExpireTime());
        }
        return source;
    }
}
