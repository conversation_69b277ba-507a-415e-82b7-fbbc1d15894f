package com.xylink.configserver.service;

import com.xylink.configserver.data.entity.BasicCertInfo;
import com.xylink.configserver.data.vo.RootCertListResponse;
import com.xylink.configserver.data.vo.RootCertResponse;

import java.util.List;

/**
 * ClassName:RootCertMaintainService
 * Package:com.xylink.configserver.service
 * Description:
 *
 * <AUTHOR>
 * @Date 2024/11/26-10:45
 * @Version: v1.0
 */
public interface RootCertMaintainService {
    //通过给定字段，查询证书列表
    RootCertListResponse getRootCertList(String sn);

    //通过给定证书id，查询证书
    RootCertResponse getRootCert(String certId);

    List<BasicCertInfo> getRootCertListByPage();

    /**
     * desc:获取根证书详情
     *
     * @param certId
     * @return BasicCertInfo
     * <AUTHOR>
     * @Date 2024/12/3-10:20
     * @Version: v1.0
     * 调用方： manager
     */
    BasicCertInfo getRootCertDetail(String certId);

    /**
     * desc:获取根证书详情
     *
     * @param basicCertInfo
     * @return void
     * <AUTHOR>
     * @Date 2024/12/3-10:20
     * @Version: v1.0
     * 调用方： manager
     */
    void addOrUpdateRootCert(BasicCertInfo basicCertInfo);

    /**
     * desc:获取根证书详情
     *
     * @param certId
     * @return void
     * <AUTHOR>
     * @Date 2024/12/3-10:20
     * @Version: v1.0
     * 调用方： manager
     */
    void deleteRootCert(String certId);
}
