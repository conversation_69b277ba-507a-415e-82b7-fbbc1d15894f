package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.enums.Configs;
import com.xylink.configserver.enums.DeviceType;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.mapper.EnterpriseNemoMapper;
import com.xylink.configserver.mapper.EnterpriseNemoProfileMapper;
import com.xylink.configserver.proxy.InternalApiProxy;
import com.xylink.configserver.service.*;
import com.xylink.configserver.util.Jackson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.xylink.configserver.util.Constants.TYPE_NEMO_CONFIG;

@Slf4j
@Service
public class EnterpriseNemoProfileServiceImpl implements EnterpriseNemoProfileService {

    @Autowired
    EnterpriseNemoProfileMapper enterpriseNemoProfileMapper;

    @Autowired
    EnterpriseNemoConfigService enterpriseNemoConfigService;

    @Autowired
    DefaultConfigService defaultConfigService;

    @Autowired
    InternalApiProxy internalApiProxy;

    @Autowired
    CustomizeFeatureService customizeFeatureService;

    @Autowired
    NotificationService notificationService;

    @Autowired
    DeviceService deviceService;

    @Autowired
    PresenceService presenceService;

    @Autowired
    EnterpriseNemoMapper enterpriseNemoMapper;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    private static final long DAY_TO_MS = TimeUnit.DAYS.toMillis(1); // 24L* 3600L* 1000L;

    @Override
    public EnterpriseNemoProfileUpdateReq getEnterpriseProfile(String enterpriseId) {
        EnterpriseNemoProfile profile = enterpriseNemoProfileMapper.getEnterpriseProfileByEnterpriseId(enterpriseId);

        List<EnterpriseNemoConfig> econfigs = enterpriseNemoConfigService.getProfileConfigs("default");
        List<NemoDefaultConfig> nemoDefaultConfigPOS = defaultConfigService.getAllDefaultConfig();
        EnterpriseNemoProfileUpdateReq enterpriseNemoProfileUpdateReqV2 = enterpriseProfilePO2RestData(profile);
        List<DeviceConfigUpdate> deviceConfigUpdateList = enterpriseNemoProfileUpdateReqV2.getConfigs();
        Set<String> defaultEnterpriseConfig = new HashSet<>();
        if (!econfigs.isEmpty()){
            for (EnterpriseNemoConfig enterpriseNemoConfigPO : econfigs){
                defaultEnterpriseConfig.add(enterpriseNemoConfigPO.getKey());
            }
        }
        if(deviceConfigUpdateList == null) {
            deviceConfigUpdateList = new ArrayList<>();
            enterpriseNemoProfileUpdateReqV2.setConfigs(deviceConfigUpdateList);
            for(NemoDefaultConfig nemoDefaultConfigPO : nemoDefaultConfigPOS) {
                if (!defaultEnterpriseConfig.contains(nemoDefaultConfigPO.getKey())){
                    DeviceConfigUpdate deviceConfigUpdate = new DeviceConfigUpdate(nemoDefaultConfigPO.getConfigName(),
                            nemoDefaultConfigPO.getConfigValue(), nemoDefaultConfigPO.getClientConfigName());
                    deviceConfigUpdate.setDeviceSubType(nemoDefaultConfigPO.getConfigType());
                    deviceConfigUpdateList.add(deviceConfigUpdate);
                }
            }
            for(EnterpriseNemoConfig enterpriseNemoConfigPO : econfigs) {
                DeviceConfigUpdate deviceConfigUpdate = new DeviceConfigUpdate(enterpriseNemoConfigPO.getConfigName(),
                        enterpriseNemoConfigPO.getConfigValue(), enterpriseNemoConfigPO.getClientConfigName());
                deviceConfigUpdate.setDeviceSubType(enterpriseNemoConfigPO.getConfigType());
                deviceConfigUpdateList.add(deviceConfigUpdate);
            }
        } else {
            Set<String> contains = new HashSet<>();
            for(DeviceConfigUpdate deviceConfigUpdate : deviceConfigUpdateList) {
                contains.add(deviceConfigUpdate.getKey());
            }
            for(NemoDefaultConfig nemoDefaultConfigPO : nemoDefaultConfigPOS) {
                if(!contains.contains(nemoDefaultConfigPO.getKey()) && !defaultEnterpriseConfig.contains(nemoDefaultConfigPO.getKey())) {
                    DeviceConfigUpdate deviceConfigUpdate = new DeviceConfigUpdate(nemoDefaultConfigPO.getConfigName(),
                            nemoDefaultConfigPO.getConfigValue(), nemoDefaultConfigPO.getClientConfigName());
                    deviceConfigUpdate.setDeviceSubType(nemoDefaultConfigPO.getConfigType());
                    deviceConfigUpdateList.add(deviceConfigUpdate);
                }
            }
            for(EnterpriseNemoConfig enterpriseNemoConfigPO : econfigs) {
                DeviceConfigUpdate deviceConfigUpdate = new DeviceConfigUpdate(enterpriseNemoConfigPO.getConfigName(),
                        enterpriseNemoConfigPO.getConfigValue(), enterpriseNemoConfigPO.getClientConfigName());
                deviceConfigUpdate.setDeviceSubType(enterpriseNemoConfigPO.getConfigType());
                if(!contains.contains(deviceConfigUpdate.getKey())) {
                    deviceConfigUpdateList.add(deviceConfigUpdate);
                }
            }
        }
        return enterpriseNemoProfileUpdateReqV2;
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void removeEnterpriseProfileConfig(EnterpriseNemoProfileUpdateReq reqV2) {

        List<DeviceConfigUpdate> configs = reqV2.getConfigs();
        if(configs == null || configs.size() == 0) {
            return;
        }
        String profileId = reqV2.getProfileId();
        if (StringUtils.isNotBlank(reqV2.getEnterpriseId()) ){
            EnterpriseNemoProfile po = enterpriseNemoProfileMapper.getEnterpriseProfileByEnterpriseId(reqV2.getEnterpriseId());
            if (po!=null){
                profileId = po.getId();
                reqV2.setProfileId(profileId);
            }
        }
        if(StringUtils.isNotBlank(reqV2.getProfileId()) && !reqV2.getProfileId().equals(profileId)){
            log.error("Invalid enterprise profile id: " + profileId);
            return;
        }
        for (DeviceConfigUpdate configUpdate : configs){
            log.info("delete enterprise profile config :" + configUpdate.getConfigName() + " from :" + profileId);
            enterpriseNemoProfileMapper.removeEnterpriseNemoProfileConfig(profileId,configUpdate.getConfigName(),configUpdate.getDeviceSubType(),configUpdate.getClientConfigName());
        }
        log.debug("notify  " + reqV2.getConfigs() + " from  " + profileId);
        notificationService.norifyProfileRemoveNemoConfig(reqV2);
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public String updateEnterpriseNemoProfileByEnterpriseId(String enterpriseId, EnterpriseNemoProfile profile) {
        if(profile == null) {
            return null;
        }
        log.debug("updateEnterpriseNemoProfileByEnterpriseId - Update enerprise nemo profile [" + profile.toString() + "]");
        EnterpriseNemoProfile po = enterpriseNemoProfileMapper.getEnterpriseProfileByEnterpriseId(enterpriseId);
        boolean newEnterprise = false;
        if(po != null) {
            po.setDisplayName(profile.getDisplayName());
            po.setSpecialNemoBinding(profile.isSpecialNemoBinding());
            po.setSpecialUI(profile.isSpecialUI());

            if (profile.isEnhancePassword() &&
                    (profile.getPasswordExpireInterval()* DAY_TO_MS) != po.getPasswordExpireInterval()) {
                if (StringUtils.isNotEmpty(po.getCustomizedKey())) {
                    onPasswordExpireIntervalChanged(profile, po);
                }
                else {
                    log.warn("Enterprise [" + profile.getDisplayName() + "] is not customized enterprise");
                }

                po.setPasswordExpireInterval(profile.getPasswordExpireInterval()* DAY_TO_MS);
            }
            enterpriseNemoProfileMapper.updateEnterpriseNemoProfile(po);
        } else {
            po = new EnterpriseNemoProfile();
            po.setDisplayName(profile.getDisplayName());
            po.setEnterpriseId(enterpriseId);
            newEnterprise = true;
            enterpriseNemoProfileMapper.addEnterpriseNemoProfile(po);
        }

        if(newEnterprise) {
            List<UserDevice> enterpriseNemos = deviceService.getEnterpriseDevices(po.getEnterpriseId(), DeviceType.HARD.getValue());
            for (UserDevice userDevicePO : enterpriseNemos) {
                updateEnterpriseNemoProfileByNemoSn(userDevicePO.getDeviceSN(), po);
            }
        }

        return po.getId();
    }

    private void updateEnterpriseNemoProfileByNemoSn(String nemoSn, EnterpriseNemoProfile enterpriseNemoProfilePO) {
        EnterpriseNemo enterpriseNemoPO = enterpriseNemoMapper.getEnterpriseNemo(nemoSn);
        if (null != enterpriseNemoPO){
            enterpriseNemoPO.setEnterpriseProfileId(enterpriseNemoProfilePO.getId());
            enterpriseNemoMapper.updateEnterpriseNemo(enterpriseNemoPO);
        }else {
            enterpriseNemoPO = new EnterpriseNemo(nemoSn);
            enterpriseNemoPO.setEnterpriseProfileId(enterpriseNemoProfilePO.getId());
            enterpriseNemoMapper.addEnterpriseNemo(enterpriseNemoPO);
        }
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public String updateEnterpriseNemoProfileByProfileId(String profileId, EnterpriseNemoProfile profile)
            throws ServiceException {
        if(profile == null) {
            return null;
        }
        log.debug("updateEnterpriseNemoProfile - Update enerprise nemo profile [" + profile.toString() + "]");
        EnterpriseNemoProfile po = null;
        if(profileId != null && !profileId.trim().isEmpty()){
            po = enterpriseNemoProfileMapper.getEnterpriseProfileByEnterpriseId(profileId);
        }
        if(po != null) {
            if (!profile.isEnhancePassword()) {
                if (po.isEnhancePassword()) {
                    log.error("Can't cancel enhance password.");
                    throw new ServiceException("ConfigureEnterpreseProfileError", ErrorStatus.INVALID_CANCEL_ENHANCED_PASSWORD);
                }
            }
            else {
                if (!po.isEnhancePassword()) {
                    log.debug("Enable enhance password function.");
                    po.setPasswordExpireInterval(profile.getPasswordExpireInterval()* DAY_TO_MS);
                }
                else {
                    if ((profile.getPasswordExpireInterval()* DAY_TO_MS) != po.getPasswordExpireInterval()) {
                        log.debug("Enhance password expire interval change from [" + po.getPasswordExpireInterval() + "] to [" + profile.getPasswordExpireInterval() + "].");

                        if (StringUtils.isNotEmpty(po.getCustomizedKey())) {
                            onPasswordExpireIntervalChanged(profile, po);
                        }
                        else {
                            log.warn("Enterprise [" + profile.getDisplayName() + "] is not customized enterprise");
                        }
                        po.setPasswordExpireInterval(profile.getPasswordExpireInterval()* DAY_TO_MS);
                    }
                }
                po.setEnhancePassword(true);
            }

            po.setDisplayName(profile.getDisplayName());
            po.setSpecialNemoBinding(profile.isSpecialNemoBinding());
            po.setSpecialUI(profile.isSpecialUI());
            enterpriseNemoProfileMapper.updateEnterpriseNemoProfile(po);
        }else {
            po = new EnterpriseNemoProfile();
            po.setDisplayName(profile.getDisplayName());
            enterpriseNemoProfileMapper.addEnterpriseNemoProfile(po);
        }
        return po.getId();
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void applyEnterpriseProfileConfig(List<DeviceConfigUpdate> configs, final String profileId) {
        if(configs == null || configs.size() == 0) {
            return;
        }
        EnterpriseNemoProfile enterpriseNemoProfilePO = enterpriseNemoProfileMapper.getEnterpriseProfileByProfileId(profileId);
        if(enterpriseNemoProfilePO == null) {
            log.error("Invalid enterprise profile id: " + profileId);
            return;
        }
        Set<EnterpriseNemoConfig> enterpriseNemoConfigPOS = enterpriseNemoConfigService.getEnterpriseNemoConfigs(profileId);
        enterpriseNemoProfilePO.setConfigs(enterpriseNemoConfigPOS);
        Map<String, EnterpriseNemoConfig> existedConfigs = new HashMap<>();
        if(enterpriseNemoConfigPOS != null) {
            for(EnterpriseNemoConfig enterpriseNemoConfigPO : enterpriseNemoConfigPOS) {
                existedConfigs.put(enterpriseNemoConfigPO.getKey(), enterpriseNemoConfigPO);
            }
        }
        for(DeviceConfigUpdate deviceConfigUpdate : configs) {
            log.debug("Apply " + deviceConfigUpdate + " to " + profileId);
            EnterpriseNemoConfig enterpriseNemoConfigPO = existedConfigs.get(deviceConfigUpdate.getKey());
            if(enterpriseNemoConfigPO == null) {
                enterpriseNemoConfigPO = new EnterpriseNemoConfig(deviceConfigUpdate.getConfigName(), StringUtils.isBlank(deviceConfigUpdate.getConfigValue())? "" :deviceConfigUpdate.getConfigValue(),
                        deviceConfigUpdate.getClientConfigName(), deviceConfigUpdate.getDeviceSubType(),enterpriseNemoProfilePO.getId());

                enterpriseNemoProfilePO.getConfigs().add(enterpriseNemoConfigPO);
            } else {
                enterpriseNemoConfigPO.setConfigValue(deviceConfigUpdate.getConfigValue());
            }
            enterpriseNemoConfigService.saveOrUpdateEnterpriseNemoConfig(enterpriseNemoConfigPO);
            // 发布事件
            applicationEventPublisher.publishEvent(new EnterpriseConfigChangedEvent(enterpriseNemoConfigPO,enterpriseNemoProfilePO.getEnterpriseId()));
        }
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void applyEnterpiseProfileConfig(Map<String, String> configs, String profileId) {
        if(configs == null){
            return;
        }
        log.debug("Apply enterprise nemo config: " + configs + " for profile: " + profileId);
        EnterpriseNemoProfile profile = enterpriseNemoProfileMapper.getEnterpriseProfileByProfileId(profileId);
        final RestNemoConfig[] nemoconfigs = new RestNemoConfig[configs.size()];
        int i = 0;
        Set<EnterpriseNemoConfig> pos = profile.getConfigs();
        Map<String, EnterpriseNemoConfig> existedConfigs = new HashMap<> ();
        if(pos != null) {
            Iterator<EnterpriseNemoConfig> enterpriseNemoConfigPOIterator = pos.iterator();
            while(enterpriseNemoConfigPOIterator.hasNext()) {
                EnterpriseNemoConfig po = enterpriseNemoConfigPOIterator.next();
                if(po.getClientConfigName() != null && !po.getClientConfigName().trim().isEmpty()) {
                    enterpriseNemoConfigPOIterator.remove();
                } else {
                    existedConfigs.put(po.getConfigName(), po);
                }
            }
        }
        for(Map.Entry<String, String> entry : configs.entrySet()) {
            if(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION.equals(entry.getKey())
                    || Configs.configsWithClientName.contains(entry.getKey())) {
                Map<?, ?> value = null;
                try {
                    value = Jackson.getObjectMapper().readValue(entry.getValue(), Map.class);
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
                if (value != null) {
                    for(Map.Entry<?, ?> valueEntry : value.entrySet()) {
                        profile.getConfigs().add(new EnterpriseNemoConfig((String) valueEntry.getKey(), (String) valueEntry.getValue(),
                                entry.getKey(), TYPE_NEMO_CONFIG, profile.getId()));
                    }
                }
            } else {
                if(existedConfigs.containsKey(entry.getKey())) {
                    EnterpriseNemoConfig po = existedConfigs.get(entry.getKey());
                    po.setConfigValue(entry.getValue());
                } else {
                    profile.getConfigs().add(new EnterpriseNemoConfig(entry.getKey(), entry.getValue(), profile.getId()));
                }
                nemoconfigs[i++] = new RestNemoConfig(entry.getKey(), entry.getValue());
            }
        }

        log.debug("Notify nemos in this enterprise.");
        Set<UserDevice> nemos = deviceService.getEnterpriseNemos(profileId);
        if(nemos != null) {
            try {
                for(UserDevice nemo : nemos) {
                    log.info("Notify nemo config change: " + nemo.getId());
                    notificationService.notifyChangeNemoConfig(nemoconfigs, nemo.getId());
                    notificationService.notifyNemoConfigChange(nemo, nemoconfigs);
                }
            } catch (DataAccessException e) {
                throw new ServiceException("Failed to notify nemos config change", ErrorStatus.INTERNAL_DATABASE_ERROR);
            }
        }
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void applyEnterpriseProfileFeature(List<EnterpriseNemoFeature> features,
                                              final String profileId) {
        log.debug("Apply nemo feature change for profile: " + profileId);
        if(features == null)
            return;
        EnterpriseNemoProfile profile = enterpriseNemoProfileMapper.getEnterpriseProfileByProfileId(profileId);
        List<EnterpriseNemoFeature> pos = enterpriseNemoProfileMapper.listByProfileId(profile.getId());
        Map<String, EnterpriseNemoFeature> existedFeatures = new HashMap<> ();
        if(pos != null) {
            for(EnterpriseNemoFeature po : pos) {
                existedFeatures.put(po.getFeatureId(), po);
            }
        }
        for(EnterpriseNemoFeature feature : features) {
            EnterpriseNemoFeature po = existedFeatures.get(feature.getFeatureId());
            if(po != null) {
                po.setFeatureStatus(feature.getFeatureStatus());
                po.setHasMainShortCut(feature.getHasMainShortCut());
                enterpriseNemoProfileMapper.updateEnterpriseNemoFeature(po);
            } else {
                CustomizeFeature cf = customizeFeatureService.getCustomizeFeatureById(feature.getFeatureId());
                if(cf != null){
                    EnterpriseNemoFeature featurePO = new EnterpriseNemoFeature(cf.getId(), feature.getFeatureStatus(), profile.getId());
                    featurePO.setHasMainShortCut(feature.getHasMainShortCut());
                    try {
                        EnterpriseNemoFeature ef = enterpriseNemoProfileMapper.getEnterpriseNemoFeatures(cf.getId(),profileId);
                        if (null != ef){
                            featurePO.setId(ef.getId());
                            enterpriseNemoProfileMapper.updateEnterpriseNemoFeature(featurePO);
                        }else {
                            enterpriseNemoProfileMapper.addEnterpriseNemoFeature(featurePO);
                        }
                    } catch (DataAccessException e) {
                        log.error("fail to save enterpriseNemoFeature!", e);
                    }
                }
            }
        }

        log.debug("Notify nemos in this enterprise.");
        Set<UserDevice> devices = deviceService.getEnterpriseNemos(profileId);
        if(devices != null) {
            log.info("Notify nemos feature change: " + devices);
            for(UserDevice device : devices) {
                notificationService.notifyNemoFeatureChange(getCustomizeFeatures(device), device);
            }
        }
    }

    @Override
    public Map<String, String> getEnterpriseConfig(String enterpriseId) {
        EnterpriseNemoProfile po = enterpriseNemoProfileMapper.getEnterpriseProfileByEnterpriseId(enterpriseId);
        Map<String, String> ret = new HashMap<>();
        if(po != null) {
            Map<String, Map<String, String>> allConfigs = getAllEnterpriseConfigs(po.getId());
            for(String clientConfigName : Configs.configsWithClientName) {
                if(allConfigs.containsKey(clientConfigName)) {
                    try {
                        ret.put(clientConfigName, Jackson.getObjectMapper().writeValueAsString(allConfigs.get(clientConfigName)));
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                    }
                }
            }
            if(allConfigs.containsKey(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION)) {
                try {
                    ret.put(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION,
                            Jackson.getObjectMapper().writeValueAsString(allConfigs.get(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION)));
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            }
            if(allConfigs.containsKey(DeviceConfig.COMMON_CONFIG_KEY)) {
                ret.putAll(allConfigs.get(DeviceConfig.COMMON_CONFIG_KEY));
            }
        }
        return ret;
    }


    @Override
    public String updateEnterpriseNemoProfileByFieldUpdate(EnterpriseNemoProfileFieldUpdateReq fieldUpdateReq) {
        String enterpriseId = fieldUpdateReq.getEnterpriseId();
        EnterpriseNemoProfile po = enterpriseNemoProfileMapper.getEnterpriseProfileByEnterpriseId(enterpriseId);
        if (Objects.isNull(po)){
            po = new EnterpriseNemoProfile();
            po.setEnterpriseId(enterpriseId);
            po.setSpecialClientLogin(fieldUpdateReq.isSpecialClientLogin());
            po.setCustomizedKey(fieldUpdateReq.getCustomizedKey());
            enterpriseNemoProfileMapper.addEnterpriseNemoProfile(po);
        }else{
            po.setSpecialClientLogin(fieldUpdateReq.isSpecialClientLogin());
            po.setCustomizedKey(fieldUpdateReq.getCustomizedKey());
            enterpriseNemoProfileMapper.updateEnterpriseNemoProfileSome(po);
        }
        return po.getId();
    }

    @Override
    public EnterpriseNemoProfile getByEnterpriseId(String enterpriseId) {
        return enterpriseNemoProfileMapper.getEnterpriseProfileByEnterpriseId(enterpriseId);
    }

    @Override
    public List<EnterpriseNemoConfig> getSimpleConfigsByEnterpriseId(String enterpriseId,String configName) {
        EnterpriseNemoProfile enterpriseNemoProfile = getByEnterpriseId(enterpriseId);
        if (Objects.nonNull(enterpriseNemoProfile)){
            return enterpriseNemoConfigService.getProfileConfigs(enterpriseNemoProfile.getId(),configName);
        }
        return null;
    }

    @Override
    public EnterpriseNemoProfile getByCustomizedKey(String customizedKey) {
        return enterpriseNemoProfileMapper.getEnterpriseProfileByCustomizedKey(customizedKey);
    }

    private Map<String, Map<String, String>> getAllEnterpriseConfigs(String enterpriseProfileId) {
        List<EnterpriseNemoConfig> configs = enterpriseNemoConfigService.getProfileConfigs(enterpriseProfileId);
        Map<String, Map<String, String>> allConfigs = new HashMap<>();
        if (configs != null) {
            for (EnterpriseNemoConfig configPO : configs) {
                configPO.addToConfigs(allConfigs);
            }
        }
        return allConfigs;
    }

    private List<CustomizeFeature> getCustomizeFeatures(UserDevice device) {
        return internalApiProxy.getCustomizeFeatures(device,"");
    }

    private EnterpriseNemoProfileUpdateReq enterpriseProfilePO2RestData(EnterpriseNemoProfile profile) {
        EnterpriseNemoProfileUpdateReq profileUpdateReqV2 = new EnterpriseNemoProfileUpdateReq();
        if(profile != null) {

            Set<EnterpriseNemoConfig> configs = enterpriseNemoConfigService.getEnterpriseNemoConfigs(profile.getId());
            if(configs != null && !configs.isEmpty()) {
                List<DeviceConfigUpdate> deviceConfigUpdates = new ArrayList<>();
                for(EnterpriseNemoConfig c : configs) {
                    DeviceConfigUpdate deviceConfigUpdate = new DeviceConfigUpdate(c.getConfigName(), c.getConfigValue(), c.getClientConfigName());
                    deviceConfigUpdate.setDeviceSubType(c.getConfigType());
                    deviceConfigUpdates.add(deviceConfigUpdate);
                }
                profileUpdateReqV2.setConfigs(deviceConfigUpdates);
            }

            //customize_feature 已经迁移到vcs。后期修改调用vcs接口
            log.info("get enterprise profile customer feature from vcs  and profile id is:"+profile.getId() +"and enterprise id is " + profile.getEnterpriseId());
            try {
                List<EnterpriseNemoFeature> efeatures = internalApiProxy.getEnterpriseNemoFeatures(profile.getId());
                if(efeatures != null && !efeatures.isEmpty()) {
                    List<EnterpriseNemoFeature> features = new ArrayList<> ();
                    for(EnterpriseNemoFeature ef : efeatures) {
                        CustomizeFeature customizeFeature = customizeFeatureService.getCustomizeFeatureById(ef.getFeatureId());
                        EnterpriseNemoFeature fea = new EnterpriseNemoFeature(customizeFeature.getId(), ef.getFeatureStatus());
                        fea.setDisplayName(customizeFeature.getDisplayName());
                        features.add(fea);
                    }
                    profileUpdateReqV2.setFeatures(features);
                }
            }catch (Exception e){
                log.error("get enterprise profile customer feature error from vcs :"+profile.getEnterpriseId() ,e);
            }

            profileUpdateReqV2.setProfileId(profile.getId());
            profileUpdateReqV2.setEnterpriseId(profile.getEnterpriseId());
            profileUpdateReqV2.setProfile(convertEnterpriseNemoProfileFromPO(profile));
        }
        return profileUpdateReqV2;
    }

    private void onPasswordExpireIntervalChanged(EnterpriseNemoProfile profile, EnterpriseNemoProfile po) {
        long delta = (profile.getPasswordExpireInterval()* DAY_TO_MS) - po.getPasswordExpireInterval();
        log.debug("ExpireInterval changed, the deltaInterval: " + delta);
        updateUserPasswordExpireTime(po.getEnterpriseId(), delta);

        Set<String> userIdAndDeviceTypeSet = presenceService.getCusomizedUsersByCustomizedKey(po.getCustomizedKey());
        for (String info : userIdAndDeviceTypeSet) {
            String[] userInfo = info.split("_");
            String userProfileId = userInfo[0];
            String deviceType = userInfo[1];
            notificationService.notifyUserPasswordExpireTimeChanged(Long.parseLong(userProfileId), Integer.valueOf(deviceType), delta);
        }
    }

    private void updateUserPasswordExpireTime(String enterpriseId, long addDeltaTimeMS) {
        List<UserProfile> userProfilePOList = deviceService.getUserProfileByEnterpriseId(enterpriseId);

        if (null != userProfilePOList) {
            for (UserProfile userProfilePO : userProfilePOList) {
                try {
                    UserPO userPO = deviceService.getUserById(userProfilePO.getUserId());
                    if (userPO.getPasswordExpireTime() > userPO.getPasswordCreateTime()) {
                        deviceService.updateUserPasswordExpireTimeById(userPO.getId(),userPO.getPasswordExpireTime() + addDeltaTimeMS);
                    }
                }
                catch (Exception e) {
                    log.error("Save UserPO fail, exception: ", e);
                }
            }
        }
    }

    private EnterpriseNemoProfile convertEnterpriseNemoProfileFromPO(EnterpriseNemoProfile profilePO) {
        EnterpriseNemoProfile profile = new EnterpriseNemoProfile(profilePO.getDisplayName());

        log.info("EnterproseNemoProfile: " + profilePO.getDisplayName() + ", enablePassword: " + profilePO.isEnhancePassword() +
                ", customizedKey: " + profilePO.getCustomizedKey());
        if (profilePO.isEnhancePassword() && StringUtils.isNotEmpty((profilePO.getCustomizedKey()))) {
            profile.setEnhancePassword(profilePO.isEnhancePassword());
        }
        profile.setPasswordExpireInterval(profilePO.getPasswordExpireInterval()/ DAY_TO_MS);

        return profile;
    }
}
