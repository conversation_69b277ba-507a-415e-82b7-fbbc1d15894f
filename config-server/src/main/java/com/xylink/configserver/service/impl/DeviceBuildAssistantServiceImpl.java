package com.xylink.configserver.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xylink.configserver.data.bo.TempSeriesConfigBO;
import com.xylink.configserver.data.dto.SubtypeExportImportDto;
import com.xylink.configserver.data.entity.BoxEnterpriseEntity;
import com.xylink.configserver.data.entity.DeviceSubtypeEntity;
import com.xylink.configserver.data.entity.DeviceSubtypeModelExternalEntity;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.data.model.deviceseries.DeviceSeriesEntity;
import com.xylink.configserver.data.model.deviceseries.DeviceSeriesSubtypeEntity;
import com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigDictionaryEntity;
import com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigEntity;
import com.xylink.configserver.data.response.R;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.mapper.*;
import com.xylink.configserver.mapper.deviceseries.DeviceSeriesMapper;
import com.xylink.configserver.mapper.deviceseries.DeviceSeriesSubtypeMapper;
import com.xylink.configserver.mapper.deviceseries.DeviceSubtypeSeriesConfigDictionaryMapper;
import com.xylink.configserver.mapper.deviceseries.DeviceSubtypeSeriesConfigMapper;
import com.xylink.configserver.service.DeviceBuildAssistantService;
import com.xylink.configserver.service.DeviceSubtypeModelExternalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName:DeviceBuildAssistantServiceImpl
 * Package:com.xylink.configserver.service.impl
 * Description:
 *
 * <AUTHOR>
 * @Date 2024/10/17-15:43
 * @Version: v1.0
 */
@Slf4j
@Service
public class DeviceBuildAssistantServiceImpl implements DeviceBuildAssistantService {
    @Autowired
    private LibraDeviceSubtypeModelMapper libraDeviceSubtypeModelMapper;

    @Autowired
    private DeviceSubtypeEntityMapper deviceSubtypeEntityMapper;

    @Autowired
    private BoxEnterpriseEntityMapper boxEnterpriseEntityMapper;

    @Autowired
    private BaseDefaultConfigMapper baseDefaultConfigMapper;

    @Autowired
    private DeviceSeriesSubtypeMapper deviceSeriesSubtypeMapper;

    @Autowired
    private DeviceSeriesMapper deviceSeriesMapper;

    @Autowired
    private DeviceSubtypeSeriesConfigMapper deviceSubtypeSeriesConfigMapper;

    @Autowired
    private DeviceSubtypeSeriesConfigDictionaryMapper deviceSubtypeSeriesConfigDictionaryMapper;

    @Autowired
    private DeviceSubtypeModelExternalService deviceSubtypeModelExternalService;

    @Autowired
    private CommonGroupConfigMapper commonGroupConfigMapper;

    @Override
    public String newDeviceBuildReturn(NewDeviceBuildInfoRequest request, Integer subtype) {
        log.info("newDeviceBuildReturn request:{}", request);
        List<String> sqlList = new ArrayList<>();
        // subTypeModel中查出型号
        List<String> subTypeModelSql = doCreateSubTypeModelSql(request, subtype);
        if (subTypeModelSql != null && !subTypeModelSql.isEmpty()) {
            sqlList.addAll(subTypeModelSql);
        }
        // libra_device_subtype sql处理
        DeviceSubtypeEntity deviceSubtypeEntity = deviceSubtypeEntityMapper
                .selectOne(new LambdaQueryWrapper<DeviceSubtypeEntity>()
                        .eq(DeviceSubtypeEntity::getSubType, subtype));

        List<String> deviceSubtypeSql = doCreateDeviceSubtypeSql(request, deviceSubtypeEntity, subtype);
        if (!deviceSubtypeSql.isEmpty()) {
            sqlList.addAll(deviceSubtypeSql);
        }
        // libra_device_subtype_model_external 处理
        List<String> deviceSubtypeModelExternalSql = doCreateDeviceSubtypeModelExternalSql(request, subtype);
        if (!deviceSubtypeModelExternalSql.isEmpty()) {
            sqlList.addAll(deviceSubtypeModelExternalSql);
        }
        // libra_box_enterprise sql处理
        List<String> boxEnterpriseSql = doCreateBoxEnterpriseSql(request, deviceSubtypeEntity);
        if (!boxEnterpriseSql.isEmpty()) {
            sqlList.addAll(boxEnterpriseSql);
        }
        // libra_base_default_config sql处理
        List<String> baseDefaultConfigSql = doCreateBaseDefaultConfigSql(request, subtype);
        if (baseDefaultConfigSql != null && !baseDefaultConfigSql.isEmpty()) {
            sqlList.addAll(baseDefaultConfigSql);
        }
        // ainemo.libra_common_group_config
        List<String> commonGroupConfigSql = doCreateCommonGroupConfigSql(request, subtype);
        if (commonGroupConfigSql != null && !commonGroupConfigSql.isEmpty()) {
            sqlList.addAll(commonGroupConfigSql);
        }

        // 4K 配置

        // libra_device_series sql处理
        List<String> deviceSeriesSql = doCreateDeviceSeriesSql(request, subtype);
        if (deviceSeriesSql != null && !deviceSeriesSql.isEmpty()) {
            sqlList.addAll(deviceSeriesSql);
        }

        // libra_device_series_subtype sql处理
        List<String> deviceSeriesSubtypeSql = doCreateDeviceSeriesSubtypeSql(request, subtype);
        if (deviceSeriesSubtypeSql != null && !deviceSeriesSubtypeSql.isEmpty()) {
            sqlList.addAll(deviceSeriesSubtypeSql);
        }

        // 系列数据处理
        // 如果是新系列,获取参考系列配置,生成系列sql
        if (!StringUtils.isEmpty(request.getNewSeries())) {
            List<String> seriesSql = doCreateSeriesSql(request, subtype);
            if (seriesSql != null && !seriesSql.isEmpty()) {
                sqlList.addAll(seriesSql);
            }
        }
        // 获取subtype配置,生成subtype sql
        List<String> subtypeSql = doCreateSeriesSubtypeSql(request, subtype);
        if (subtypeSql != null && !subtypeSql.isEmpty()) {
            sqlList.addAll(subtypeSql);
        }
        // sql list转为set 去除重复sql
        Set<String> sqlSet = new LinkedHashSet<>(sqlList);
        // sqList转为字符串,每个字符串以换行符分割
        StringBuilder sqlBuilder = new StringBuilder();
        for (String sql : sqlSet) {
            sqlBuilder.append(sql).append("\n");
        }
        return sqlBuilder.toString();
    }

    private List<String> doCreateDeviceSubtypeModelExternalSql(NewDeviceBuildInfoRequest request, Integer subtype) {
        List<String> sqlList = new ArrayList<>();
        // 查询参照型号模版数据
        DeviceSubtypeModelExternalEntity deviceSubtypeModelExternal = deviceSubtypeModelExternalService
                .getOne(new LambdaQueryWrapper<DeviceSubtypeModelExternalEntity>()
                        .eq(DeviceSubtypeModelExternalEntity::getSubtype, subtype));
        if (deviceSubtypeModelExternal == null) {
            return sqlList;
        }
        // delete 语句 delete
        // from ainemo.libra_device_subtype_model_external
        // where subtype = 8459;
        String deleteSql = "delete from ainemo.libra_device_subtype_model_external where subtype = "
                + request.getSubtype() + ";";
        sqlList.add(deleteSql);
        // 插入语句 subtype 为新型号，multi_image_avc 为参考型号值 insert into
        // ainemo.libra_device_subtype_model_external (subtype, multi_image_avc)
        // values (8459,
        // '["3x3", "3x4", "3x5", "3x6", "6x5", "6x6", "6x7", "7x1", "7x2", "7x3",
        // "7x4", "7x5", "7x6", "7x7", "8x2", "8x3", "8x4", "8x5", "8x6", "8x7", "8x8",
        // "9x2", "9x3", "10x1", "10x2", "10x3", "10x4", "10x5", "10x6", "10x7", "10x8",
        // "11x1", "11x2", "14x1", "13x1", "13x2", "13x3", "13x4", "13x5", "13x6",
        // "15x1", "15x2", "15x3", "16x1", "17x2", "17x3", "17x4", "18x1", "18x2",
        // "20x1", "20x2", "20x3", "20x4", "21x1", "21x2", "21x3", "24x1", "24x2",
        // "25x1", "36x1", "49x1", "64x1"]');
        String insertSql = "insert into ainemo.libra_device_subtype_model_external (subtype, multi_image_avc) values ("
                + request.getSubtype() + ", '" + deviceSubtypeModelExternal.getMultiImageAvc() + "');";
        sqlList.add(insertSql);
        return sqlList;
    }

    private List<String> doCreateSeriesSubtypeSql(NewDeviceBuildInfoRequest request, Integer subtype) {
        List<String> sqlList = new ArrayList<>();
        // 删除新型号系列
        // DELETE ldsc
        // FROM
        // ainemo.libra_device_subtype_series_config ldsc
        // WHERE
        // ldsc.sub_type = 新subtype
        // AND ldsc.type = 'subtype';
        String deleteSql = "delete  from ainemo.libra_device_subtype_series_config  " +
                "where sub_type = " + request.getSubtype() + " and type = 'subtype';";
        sqlList.add(deleteSql);
        // 插入新型号系列
        // INSERT INTO ainemo.libra_device_subtype_series_config (sub_type, series_id,
        // config_id, platform, type)
        // SELECT
        // 新subtype AS sub_type,
        // series_id,
        // config_id,
        // platform,
        // type
        // FROM
        // ainemo.libra_device_subtype_series_config
        // WHERE
        // sub_type = 旧subtype
        // AND type = 'subtype';
        List<DeviceSubtypeSeriesConfigEntity> deviceSubtypeSeriesConfigEntities = deviceSubtypeSeriesConfigMapper
                .selectList(new LambdaQueryWrapper<DeviceSubtypeSeriesConfigEntity>()
                        .eq(DeviceSubtypeSeriesConfigEntity::getSubtype, subtype)
                        .eq(DeviceSubtypeSeriesConfigEntity::getType, "subtype"));
        List<TempSeriesConfigBO> collect = deviceSubtypeSeriesConfigEntities.stream().map(config -> {
            TempSeriesConfigBO tempSeriesConfigBO = new TempSeriesConfigBO();
            tempSeriesConfigBO.setSubType(request.getSubtype());
            tempSeriesConfigBO.setSeriesId(null);
            DeviceSubtypeSeriesConfigDictionaryEntity deviceSubtypeSeriesConfigDictionaryEntity = deviceSubtypeSeriesConfigDictionaryMapper
                    .selectOne(new LambdaQueryWrapper<DeviceSubtypeSeriesConfigDictionaryEntity>()
                            .eq(DeviceSubtypeSeriesConfigDictionaryEntity::getId, config.getConfigId()));
            // 查询表获取 libra_device_subtype_series_config_dictionary
            tempSeriesConfigBO.setConfigName(deviceSubtypeSeriesConfigDictionaryEntity.getConfigCode());
            tempSeriesConfigBO.setPlatform(config.getPlatform());
            tempSeriesConfigBO.setType(config.getType());
            return tempSeriesConfigBO;
        }).collect(Collectors.toList());

        collect.forEach(config -> {
            // sub_type 使用request.getSubtype(),其他配置使用config item属性
            String insertSql = "insert into ainemo.libra_device_subtype_series_config (sub_type, series_id, config_id, platform, type) "
                    +
                    "select " + request.getSubtype() + ", null, id, '" + config.getPlatform() + "', '"
                    + config.getType() + "' " +
                    "from ainemo.libra_device_subtype_series_config_dictionary " +
                    "where config_code = '" + config.getConfigName() + "' and dictionary_type = 'SUBTYPE';";
            sqlList.add(insertSql);
        });
        /*
         * String insertSql =
         * "insert into ainemo.libra_device_subtype_series_config (sub_type, series_id, config_id, platform, type) "
         * +
         * "select " + request.getSubtype() +
         * " as sub_type, series_id, config_id, platform, type " +
         * "from ainemo.libra_device_subtype_series_config " +
         * "where sub_type = " + subtype + " and type = 'subtype';";
         * sqlList.add(insertSql);
         */
        return sqlList;
    }

    private List<String> doCreateSeriesSql(NewDeviceBuildInfoRequest request, Integer subtype) {
        // 删除新的系列配置 DELETE ldsc
        // FROM
        // ainemo.libra_device_subtype_series_config ldsc
        // JOIN ainemo.libra_device_series_subtype lds ON ldsc.series_id = lds.series_id
        // WHERE
        // lds.sub_type = 新subtype
        // AND ldsc.type = 'series';
        List<String> sqlList = new ArrayList<>();
        // delete from ainemo.libra_device_subtype_series_config
        // where type = 'series' and series_id in (select series_id from
        // ainemo.libra_device_series_subtype dss where dss.sub_type = 8459 );
        // 删除新的系列配置
        String deleteSql = "delete from ainemo.libra_device_subtype_series_config " +
                "where type = 'series' and series_id in (select series_id from ainemo.libra_device_series_subtype dss where dss.sub_type = "
                + request.getSubtype() + ");";
        sqlList.add(deleteSql);
        // 查询出所有旧的系列配置
        // 1.查出旧的系列id
        DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = deviceSeriesSubtypeMapper
                .selectOne(new LambdaQueryWrapper<DeviceSeriesSubtypeEntity>()
                        .eq(DeviceSeriesSubtypeEntity::getSubtype, subtype));
        // 2.查出旧的系列配置
        Long seriesId = deviceSeriesSubtypeEntity.getSeriesId();
        List<DeviceSubtypeSeriesConfigEntity> deviceSubtypeSeriesConfigEntities = deviceSubtypeSeriesConfigMapper
                .selectList(new LambdaQueryWrapper<DeviceSubtypeSeriesConfigEntity>()
                        .eq(DeviceSubtypeSeriesConfigEntity::getSeriesId, seriesId)
                        .eq(DeviceSubtypeSeriesConfigEntity::getType, "series"));
        if (deviceSubtypeSeriesConfigEntities == null || deviceSubtypeSeriesConfigEntities.isEmpty()) {
            return sqlList;
        }
        List<TempSeriesConfigBO> tempSeriesConfigs = deviceSubtypeSeriesConfigEntities.stream().map(config -> {
            TempSeriesConfigBO tempSeriesConfigBO = new TempSeriesConfigBO();
            tempSeriesConfigBO.setSubType(null);
            // 夸表获取 libra_device_series_subtype
            tempSeriesConfigBO.setSeriesId(null);
            DeviceSubtypeSeriesConfigDictionaryEntity deviceSubtypeSeriesConfigDictionaryEntity = deviceSubtypeSeriesConfigDictionaryMapper
                    .selectOne(new LambdaQueryWrapper<DeviceSubtypeSeriesConfigDictionaryEntity>()
                            .eq(DeviceSubtypeSeriesConfigDictionaryEntity::getId, config.getConfigId()));
            // 查询表获取 libra_device_subtype_series_config_dictionary
            tempSeriesConfigBO.setConfigName(deviceSubtypeSeriesConfigDictionaryEntity.getConfigCode());
            tempSeriesConfigBO.setPlatform(config.getPlatform());
            tempSeriesConfigBO.setType(config.getType());
            return tempSeriesConfigBO;
        }).collect(Collectors.toList());
        tempSeriesConfigs.forEach(config -> {
            String sql = "INSERT INTO ainemo.libra_device_subtype_series_config (sub_type, series_id, config_id, platform, type) "
                    +
                    "SELECT " + null + " AS sub_type, " +
                    "(SELECT series_id FROM ainemo.libra_device_series_subtype WHERE sub_type = " + request.getSubtype()
                    + ") AS series_id, " +
                    // SELECT id
                    // FROM ainemo.libra_device_subtype_series_config_dictionary
                    // WHERE config_code = 'inCallToolbar' and dictionary_type = 'SERIES';
                    "(SELECT id FROM ainemo.libra_device_subtype_series_config_dictionary WHERE config_code = '"
                    + config.getConfigName() + "' and dictionary_type = 'SERIES') AS config_id, " +
                    "'" + config.getPlatform() + "' AS platform, 'series' AS type " +
                    "FROM ainemo.libra_device_subtype_series_config_dictionary " +
                    "WHERE config_code = '" + config.getConfigName() + "' " +
                    "AND EXISTS (" +
                    "    SELECT 1 " +
                    "    FROM ainemo.libra_device_subtype_series_config_dictionary " +
                    "    WHERE config_code = '" + config.getConfigName() + "'" +
                    ");";
            sqlList.add(sql);
        });

        // 插入新的系列配置
        /*
         * String insertSql =
         * "insert into ainemo.libra_device_subtype_series_config (sub_type, series_id, config_id, platform, type) "
         * +
         * "select null as sub_type, dst.new_series_id as series_id, config_id, platform, type "
         * +
         * "from ainemo.libra_device_subtype_series_config ldsc " +
         * "join (select lds1.sub_type as old_sub_type, lds1.series_id as old_series_id, lds2.series_id as new_series_id "
         * +
         * "from ainemo.libra_device_series_subtype lds1 " +
         * "join ainemo.libra_device_series_subtype lds2 on lds1.sub_type = " + subtype
         * + " and lds2.sub_type = " + request.getSubtype() + ") dst " +
         * "on ldsc.series_id = dst.old_series_id " +
         * "where ldsc.type = 'series';";
         * sqlList.add(insertSql);
         */
        return sqlList;
    }

    private List<String> doCreateCommonGroupConfigSql(NewDeviceBuildInfoRequest request, Integer subtype) {
        // INSERT INTO ainemo.libra_common_group_config(config_name, config_value,
        // config_group, client_config_name, config_type) (
        // SELECT config2.config_name, config2.config_value, config2.config_group,
        // config2.client_config_name, '8306'
        // FROM ainemo.libra_common_group_config config2 WHERE config2.config_type=(
        // SELECT sub_type FROM ainemo.libra_device_subtype_model WHERE category='AE800'
        // )
        // );
        List<String> sqlList = new ArrayList<>();
        // 删除新型号系列
        // DELETE FROM ainemo.libra_common_group_config WHERE config_type=(
        // SELECT sub_type FROM ainemo.libra_device_subtype_model WHERE category='GE600'
        // );
        String deleteSql = "delete from ainemo.libra_common_group_config where config_type = " + request.getSubtype()
                + ";";
        sqlList.add(deleteSql);
        // 插入新型号系列
        String insertSql = "insert into ainemo.libra_common_group_config (config_name, config_value, config_group, client_config_name, config_type) "
                +
                "(select config2.config_name, config2.config_value, config2.config_group, config2.client_config_name, "
                + request.getSubtype() + " " +
                "from  ainemo.libra_common_group_config config2 where config2.config_type = " + subtype + ");";
        sqlList.add(insertSql);
        return sqlList;
    }

    private List<String> doCreateDeviceSeriesSubtypeSql(NewDeviceBuildInfoRequest request, Integer subtype) {
        // delete from ainemo.libra_device_series_subtype where sub_type = 8306;
        // insert into ainemo.libra_device_series_subtype (series_id, sub_type)(
        // select id, 8306 from ainemo.libra_device_series where series_name = 'GE600系列'
        // );
        List<String> sqlList = new ArrayList<>();
        // 删除新型号系列
        String deleteSql = "delete from ainemo.libra_device_series_subtype where sub_type = " + request.getSubtype()
                + ";";
        sqlList.add(deleteSql);

        String newSeries = request.getNewSeries();
        String insertSql;
        if (StringUtils.isEmpty(newSeries)) {
            // 获取系列名称
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = deviceSeriesSubtypeMapper
                    .selectOne(new LambdaQueryWrapper<DeviceSeriesSubtypeEntity>()
                            .eq(DeviceSeriesSubtypeEntity::getSubtype, subtype));
            if (deviceSeriesSubtypeEntity == null) {
                return sqlList;
            }
            Long seriesId = deviceSeriesSubtypeEntity.getSeriesId();
            // 查询系列名称
            DeviceSeriesEntity deviceSeriesEntity = deviceSeriesMapper
                    .selectOne(new LambdaQueryWrapper<DeviceSeriesEntity>()
                            .eq(DeviceSeriesEntity::getId, seriesId));
            newSeries = deviceSeriesEntity.getSeriesName();
        }
        // 插入新型号系列
        insertSql = "insert into ainemo.libra_device_series_subtype (series_id, sub_type) " +
                "(select id, " + request.getSubtype() + " from ainemo.libra_device_series where series_name = '"
                + newSeries + "');";

        sqlList.add(insertSql);

        return sqlList;
    }

    private List<String> doCreateDeviceSeriesSql(NewDeviceBuildInfoRequest request, Integer subtype) {
        String newSeries = request.getNewSeries();
        // 查询是否有新型号系列定义
        if (!StringUtils.isEmpty(newSeries)) {
            List<String> sqlList = new ArrayList<>();
            // delete from ainemo.libra_device_series where series_name = 'GE600系列';
            // insert into ainemo.libra_device_series (series_name, special) values
            // ('GE600系列', 0);
            String deleteSql = "delete from ainemo.libra_device_series where series_name = '" + newSeries + "';";
            sqlList.add(deleteSql);
            String insertSql = "insert into ainemo.libra_device_series (series_name, special) values ('" + newSeries
                    + "', 0);";
            sqlList.add(insertSql);
            return sqlList;
        }
        return null;
    }

    private List<String> doCreateBaseDefaultConfigSql(NewDeviceBuildInfoRequest request, Integer subtype) {
        // 查询参照型号模版数据
        List<NemoBaseDefaultConfig> baseDefaultConfigs = baseDefaultConfigMapper
                .selectList(new LambdaQueryWrapper<NemoBaseDefaultConfig>()
                        .eq(NemoBaseDefaultConfig::getConfigType, subtype));
        if (baseDefaultConfigs != null && !baseDefaultConfigs.isEmpty()) {
            List<String> sqlList = new ArrayList<>();
            Integer targetSubType = request.getSubtype();
            // 删除语句 delete from ainemo.libra_base_default_config where config_type = 8306;
            String deleteSql = "delete from ainemo.libra_base_default_config where config_type = " + targetSubType
                    + ";";
            sqlList.add(deleteSql);
            // 插入语句 insert into ainemo.libra_base_default_config (config_name, config_value,
            // config_type, product_family, client_config_name, base_config_type)
            // values ('nemo.base.default.config.8306.1', '1', 8306, 'nemo',
            // 'nemo.base.default.config.8306.1', 0),('nemo.base.default.config.8306.1',
            // '1', 8306, 'nemo', 'nemo.base.default.config.8306.1', 0);
            // nemoBaseDefaultConfigs 转为hashmap,key 为 item 中的getClientConfigName() + "_" +
            // getConfigName()
            // 有重复问题,变更为使用for循环处理
            Map<String, NemoBaseDefaultConfig> cacheMap = new HashMap<>();
            for (NemoBaseDefaultConfig baseDefaultConfig : baseDefaultConfigs) {
                String prefix = baseDefaultConfig.getClientConfigName();
                if (StringUtils.isEmpty(prefix)) {
                    prefix = "common";
                }
                cacheMap.put(prefix + "_" + baseDefaultConfig.getConfigName(), baseDefaultConfig);
            }
            List<NewDeviceBuildParam> params = request.getParams();
            if (!(params == null || params.isEmpty())) {
                params.forEach(param -> {
                    String key = param.getPName() + "_" + param.getCName();
                    NemoBaseDefaultConfig nemoBaseDefaultConfig = cacheMap.get(key);
                    if (nemoBaseDefaultConfig != null) {
                        nemoBaseDefaultConfig.setConfigValue(param.getValue());
                    }
                });
            }

            List<NemoBaseDefaultConfig> nemoBaseDefaultConfigs = new ArrayList(cacheMap.values());

            String prefixSql = "insert into ainemo.libra_base_default_config (config_name, config_value, config_type, product_family, client_config_name, base_config_type) values ";
            StringBuilder insertSql = new StringBuilder(prefixSql);
            // 循环插入
            for (int i = 0; i < nemoBaseDefaultConfigs.size(); i++) {
                insertSql.append("(");
                insertSql.append("'" + nemoBaseDefaultConfigs.get(i).getConfigName() + "', ");
                insertSql.append("'" + nemoBaseDefaultConfigs.get(i).getConfigValue() + "', ");
                insertSql.append(targetSubType + ", ");

                // 使用三元运算符处理可能为 null 的字段
                insertSql.append(nemoBaseDefaultConfigs.get(i).getProductFamily() != null
                        ? "'" + nemoBaseDefaultConfigs.get(i).getProductFamily() + "'"
                        : "null");
                insertSql.append(", ");
                insertSql.append(nemoBaseDefaultConfigs.get(i).getClientConfigName() != null
                        ? "'" + nemoBaseDefaultConfigs.get(i).getClientConfigName() + "'"
                        : "null");
                insertSql.append(", ");

                insertSql.append(nemoBaseDefaultConfigs.get(i).getBaseConfigType());

                if (i == nemoBaseDefaultConfigs.size() - 1) {
                    insertSql.append(");");
                } else {
                    insertSql.append("),");
                }
            }

            sqlList.add(insertSql.toString());
            return sqlList;
        }
        return null;
    }

    private List<String> doCreateBoxEnterpriseSql(NewDeviceBuildInfoRequest request,
                                                  DeviceSubtypeEntity deviceSubtypeEntity) {
        String packageName = deviceSubtypeEntity.getPackageName();
        // 查询boxEnterprise
        // UPDATE ainemo.libra_box_enterprise SET model_list=CONCAT(model_list, ',',
        // 'TVBox_GE600') WHERE package_name = 'com.xylink.gill';
        String updateSql = "update ainemo.libra_box_enterprise set model_list = CONCAT(model_list, ',', '"
                + request.getModel() + "') where package_name = '" + packageName + "';";
        List<String> sqlList = new ArrayList<>();
        // 生成更新sql
        // String updateSql = "update ainemo.libra_box_enterprise set model_list = '" +
        // boxEnterpriseEntity.getModelList() + "' where package_name = '" + packageName
        // + "';";
        sqlList.add(updateSql);
        return sqlList;
    }

    private List<String> doCreateDeviceSubtypeSql(NewDeviceBuildInfoRequest request,
                                                  DeviceSubtypeEntity deviceSubtypeEntity, Integer subtype) {
        List<String> sqlList = new ArrayList<>();
        // 创建删除语句
        String deleteSql = "delete from ainemo.libra_device_subtype where sub_type = " + request.getSubtype() + ";";
        sqlList.add(deleteSql);
        // 查询出模版型号数据
        if (deviceSubtypeEntity == null) {
            return sqlList;
        }
        // uuid生成id
        deviceSubtypeEntity.setId(UUID.randomUUID().toString().replace("-", ""));
        // 设置subtype
        deviceSubtypeEntity.setSubType(request.getSubtype().shortValue());
        // 设置model
        deviceSubtypeEntity.setModel(request.getModel());
        // 设置displayModel
        deviceSubtypeEntity.setDisplayModel(request.getDeviceName());
        // 设置referenceModel
        deviceSubtypeEntity.setReferenceModel(deviceSubtypeEntity.getSubType());
        // 拼装insertSql
        String insertSql = "insert into ainemo.libra_device_subtype (id, sub_type, model, package_name, display_model, reference_model) "
                +
                "values ('" + deviceSubtypeEntity.getId() + "', " + deviceSubtypeEntity.getSubType() + ", '"
                + deviceSubtypeEntity.getModel() + "', " +
                "'" + deviceSubtypeEntity.getPackageName() + "', '" + deviceSubtypeEntity.getDisplayModel() + "', "
                + subtype + ");";
        sqlList.add(insertSql);

        return sqlList;
    }

    private List<String> doCreateSubTypeModelSql(NewDeviceBuildInfoRequest request, Integer subtype) {
        LibraDeviceSubtypeModel subtypeModel = libraDeviceSubtypeModelMapper
                .selectOne(new LambdaQueryWrapper<LibraDeviceSubtypeModel>()
                        .eq(LibraDeviceSubtypeModel::getSubType, subtype));
        if (subtypeModel == null) {
            return null;
        }
        Integer subType = request.getSubtype();
        subtypeModel.setId(subType + "");
        subtypeModel.setSubType(subType.shortValue());
        String deviceName = request.getDeviceName();
        subtypeModel.setCategory(deviceName);
        subtypeModel.setCategoryDisplay(deviceName);

        List<String> sqlList = new ArrayList<>();
        // 先生成尝试删除新型号的sql
        String deleteSql = "delete from ainemo.libra_device_subtype_model where id = '" + subType + "';";
        sqlList.add(deleteSql);
        // 拼装insertSql id, sub_type, type, category, category_display, type_category,
        // is_charge_session, is_charge_port, is_present_session,
        // is_join_enterprise, is_network_Topology, is_modify_manager,
        // cart_vip_type, cart_session_type, is_adjuste_volume, is_multi_image,
        // multi_image, device_numer_prefix, is_associate, sn_prefix, description,
        // is_touch_screen, is_third_device, inspect, is_multi_image_poll,
        // charge_port_present_months, cart_vip_present_months,
        // is_record_soft_version, charge_resolution, is_integrated_style, can_k4,
        // soft_reset_clear_vod
        //hot_standby
        String insertSql = "insert into ainemo.libra_device_subtype_model (id, sub_type, type, category, category_display, type_category, "
                +
                "is_charge_session, is_charge_port, is_present_session, is_join_enterprise, is_network_Topology, is_modify_manager, "
                +
                "cart_vip_type, cart_session_type, is_adjuste_volume, is_multi_image, multi_image, device_numer_prefix, is_associate, "
                +
                "sn_prefix, description, is_touch_screen, is_third_device, inspect, is_multi_image_poll, charge_port_present_months, "
                +
                "cart_vip_present_months, is_record_soft_version, charge_resolution, is_integrated_style, can_k4, soft_reset_clear_vod) "
                +
                "values ('" + subtypeModel.getId() + "', " +
                (subtypeModel.getSubType() == null ? null : subtypeModel.getSubType()) + ", " +
                (subtypeModel.getType() == null ? null : subtypeModel.getType()) + ", " +
                (subtypeModel.getCategory() == null ? null : "'" + subtypeModel.getCategory() + "'") + ", " +
                (subtypeModel.getCategoryDisplay() == null ? null : "'" + subtypeModel.getCategoryDisplay() + "'")
                + ", " +
                (subtypeModel.getTypeCategory() == null ? null : "'" + subtypeModel.getTypeCategory() + "'") + ", " +
                (subtypeModel.getIsChargeSession() == null ? null : (subtypeModel.getIsChargeSession() ? 1 : 0)) + ", "
                +
                (subtypeModel.getIsChargePort() == null ? null : (subtypeModel.getIsChargePort() ? 1 : 0)) + ", " +
                (subtypeModel.getIsPresentSession() == null ? null : (subtypeModel.getIsPresentSession() ? 1 : 0))
                + ", " +
                (subtypeModel.getIsJoinEnterprise() == null ? null : (subtypeModel.getIsJoinEnterprise() ? 1 : 0))
                + ", " +
                (subtypeModel.getIsNetworkTopology() == null ? null : (subtypeModel.getIsNetworkTopology() ? 1 : 0))
                + ", " +
                (subtypeModel.getIsModifyManager() == null ? null : (subtypeModel.getIsModifyManager() ? 1 : 0)) + ", "
                +
                (subtypeModel.getCartVipType() == null ? null : "'" + subtypeModel.getCartVipType() + "'") + ", " +
                (subtypeModel.getCartSessionType() == null ? null : "'" + subtypeModel.getCartSessionType() + "'")
                + ", " +
                (subtypeModel.getIsAdjusteVolume() == null ? null : (subtypeModel.getIsAdjusteVolume() ? 1 : 0)) + ", "
                +
                (subtypeModel.getIsMultiImage() == null ? null : (subtypeModel.getIsMultiImage() ? 1 : 0)) + ", " +
                (subtypeModel.getMultiImage() == null ? null : "'" + subtypeModel.getMultiImage() + "'") + ", " +
                (subtypeModel.getDeviceNumerPrefix() == null ? null : "'" + subtypeModel.getDeviceNumerPrefix() + "'")
                + ", " +
                (subtypeModel.getIsAssociate() == null ? 0 : (subtypeModel.getIsAssociate() ? 1 : 0)) + ", " +
                (subtypeModel.getSnPrefix() == null ? null : "'" + subtypeModel.getSnPrefix() + "'") + ", " +
                "'" + request.getDeviceName() + "'" + ", " +
                (subtypeModel.getIsTouchScreen() == null ? 0 : (subtypeModel.getIsTouchScreen() ? 1 : 0)) + ", " +
                (subtypeModel.getIsThirdDevice() == null ? 0 : (subtypeModel.getIsThirdDevice() ? 1 : 0)) + ", " +
                (subtypeModel.getInspect() == null ? 0 : (subtypeModel.getInspect() ? 1 : 0)) + ", " +
                (subtypeModel.getIsMultiImagePoll() == null ? 0 : (subtypeModel.getIsMultiImagePoll() ? 1 : 0)) + ", " +
                (subtypeModel.getChargePortPresentMonths() == null ? 12 : subtypeModel.getChargePortPresentMonths())
                + ", " +
                (subtypeModel.getCartVipPresentMonths() == null ? 12 : subtypeModel.getCartVipPresentMonths()) + ", " +
                (subtypeModel.getIsRecordSoftVersion() == null ? 0 : (subtypeModel.getIsRecordSoftVersion() ? 1 : 0))
                + ", " +
                (subtypeModel.getChargeResolution() == null ? "'720P'" : "'" + subtypeModel.getChargeResolution() + "'")
                + ", " +
                (subtypeModel.getIsIntegratedStyle() == null ? 0 : subtypeModel.getIsIntegratedStyle()) + ", " +
                (subtypeModel.getCanK4() == null ? 0 : (subtypeModel.getCanK4() ? 1 : 0)) + ", " +
                (subtypeModel.getIsRecordSoftVersion() == null ? 1 : (subtypeModel.getIsRecordSoftVersion() ? 1 : 0)) +
                (subtypeModel.getHotStandby() == null ? 0 : subtypeModel.getHotStandby())
                + ");";
        sqlList.add(insertSql);
        return sqlList;
    }

    @Override
    public SubtypeExportImportDto subtypeBaseExport(Integer subtype) {
        // 根据subtype导出各个entity对象
        // 1.libra_device_subtype_model
        LibraDeviceSubtypeModel libraDeviceSubtypeModel = libraDeviceSubtypeModelMapper
                .selectOne(new LambdaQueryWrapper<LibraDeviceSubtypeModel>()
                        .eq(LibraDeviceSubtypeModel::getSubType, subtype));

        // 2.libra_device_subtype
        //DeviceSubtypeEntity deviceSubtypeEntity = deviceSubtypeEntityMapper
        //        .selectOne(new LambdaQueryWrapper<DeviceSubtypeEntity>()
        //                .eq(DeviceSubtypeEntity::getSubType, subtype));

        List<DeviceSubtypeEntity> deviceSubtypeEntities = deviceSubtypeEntityMapper
                .selectList(new LambdaQueryWrapper<DeviceSubtypeEntity>()
                        .eq(DeviceSubtypeEntity::getSubType, subtype));

        // 如果为空，直接异常返回
        if (ObjectUtils.isEmpty(libraDeviceSubtypeModel) || CollectionUtils.isEmpty(deviceSubtypeEntities)) {
            throw new ServiceException("invalid param", ErrorStatus.INVALID_PARAMETER);
        }

        // 3.libra_device_subtype_model_external
        DeviceSubtypeModelExternalEntity deviceSubtypeModelExternalEntity = deviceSubtypeModelExternalService
                .getOne(new LambdaQueryWrapper<DeviceSubtypeModelExternalEntity>()
                        .eq(DeviceSubtypeModelExternalEntity::getSubtype, subtype));
        // 4.libra_box_enterprise
        //BoxEnterpriseEntity boxEnterpriseEntity =
        //        boxEnterpriseEntityMapper.selectOne(new
        //                LambdaQueryWrapper<BoxEnterpriseEntity>()
        //                .eq(BoxEnterpriseEntity::getPackageName, deviceSubtypeEntity.getPackageName()));

        //deviceSubtypeEntities 取出packageName
        List<String> packageNames = deviceSubtypeEntities.stream()
                .map(DeviceSubtypeEntity::getPackageName)
                .collect(Collectors.toList());
        List<BoxEnterpriseEntity> boxEnterpriseEntities = boxEnterpriseEntityMapper
                .selectList(new LambdaQueryWrapper<BoxEnterpriseEntity>()
                        .in(BoxEnterpriseEntity::getPackageName, packageNames));

        // 5.libra_base_default_config
        List<NemoBaseDefaultConfig> nemoBaseDefaultConfigs = baseDefaultConfigMapper
                .selectList(new LambdaQueryWrapper<NemoBaseDefaultConfig>()
                        .eq(NemoBaseDefaultConfig::getConfigType, subtype));
        // 6.libra_common_group_config,根据subtype 查询出所有配置
        List<CommonGroupConfig> commonGroupConfigs = commonGroupConfigMapper
                .selectList(new LambdaQueryWrapper<CommonGroupConfig>().eq(CommonGroupConfig::getConfigType, subtype));
        // 7.libra_device_series_subtype
        DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = deviceSeriesSubtypeMapper
                .selectOne(new LambdaQueryWrapper<DeviceSeriesSubtypeEntity>()
                        .eq(DeviceSeriesSubtypeEntity::getSubtype, subtype));
        // 8.libra_device_series
        List<TempSeriesConfigBO> tempSeriesConfigs = new ArrayList<>();
        DeviceSeriesEntity deviceSeriesEntity = null;
        if (!ObjectUtils.isEmpty(deviceSeriesSubtypeEntity)) {
            deviceSeriesEntity = deviceSeriesMapper
                    .selectOne(new LambdaQueryWrapper<DeviceSeriesEntity>()
                            .eq(DeviceSeriesEntity::getId, deviceSeriesSubtypeEntity.getSeriesId()));
            // 9.1 libra_device_subtype_series_config 此处查询系列配置
            List<DeviceSubtypeSeriesConfigEntity> deviceSubtypeSeriesConfigSeries = deviceSubtypeSeriesConfigMapper
                    .selectList(new LambdaQueryWrapper<DeviceSubtypeSeriesConfigEntity>()
                            .eq(DeviceSubtypeSeriesConfigEntity::getSeriesId, deviceSeriesEntity.getId())
                            .eq(DeviceSubtypeSeriesConfigEntity::getType, "series"));
            // 9.1.1 通过seriesId 查询出config_code
            tempSeriesConfigs = deviceSubtypeSeriesConfigSeries.stream().map(config -> {
                DeviceSubtypeSeriesConfigDictionaryEntity deviceSubtypeSeriesConfigDictionaryEntity = deviceSubtypeSeriesConfigDictionaryMapper
                        .selectOne(new LambdaQueryWrapper<DeviceSubtypeSeriesConfigDictionaryEntity>()
                                .eq(DeviceSubtypeSeriesConfigDictionaryEntity::getId, config.getConfigId()));
                if (deviceSubtypeSeriesConfigDictionaryEntity != null) {
                    TempSeriesConfigBO tempSeriesConfigBO = new TempSeriesConfigBO();
                    tempSeriesConfigBO.setSubType(null);
                    tempSeriesConfigBO.setSeriesId(config.getSeriesId());

                    // 查询表获取 libra_device_subtype_series_config_dictionary
                    tempSeriesConfigBO.setConfigName(deviceSubtypeSeriesConfigDictionaryEntity.getConfigCode());
                    tempSeriesConfigBO.setPlatform(config.getPlatform());
                    tempSeriesConfigBO.setType(config.getType());
                    return tempSeriesConfigBO;
                }
                return null;

            }).collect(Collectors.toList());
            // 9.1.2 清理tempSeriesConfigs 中的null
            tempSeriesConfigs = tempSeriesConfigs.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }


        // 9.2 libra_device_subtype_series_config 此处查询subtype 配置
        List<DeviceSubtypeSeriesConfigEntity> deviceSubtypeSeriesConfigSubtype = deviceSubtypeSeriesConfigMapper
                .selectList(new LambdaQueryWrapper<DeviceSubtypeSeriesConfigEntity>()
                        .eq(DeviceSubtypeSeriesConfigEntity::getSubtype, subtype)
                        .eq(DeviceSubtypeSeriesConfigEntity::getType, "subtype"));
        /*
         * 根据sql 找出config_code
         * insert into ainemo.libra_device_subtype_series_config (sub_type, series_id,
         * config_id, platform, type)
         * select 8939, null, id, 'DEVICE', 'SUBTYPE'
         * from ainemo.libra_device_subtype_series_config_dictionary
         * where config_code = 'agcenabled';
         */
        // 9.2.1 通过subtype 查询出config_code
        List<TempSeriesConfigBO> tempSubtypeConfigs = deviceSubtypeSeriesConfigSubtype.stream().map(config -> {
            DeviceSubtypeSeriesConfigDictionaryEntity deviceSubtypeSeriesConfigDictionaryEntity = deviceSubtypeSeriesConfigDictionaryMapper
                    .selectOne(new LambdaQueryWrapper<DeviceSubtypeSeriesConfigDictionaryEntity>()
                            .eq(DeviceSubtypeSeriesConfigDictionaryEntity::getId, config.getConfigId()));
            if (deviceSubtypeSeriesConfigDictionaryEntity != null) {
                TempSeriesConfigBO tempSeriesConfigBO = new TempSeriesConfigBO();
                tempSeriesConfigBO.setSubType(subtype);
                tempSeriesConfigBO.setSeriesId(null);
                // 查询表获取 libra_device_subtype_series_config_dictionary
                tempSeriesConfigBO.setConfigName(deviceSubtypeSeriesConfigDictionaryEntity.getConfigCode());
                tempSeriesConfigBO.setPlatform(config.getPlatform());
                tempSeriesConfigBO.setType(config.getType());
                return tempSeriesConfigBO;
            }
            return null;

        }).collect(Collectors.toList());
        // 9.2.2 清理tempSubtypeConfigs 中的null
        tempSubtypeConfigs = tempSubtypeConfigs.stream().filter(Objects::nonNull).collect(Collectors.toList());

        SubtypeExportImportDto subtypeExportImportDto = new SubtypeExportImportDto();
        // 根据set方法将对象数据设置进去
        subtypeExportImportDto.setLibraDeviceSubtypeModel(libraDeviceSubtypeModel);
        subtypeExportImportDto.setDeviceSubtypeEntities(deviceSubtypeEntities);
        subtypeExportImportDto.setDeviceSubtypeModelExternalEntity(deviceSubtypeModelExternalEntity);
        subtypeExportImportDto.setBoxEnterpriseEntities(boxEnterpriseEntities);
        subtypeExportImportDto.setNemoBaseDefaultConfigs(nemoBaseDefaultConfigs);
        subtypeExportImportDto.setCommonGroupConfigs(commonGroupConfigs);
        subtypeExportImportDto.setDeviceSeriesSubtypeEntity(deviceSeriesSubtypeEntity);
        subtypeExportImportDto.setDeviceSeriesEntity(deviceSeriesEntity);
        subtypeExportImportDto.setTempSeriesConfigs(tempSeriesConfigs);
        subtypeExportImportDto.setTempSubtypeConfigs(tempSubtypeConfigs);
        // 返回对象
        return subtypeExportImportDto;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public R<String> subtypeBaseImport(SubtypeExportImportDto subtypeExportImportDto) {
        // 检查基础参数
        validateImportParams(subtypeExportImportDto);

        LibraDeviceSubtypeModel libraDeviceSubtypeModel = subtypeExportImportDto.getLibraDeviceSubtypeModel();

        Short subType = libraDeviceSubtypeModel.getSubType();
        // 1. 导入设备子类型模型
        importLibraDeviceSubtypeModel(subtypeExportImportDto.getLibraDeviceSubtypeModel());

        // 2. 导入设备子类型实体
        List<DeviceSubtypeEntity> deviceSubtypeEntity = importDeviceSubtypeEntity(subtypeExportImportDto.getDeviceSubtypeEntities());

        // 3. 导入设备子类型模型外部配置
        importDeviceSubtypeModelExternal(subtypeExportImportDto.getDeviceSubtypeModelExternalEntity());

        // 4. 导入企业盒子配置
        importBoxEnterprise(deviceSubtypeEntity, subtypeExportImportDto.getBoxEnterpriseEntities());

        // 5. 导入基础默认配置
        importBaseDefaultConfig(subtypeExportImportDto.getNemoBaseDefaultConfigs(), subType);

        // 6. 导入公共组配置
        importCommonGroupConfig(subtypeExportImportDto.getCommonGroupConfigs(), subType);

        // 7. 导入设备系列
        DeviceSeriesEntity deviceSeriesEntity = null;
        if (!ObjectUtils.isEmpty(subtypeExportImportDto.getDeviceSeriesEntity())) {
            deviceSeriesEntity = importDeviceSeries(subtypeExportImportDto.getDeviceSeriesEntity());
        }


        // 8. 导入设备系列子类型
        DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = null;
        if (!ObjectUtils.isEmpty(deviceSeriesEntity) && !ObjectUtils.isEmpty(subtypeExportImportDto.getDeviceSeriesSubtypeEntity())) {
            deviceSeriesSubtypeEntity = importDeviceSeriesSubtype(
                    subtypeExportImportDto.getDeviceSeriesSubtypeEntity(), deviceSeriesEntity.getId());
        }


        // 9. 导入设备子类型系列配置
        importDeviceSubtypeSeriesConfig(
                subtypeExportImportDto.getTempSeriesConfigs(),
                subtypeExportImportDto.getTempSubtypeConfigs(),
                deviceSeriesEntity,
                deviceSeriesSubtypeEntity, subType
        );

        return R.ok();
    }

    /**
     * 验证导入参数
     */
    private void validateImportParams(SubtypeExportImportDto subtypeExportImportDto) {
        if (ObjectUtils.isEmpty(subtypeExportImportDto.getLibraDeviceSubtypeModel())) {
            throw new ServiceException("设备子类型模型不能为空", ErrorStatus.INVALID_PARAMETER);
        }

        if (CollectionUtils.isEmpty(subtypeExportImportDto.getDeviceSubtypeEntities())) {
            throw new ServiceException("设备子类型实体不能为空", ErrorStatus.INVALID_PARAMETER);
        }
    }

    /**
     * 导入设备子类型模型
     */
    private void importLibraDeviceSubtypeModel(LibraDeviceSubtypeModel libraDeviceSubtypeModel) {
        // 重复可执行，删除数据库中数据
        libraDeviceSubtypeModelMapper.delete(new LambdaQueryWrapper<LibraDeviceSubtypeModel>()
                .eq(LibraDeviceSubtypeModel::getSubType, libraDeviceSubtypeModel.getSubType()));
        // 插入数据
        libraDeviceSubtypeModelMapper.insert(libraDeviceSubtypeModel);
    }

    /**
     * 导入设备子类型实体
     */
    private List<DeviceSubtypeEntity> importDeviceSubtypeEntity(List<DeviceSubtypeEntity> deviceSubtypeEntity) {
        for (DeviceSubtypeEntity subtypeEntity : deviceSubtypeEntity) {
            // 重复可执行，删除数据库中数据
            deviceSubtypeEntityMapper.delete(new LambdaQueryWrapper<DeviceSubtypeEntity>()
                    .eq(DeviceSubtypeEntity::getSubType, subtypeEntity.getSubType()).eq(DeviceSubtypeEntity::getPackageName, subtypeEntity.getPackageName()));
            // 插入数据
            deviceSubtypeEntityMapper.insert(subtypeEntity);
        }


        return deviceSubtypeEntity;
    }

    /**
     * 导入设备子类型模型外部配置
     */
    private void importDeviceSubtypeModelExternal(DeviceSubtypeModelExternalEntity deviceSubtypeModelExternalEntity) {
        if (!ObjectUtils.isEmpty(deviceSubtypeModelExternalEntity)) {
            // 重复可执行，删除数据库中数据
            deviceSubtypeModelExternalService.remove(new LambdaQueryWrapper<DeviceSubtypeModelExternalEntity>()
                    .eq(DeviceSubtypeModelExternalEntity::getSubtype, deviceSubtypeModelExternalEntity.getSubtype()));
            // 清除id，数据库自增
            deviceSubtypeModelExternalEntity.setId(null);
            // 插入数据
            deviceSubtypeModelExternalService.save(deviceSubtypeModelExternalEntity);
        }
    }

    /**
     * 导入企业盒子配置
     */
    private void importBoxEnterprise(List<DeviceSubtypeEntity> deviceSubtypeEntity, List<BoxEnterpriseEntity> newBoxEntity) {
        for (DeviceSubtypeEntity subtypeEntity : deviceSubtypeEntity) {
            String packageName = subtypeEntity.getPackageName();
            String model = subtypeEntity.getModel();
            if (StringUtils.isEmpty(packageName) || StringUtils.isEmpty(model)) {
                continue;
            }
            // 查询libra_box_enterprise中packageName为packageName的数据
            BoxEnterpriseEntity boxEnterpriseEntity = boxEnterpriseEntityMapper.selectOne(
                    new LambdaQueryWrapper<BoxEnterpriseEntity>()
                            .eq(BoxEnterpriseEntity::getPackageName, packageName));
            if (boxEnterpriseEntity != null) {
                // 更新已存在的盒子配置
                updateExistingBoxEnterprise(boxEnterpriseEntity, model);
            } else {
                //newBoxEntity  中取出packagename一致的
                List<BoxEnterpriseEntity> boxEnterpriseEntities = newBoxEntity.stream()
                        .filter(entity -> entity.getPackageName().equals(packageName))
                        .collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(boxEnterpriseEntities)) {
                    // 插入新的盒子配置
                    boxEnterpriseEntityMapper.insert(boxEnterpriseEntities.get(0));
                }

            }
        }


    }

    /**
     * 更新已存在的企业盒子配置
     */
    private void updateExistingBoxEnterprise(BoxEnterpriseEntity boxEnterpriseEntity, String model) {
        String modelList = boxEnterpriseEntity.getModelList();
        // 避免重复添加相同型号
        if (!modelListContains(modelList, model)) {
            String newModelList = appendModelToList(modelList, model);
            boxEnterpriseEntity.setModelList(newModelList);
            boxEnterpriseEntityMapper.updateById(boxEnterpriseEntity);
        }
    }

    /**
     * 检查模型列表中是否已包含指定模型
     */
    private boolean modelListContains(String modelList, String model) {
        if (StringUtils.isEmpty(modelList)) {
            return false;
        }
        String[] models = modelList.split(",");
        for (String m : models) {
            if (m.trim().equals(model.trim())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 向模型列表中添加新模型
     */
    private String appendModelToList(String modelList, String model) {
        if (StringUtils.isEmpty(modelList)) {
            return model;
        }
        return modelList + "," + model;
    }

    /**
     * 导入基础默认配置
     */
    private void importBaseDefaultConfig(List<NemoBaseDefaultConfig> nemoBaseDefaultConfigs, Short subType) {
        if (nemoBaseDefaultConfigs != null && !nemoBaseDefaultConfigs.isEmpty()) {
            // 删除数据
            baseDefaultConfigMapper.delete(new LambdaQueryWrapper<NemoBaseDefaultConfig>()
                    .eq(NemoBaseDefaultConfig::getConfigType, subType));
            // 插入数据
            baseDefaultConfigMapper.insertList(nemoBaseDefaultConfigs);
        }
    }

    /**
     * 导入公共组配置
     */
    private void importCommonGroupConfig(List<CommonGroupConfig> commonGroupConfigs, Short subType) {
        if (commonGroupConfigs != null && !commonGroupConfigs.isEmpty()) {
            // 删除数据
            commonGroupConfigMapper.delete(new LambdaQueryWrapper<CommonGroupConfig>()
                    .eq(CommonGroupConfig::getConfigType, subType));
            // 插入数据
            commonGroupConfigMapper.insertList(commonGroupConfigs);
        }
    }

    /**
     * 导入设备系列
     */
    private DeviceSeriesEntity importDeviceSeries(DeviceSeriesEntity deviceSeriesEntity) {
        DeviceSeriesEntity deviceSeriesEntityDb = deviceSeriesMapper
                .selectOne(new LambdaQueryWrapper<DeviceSeriesEntity>()
                        .eq(DeviceSeriesEntity::getSeriesName, deviceSeriesEntity.getSeriesName()));

        if (ObjectUtils.isEmpty(deviceSeriesEntityDb)) {
            // 插入数据前清除ID，使用数据库自增
            deviceSeriesEntity.setId(null);
            deviceSeriesMapper.insert(deviceSeriesEntity);
            return deviceSeriesEntity;
        }

        // 返回数据库中已存在的系列实体
        return deviceSeriesEntityDb;
    }

    /**
     * 导入设备系列子类型
     */
    private DeviceSeriesSubtypeEntity importDeviceSeriesSubtype(DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity, Long seriesId) {
        //删除数据库中的数据
        deviceSeriesSubtypeMapper.delete(new LambdaQueryWrapper<DeviceSeriesSubtypeEntity>()
                .eq(DeviceSeriesSubtypeEntity::getSubtype, deviceSeriesSubtypeEntity.getSubtype()));

        // 更新seriesId
        deviceSeriesSubtypeEntity.setSeriesId(seriesId);
        //dm数据库自增报错修复
        deviceSeriesSubtypeEntity.setId(null);
        // 插入数据
        deviceSeriesSubtypeMapper.insert(deviceSeriesSubtypeEntity);
        return deviceSeriesSubtypeEntity;
    }

    /**
     * 导入设备子类型系列配置
     */
    private void importDeviceSubtypeSeriesConfig(
            List<TempSeriesConfigBO> tempSeriesConfigs,
            List<TempSeriesConfigBO> tempSubtypeConfigs,
            DeviceSeriesEntity deviceSeriesEntity,
            DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity,
            Short subType) {
        // 导入系列配置
        if (!ObjectUtils.isEmpty(deviceSeriesEntity) && !CollectionUtils.isEmpty(tempSeriesConfigs)) {
            importSeriesConfig(tempSeriesConfigs, deviceSeriesEntity.getId(), deviceSeriesSubtypeEntity);
        }

        // 导入子类型配置
        if (!CollectionUtils.isEmpty(tempSubtypeConfigs)) {
            importSubtypeConfig(tempSubtypeConfigs, subType);
        }
    }

    /**
     * 导入系列配置
     */
    private void importSeriesConfig(List<TempSeriesConfigBO> tempSeriesConfigs, Long seriesId, DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity) {
        // 删除系列配置
        deviceSubtypeSeriesConfigMapper.delete(new LambdaQueryWrapper<DeviceSubtypeSeriesConfigEntity>()
                .eq(DeviceSubtypeSeriesConfigEntity::getType, "series")
                .in(DeviceSubtypeSeriesConfigEntity::getSeriesId, deviceSeriesSubtypeEntity.getSeriesId()));

        // 插入系列配置
        if (tempSeriesConfigs != null && !tempSeriesConfigs.isEmpty()) {
            for (TempSeriesConfigBO tempSeriesConfigBO : tempSeriesConfigs) {
                DeviceSubtypeSeriesConfigDictionaryEntity dictionary = findSeriesConfigDictionary(tempSeriesConfigBO.getConfigName(), "SERIES");
                if (dictionary != null) {
                    insertSeriesConfig(null, seriesId, dictionary.getId(), tempSeriesConfigBO.getPlatform(), tempSeriesConfigBO.getType());
                }
            }
        }
    }

    /**
     * 导入子类型配置
     */
    private void importSubtypeConfig(List<TempSeriesConfigBO> tempSubtypeConfigs, Short subType) {
        // 删除子类型配置
        deviceSubtypeSeriesConfigMapper.delete(new LambdaQueryWrapper<DeviceSubtypeSeriesConfigEntity>()
                .eq(DeviceSubtypeSeriesConfigEntity::getType, "subtype")
                .eq(DeviceSubtypeSeriesConfigEntity::getSubtype, subType));

        // 插入子类型配置
        if (tempSubtypeConfigs != null && !tempSubtypeConfigs.isEmpty()) {
            for (TempSeriesConfigBO tempSeriesConfigBO : tempSubtypeConfigs) {
                DeviceSubtypeSeriesConfigDictionaryEntity dictionary = findSeriesConfigDictionary(tempSeriesConfigBO.getConfigName(), "SUBTYPE");
                if (dictionary != null) {
                    insertSeriesConfig(
                            tempSeriesConfigBO.getSubType(),
                            tempSeriesConfigBO.getSeriesId(),
                            dictionary.getId(),
                            tempSeriesConfigBO.getPlatform(),
                            tempSeriesConfigBO.getType()
                    );
                }
            }
        }
    }

    /**
     * 查找系列配置字典
     */
    private DeviceSubtypeSeriesConfigDictionaryEntity findSeriesConfigDictionary(String configName, String dictionaryType) {
        return deviceSubtypeSeriesConfigDictionaryMapper.selectOne(
                new LambdaQueryWrapper<DeviceSubtypeSeriesConfigDictionaryEntity>()
                        .eq(DeviceSubtypeSeriesConfigDictionaryEntity::getConfigCode, configName)
                        .eq(DeviceSubtypeSeriesConfigDictionaryEntity::getDictionaryType, dictionaryType)
        );
    }

    /**
     * 插入系列配置
     */
    private void insertSeriesConfig(Integer subType, Long seriesId, Long configId, String platform, String type) {
        DeviceSubtypeSeriesConfigEntity entity = new DeviceSubtypeSeriesConfigEntity();
        entity.setSubtype(subType);
        entity.setSeriesId(seriesId);
        entity.setConfigId(configId);
        entity.setPlatform(platform);
        entity.setType(type);
        deviceSubtypeSeriesConfigMapper.insert(entity);
    }
}
