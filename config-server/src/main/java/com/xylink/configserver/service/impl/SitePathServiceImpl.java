package com.xylink.configserver.service.impl;

import com.xylink.configserver.data.model.EnterpriseSitePath;
import com.xylink.configserver.data.model.EnterpriseSitePathInfoOld;
import com.xylink.configserver.mapper.SitePathMapper;
import com.xylink.configserver.service.SitePathService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.xylink.configserver.util.Constants.PUBLIC_PATH_KEY;

@Slf4j
@Service
public class SitePathServiceImpl implements SitePathService {

    @Autowired
    SitePathMapper sitePathMapper;

    @Override
    public List<EnterpriseSitePathInfoOld> getOldEnterpriseSitePathByEnterpriseId(String enterpriseId) {
        List<EnterpriseSitePath> pos = getPublicAndPrivatePaths(enterpriseId);
        if (null == pos || pos.size() <= 0) {
            log.error("getEnterpriseSitePathByEnterpriseId site path not exist, enterpriseId:" + enterpriseId);
            return Collections.emptyList();
        }

        List<EnterpriseSitePath> pathPOs = new ArrayList<>();
        //过滤掉同一私有线路有内外网时候，只给用户内网：此功能只是为了兼容已经发版的pc 和 mac
        Map<Pair<String, String>, EnterpriseSitePath> map = new HashMap<>();
        for (EnterpriseSitePath po : pos) {
            Pair<String, String> key = Pair.of(po.getLocationOfSitecode(), po.getSublocationOfSitecode());
            if (map.containsKey(key) && po.getNetworkType() == 1) {
                continue;
            }
            map.put(key, po);
        }
        pathPOs.addAll(map.values());

        List<EnterpriseSitePathInfoOld> resultList = new ArrayList<>(pathPOs.size());

        for (EnterpriseSitePath po: pathPOs) {
            EnterpriseSitePathInfoOld info = new EnterpriseSitePathInfoOld(po.getId(), po.getLocationOfSitecode(),
                    po.getEnterpriseOfSitecode(), po.getNetToolServer() , po.getDisalayName(), po.getEnterpriseId(), po.getDetail());
            resultList.add(info);
        }
        return resultList;
    }

    private List<EnterpriseSitePath> getPublicAndPrivatePaths(String enterpriseId) {
        List<EnterpriseSitePath> pathPOs = new ArrayList<>();

        List<EnterpriseSitePath> publicPathPOs = sitePathMapper.getEnterpriseSitePaths(PUBLIC_PATH_KEY);
        if (null == publicPathPOs || publicPathPOs.size() <= 0) {
            log.error("getEnterpriseSitePathByEnterpriseId public site path not exist, enterpriseId:" + enterpriseId);
        } else {
            pathPOs.addAll(publicPathPOs);
        }


        List<EnterpriseSitePath> privatePathPOs = sitePathMapper.getEnterpriseSitePaths(enterpriseId);
        if (null == privatePathPOs || privatePathPOs.size() <= 0) {
            log.info("getEnterpriseSitePathByEnterpriseId private site path not exist, enterpriseId:" + enterpriseId);
        } else {
            pathPOs.addAll(privatePathPOs);
        }

        if (null == pathPOs || pathPOs.size() <= 0) {
            log.error("getEnterpriseSitePathByEnterpriseId site path not exist, enterpriseId:" + enterpriseId);
            return Collections.emptyList();
        }

        return pathPOs;
    }
}
