package com.xylink.configserver.service.deviceseries.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xylink.configserver.data.model.deviceseries.DeviceGroupEnum;
import com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigEntity;
import com.xylink.configserver.mapper.deviceseries.DeviceSubtypeSeriesConfigMapper;
import com.xylink.configserver.service.deviceseries.DeviceSubtypeSeriesConfigService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-18 21:48
 */
@Service
public class DeviceSubtypeSeriesConfigServiceImpl extends ServiceImpl<DeviceSubtypeSeriesConfigMapper, DeviceSubtypeSeriesConfigEntity> implements DeviceSubtypeSeriesConfigService {
    @Override
    public List<DeviceSubtypeSeriesConfigEntity> getSeriesConfig(long seriesId) {
        return this.baseMapper.getConfigByIdAndType(seriesId, DeviceGroupEnum.SERIES.name());
    }

    @Override
    public List<DeviceSubtypeSeriesConfigEntity> getSubtypeConfig(int subtype) {
        return this.baseMapper.getConfigByIdAndType(subtype, DeviceGroupEnum.SUBTYPE.name());
    }

    @Override
    public void deleteSeriesConfigBySeriesId(long seriesId) {
        this.baseMapper.deleteSeriesConfigBySeriesId(seriesId);
    }

    @Override
    public void deleteSubtypeConfigBySubtype(int subtype) {
        this.baseMapper.deleteSubtypeConfigBySubtype(subtype);
    }
}
