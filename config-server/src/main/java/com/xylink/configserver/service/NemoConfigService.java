package com.xylink.configserver.service;

import com.xylink.configserver.data.model.DeviceInfo;
import com.xylink.configserver.data.model.RestNemoConfig;
import com.xylink.configserver.exception.ServiceException;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

public interface NemoConfigService {

    List<RestNemoConfig> getNemoConfigs(DeviceInfo device, long nemoId);

    String getNemoConfigByName(long nemoId, String configName);

    void updateNemoConfig(RestNemoConfig[] configs,long nemoId) throws ServiceException;

    void updateInternalNemoConfig(RestNemoConfig[] configs, long nemoId) throws ServiceException;

    void applyJsonValueConfig(String value, long nemoId, String clientConfigName);

    List<RestNemoConfig> getExternalNemoConfigs(long nemoId,String hardVersion, String softVersion, String os, String model, HttpServletRequest request);
    List<RestNemoConfig>getInternalNemoConfigs(long nemoId);

    List<RestNemoConfig> getInternalNemoCallConfigs(long nemoId);
}
