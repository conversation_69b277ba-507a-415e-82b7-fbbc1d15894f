package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListenableFutureTask;
import com.xylink.configserver.data.model.NemoDefaultBrandConfig;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.mapper.NemoDefaultBrandConfigMapper;
import com.xylink.configserver.service.BrandConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class BrandConfigServiceImpl implements BrandConfigService {

    @Autowired
    NemoDefaultBrandConfigMapper nemoDefaultBrandConfigMapper;

    private ExecutorService executorService = Executors.newFixedThreadPool(5);

    private LoadingCache<String, List<NemoDefaultBrandConfig>> nemoDefaultBrandConfigCache = CacheBuilder.newBuilder()
            .maximumSize(100)
            .refreshAfterWrite(10, TimeUnit.MINUTES)
            .build(createCacheLoader());

    @Override
    public List<NemoDefaultBrandConfig> getDefaultBrandConfigBytypeAndBrand(String brand, int deviceTypeOrSubtype,String brandModel) {
        if (StringUtils.isBlank(brand) || deviceTypeOrSubtype<=0){
            return null;
        }
        try {
            log.info("get default brand config By brand :" + brand + " and type is :" +deviceTypeOrSubtype +"and brand model is "+ brandModel);
            String key = getDefaultBrandCacheKey(brand,deviceTypeOrSubtype,brandModel);
            return nemoDefaultBrandConfigCache.get(key);
        }catch ( ExecutionException e){
            log.error(" get nemo defualt brand config error " + e);
            return null;
        }

    }

    @Override
    public void clearBrandConfigCache(String brand, int configTypeOrSubype, String brandModel) {
        if (StringUtils.isBlank(brand) || configTypeOrSubype<=0){
            nemoDefaultBrandConfigCache.invalidateAll();
        }else{
            String key = getDefaultBrandCacheKey(brand,configTypeOrSubype,brandModel);
            nemoDefaultBrandConfigCache.invalidate(key);
        }
    }

    @Override
    public Map<String, String> getDefaultBrandConfigAll() {
        return nemoDefaultBrandConfigMapper.getDefaultBrandConfigAll();
    }


    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void saveOrUpdateDefaultBrandConfig(NemoDefaultBrandConfig defaultBrandConfig) {
        if (defaultBrandConfig == null || StringUtils.isBlank(defaultBrandConfig.getConfigValue())){
            return;
        }
        if (StringUtils.isBlank(defaultBrandConfig.getClientConfigName())){
            defaultBrandConfig.setClientConfigName(NemoDefaultBrandConfig.COMMON_CONFIG_KEY);
        }
        if (StringUtils.isBlank(defaultBrandConfig.getBrandModel())){
            defaultBrandConfig.setClientConfigName(NemoDefaultBrandConfig.DEFAULT_BRAND_MODEL_KEY);
        }
        NemoDefaultBrandConfig existDefaultBrandConfig = nemoDefaultBrandConfigMapper.getDefaultBrandConfigUnique(defaultBrandConfig);
        try {
            if (existDefaultBrandConfig != null){
                log.info(" default brand config exists" + existDefaultBrandConfig +" and change vaule to :" + defaultBrandConfig.getConfigValue());
                existDefaultBrandConfig.setConfigValue(defaultBrandConfig.getConfigValue());
                nemoDefaultBrandConfigMapper.updateBrandConfig(existDefaultBrandConfig);
            }else {
                log.info("create new default brand config " + defaultBrandConfig.toString());
                existDefaultBrandConfig = new NemoDefaultBrandConfig(defaultBrandConfig.getConfigName(),defaultBrandConfig.getConfigValue(),defaultBrandConfig.getClientConfigName(),defaultBrandConfig.getConfigBrand(),defaultBrandConfig.getConfigType(),defaultBrandConfig.getBaseConfigType(),defaultBrandConfig.getBrandModel());
                nemoDefaultBrandConfigMapper.saveBrandConfig(existDefaultBrandConfig);
            }
        }catch (DataAccessException e){
            log.error("Failed to update default brand config ", e);
            throw new ServiceException("DBError", ErrorStatus.INTERNAL_DATABASE_ERROR);
        }
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void removeDefaultBrandConfig(String configBrand, String brandModel, int deviceTypeOrSubtype,String configName,String clientConfigName){
        log.info("delete brand model config configBrand:{}  ,brandModel:{} ,deviceTypeOrSubtype:{} ,configName{}, clientConfigName:{}",
                configBrand,brandModel,deviceTypeOrSubtype,configName,clientConfigName);
        if(StringUtils.isBlank(configBrand) || deviceTypeOrSubtype<=0){
            log.error("removeDefaultBrandConfig error,configBrand:{},deviceTypeOrSubtype:{}",configBrand,deviceTypeOrSubtype);
            return;
        }
        NemoDefaultBrandConfig existDefaultBrandConfig = nemoDefaultBrandConfigMapper.getDefaultBrandConfigUnique(new NemoDefaultBrandConfig(configName,clientConfigName,configBrand,brandModel,deviceTypeOrSubtype));

        try {
            if (existDefaultBrandConfig!=null){
                nemoDefaultBrandConfigMapper.deleteBrandConfig(existDefaultBrandConfig.getId());
            }
        }catch (Exception e){
            log.error("delete brand config error" +e);
        }
    }

    private CacheLoader<String, List<NemoDefaultBrandConfig>> createCacheLoader() {
        return new CacheLoader<String, List<NemoDefaultBrandConfig>>() {

            @Override
            public List<NemoDefaultBrandConfig> load(String key) throws Exception {
                try {
                    if (StringUtils.isNotEmpty(key)) {
                        log.info(" load nemo default brand config session cache:" + key);
                        String[] keys = key.split(":");
                        String brand = keys[0];
                        int type = Integer.parseInt(keys[1]);
                        String brandModel = keys[2];
                        return nemoDefaultBrandConfigMapper.getDefaultBrandConfigByBrandAndType(brand,type,brandModel);
                    }
                    return null;
                }catch (Exception e){
                    log.error(" load nemo defualt brand config error ",e);
                }
                return Collections.EMPTY_LIST;
            }

            @Override
            public ListenableFuture<List<NemoDefaultBrandConfig>> reload(String key, List<NemoDefaultBrandConfig> defaultBrandConfigPOS){
                try {
                    if (StringUtils.isNotEmpty(key)) {
                        ListenableFutureTask<List<NemoDefaultBrandConfig>> futureTask = ListenableFutureTask.create(() -> {
                            log.info("async reload nemo default brand config "+ key);
                            String[] keys = key.split(":");
                            String brand = keys[0];
                            int type = Integer.parseInt(keys[1]);
                            String brandModel = keys[2];
                            return nemoDefaultBrandConfigMapper.getDefaultBrandConfigByBrandAndType(brand,type,brandModel);
                        });
                        executorService.execute(futureTask);
                        return futureTask;
                    }
                }catch (Exception e){
                    log.error(" reload nemo defualt brand config error ",e);
                }
                return null;
            }
        };
    }

    private String getDefaultBrandCacheKey(String brand,int deviceTypeOrSubype,String brandModel){
        if (StringUtils.isEmpty(brand) ||deviceTypeOrSubype<=0 ){
            log.info("request get default brand nemo config error and brand id is null or request type is not exist");
            return "";
        }
        String key = brand+":"+deviceTypeOrSubype+":"+brandModel;
        log.info("get current nemo default brand config  key:" + key);
        return key;

    }
}
