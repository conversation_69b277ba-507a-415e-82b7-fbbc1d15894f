package com.xylink.configserver.service;

import com.xylink.configserver.data.dto.DeptChangeDTO;
import com.xylink.configserver.data.model.DeptConfigPO;
import com.xylink.configserver.data.model.EnterpriseNemoProfileUpdateReq;

import java.util.List;

public interface DeptConfigService {

    void changeConfig(DeptChangeDTO changeDTO);

    /** 查询覆盖后的部门维度配置 */
    EnterpriseNemoProfileUpdateReq getOverrideDeptConfig(String enterpriseId, String deptId, int type);

    /** 查询覆盖后的部门维度配置, 并转成List<><DeptConfigPO>返回 */
    List<DeptConfigPO> getOverrideMergeDeptConfigList(String enterpriseId, String deptId);

    /** 查询原始部门维度配置 */
    List<DeptConfigPO> getSourceDeptConfig(String enterpriseId, String deptId);

    /** 删除部门下的配置 */
    void deleteByDeptId(String enterpriseId, String deptId, String configName);

    /** 查询部门配置 */
    List<DeptConfigPO> findConfigListByDeptIds(String enterpriseId, List<String> deptIdList);

}
