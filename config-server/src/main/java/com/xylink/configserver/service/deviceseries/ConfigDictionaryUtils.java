package com.xylink.configserver.service.deviceseries;

import org.apache.commons.lang3.StringUtils;
import com.xylink.configserver.data.model.DeviceConfigUpdate;
import com.xylink.configserver.data.model.deviceseries.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021-01-18 22:21
 */
public final class ConfigDictionaryUtils {

    private ConfigDictionaryUtils() {
    }

    public static String typeName(int type) {
        String typeName;
        switch (type) {
            case 2:
                typeName = "小鱼";
                break;
            case 7:
                typeName = "大鱼";
                break;
            case 8:
                typeName = "中鱼";
                break;
            default:
                typeName = "unknown";
        }
        return typeName;
    }

    public static List<DeviceSeriesSubtypeConfigsDto> parseTree(List<DeviceSubtypeSeriesConfigDictionaryEntity> data, List<DeviceSubtypeSeriesConfigEntity> selectedConfig) {
        List<DeviceSeriesSubtypeConfigsDto> configs = of(data, selectedConfig);
        return configs.stream()
                .filter(item -> item.getPId() == 0)
                .peek(item -> item.setChildren(children(item, configs)))
                .sorted(Comparator.comparingInt(DeviceSeriesSubtypeConfigsDto::getConfigOrder))
                .collect(Collectors.toList());
    }

    public static boolean equalConfigs(DeviceConfigUpdate object1, DeviceSubtypeSeriesConfigDictionaryDataEntity object2) {
        return object1.getConfigName().equalsIgnoreCase(object2.getConfigName()) && equalClientConfigName(object1.getClientConfigName(), object2.getClientConfigName());
    }

    public static List<DeviceSeriesSubtypeConfigsDto> excludeNotSelectedTree(List<DeviceSeriesSubtypeConfigsDto> tree) {
        if (CollectionUtils.isEmpty(tree)) {
            return Collections.emptyList();
        }
        return tree.stream().peek(item -> item.setChildren(excludeNotSelectedChildren(item)))
                .filter(item -> !CollectionUtils.isEmpty(item.getChildren()))
                .filter(item -> !CollectionUtils.isEmpty(item.getChildren()))
                .sorted(Comparator.comparingInt(DeviceSeriesSubtypeConfigsDto::getConfigOrder))
                .collect(Collectors.toList());
    }

    private static List<DeviceSeriesSubtypeConfigsDto> of(List<DeviceSubtypeSeriesConfigDictionaryEntity> configDictionary, List<DeviceSubtypeSeriesConfigEntity> selectedConfig) {
        if (CollectionUtils.isEmpty(configDictionary)) {
            return Collections.emptyList();
        }
        boolean setSelected = !CollectionUtils.isEmpty(selectedConfig);
        List<DeviceSeriesSubtypeConfigsDto> configs = new ArrayList<>();
        for (DeviceSubtypeSeriesConfigDictionaryEntity item : configDictionary) {
            DeviceSeriesSubtypeConfigsDto deviceSeriesSubtypeConfigsDto = of(item);
            configs.add(deviceSeriesSubtypeConfigsDto);
            if (setSelected) {
                for (DeviceSubtypeSeriesConfigEntity selected : selectedConfig) {
                    setSelected(deviceSeriesSubtypeConfigsDto, selected);
                }
            }
        }
        return configs;
    }

    private static List<DeviceSeriesSubtypeConfigsDto> excludeNotSelectedChildren(DeviceSeriesSubtypeConfigsDto parent) {
        return parent.getChildren().stream().peek(item -> item.setChildren(excludeNotSelectedChildren(item)))
                .filter(item -> (ConfigTypeEnum.ITEM.name().equalsIgnoreCase(item.getConfigType()) && item.isSelected()) || (!CollectionUtils.isEmpty(item.getChildren())))
                .sorted(Comparator.comparingInt(DeviceSeriesSubtypeConfigsDto::getConfigOrder))
                .collect(Collectors.toList());
    }

    private static boolean equalClientConfigName(String clientConfigName1, String clientConfigName2) {
        if (StringUtils.isEmpty(clientConfigName1)) {
            clientConfigName1 = "common";
        }
        if (StringUtils.isEmpty(clientConfigName2)) {
            clientConfigName2 = "common";
        }
        return clientConfigName1.equalsIgnoreCase(clientConfigName2);
    }

    private static List<DeviceSeriesSubtypeConfigsDto> children(DeviceSeriesSubtypeConfigsDto parent, List<DeviceSeriesSubtypeConfigsDto> data) {
        return data.stream().filter(item -> Objects.equals(parent.getConfigId(), item.getPId()))
                .peek(item -> item.setChildren(children(item, data)))
                .sorted(Comparator.comparingInt(DeviceSeriesSubtypeConfigsDto::getConfigOrder))
                .collect(Collectors.toList());
    }

    private static DeviceSeriesSubtypeConfigsDto of(DeviceSubtypeSeriesConfigDictionaryEntity entity) {
        DeviceSeriesSubtypeConfigsDto config = new DeviceSeriesSubtypeConfigsDto();
        config.setConfigId(entity.getId());
        config.setPId(entity.getPId());
        config.setConfigCode(entity.getConfigCode());
        config.setConfigName(entity.getConfigName());
        config.setConfigComment(entity.getConfigComment());
        config.setConfigOrder(entity.getConfigOrder());
        config.setDictionaryType(entity.getDictionaryType());
        config.setConfigType(entity.getConfigType());
        config.setSelected(false);
        return config;
    }

    private static void setSelected(DeviceSeriesSubtypeConfigsDto target1, DeviceSubtypeSeriesConfigEntity target2) {
        if (Objects.equals(target1.getConfigId(), target2.getConfigId())) {
            target1.setSelected(true);
        }
        if (Objects.equals(target1.getPId(), target2.getConfigId()) && DeviceGroupEnum.SUBTYPE.name().equalsIgnoreCase(target1.getDictionaryType()) && target1.getConfigId() == -1 && target1.getConfigCode().equalsIgnoreCase(target2.getPlatform())) {
            target1.setSelected(true);
        }
    }
}
