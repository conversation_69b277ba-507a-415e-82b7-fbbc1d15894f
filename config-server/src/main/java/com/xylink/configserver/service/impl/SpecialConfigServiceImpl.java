package com.xylink.configserver.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xylink.configserver.data.model.SpecialConfig;
import com.xylink.configserver.mapper.SpecialConfigMapper;
import com.xylink.configserver.service.SpecialConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.xylink.configserver.util.Constants.EN_NEMOS_WITHOUT_ADMIN;

@Slf4j
@Service
public class SpecialConfigServiceImpl extends ServiceImpl<SpecialConfigMapper, SpecialConfig> implements SpecialConfigService {

    @Autowired
    SpecialConfigMapper specialConfigMapper;

    @Override
    public List<SpecialConfig> getSpecialConfig(String deviceSN) {
        return specialConfigMapper.getSpecialConfig(deviceSN);
    }

    @Override
    public List<SpecialConfig> getSpecialConfigByUserId(long userProfileId) {
        return specialConfigMapper.getSpecialConfigByUserId(userProfileId);
    }

    @Override
    public List<SpecialConfig> getSpecialClientConfig(String deviceSN, String clientConfigName) {
        return specialConfigMapper.getSpecialClientConfig(deviceSN,clientConfigName);
    }

    @Override
    public SpecialConfig getDeviceSpecialConfig(String nemoSN, String configName) {
        return specialConfigMapper.getDeviceSpecialConfig(nemoSN,configName);
    }

    @Override
    public CharSequence getSpecialConfigByNameAndClient(String deviceSN, String configName, String clientConfigName) {
        List<SpecialConfig> specialConfigs = specialConfigMapper.getSpecialConfigByConfigNameAndClientName(deviceSN,configName,clientConfigName,EN_NEMOS_WITHOUT_ADMIN);
        return specialConfigs != null && specialConfigs.size() >0 ? specialConfigs.get(0).getConfigValue() : null;
    }

    @Override
    public List<SpecialConfig> getSpecialConfigsByFeatureById(String featureId) {
        return specialConfigMapper.getSpecialConfigsByFeatureById(featureId);
    }
}
