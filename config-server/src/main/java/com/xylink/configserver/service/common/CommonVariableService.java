package com.xylink.configserver.service.common;

import com.xylink.configserver.proxy.InternalApiProxy;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Slf4j
@Data
@Component
public class CommonVariableService {

    /** 多企业开关 */
    @Value("${common.multiEnterprise:false}")
    private boolean multiEnterprise;

    @Autowired
    InternalApiProxy internalApiProxy;

    /** 开启部门维度的配置, 默认不开启 */
    /**
     * 交付
     * */
    //@Value("${common.enableDeptConfig:false}")
    //private boolean enableDeptConfig;

    private volatile boolean enableDeptConfig;
    private volatile long lastUpdateTime;
    private final Object lock = new Object();
    private static final long CACHE_TTL = 60_000;


    @PostConstruct
    public void print(){
        log.info("==========>[Running]==========>[Variable]:{}", this);
    }


    public boolean isEnableDeptConfig() {
        if (needRefresh()) {
            synchronized (lock) {
                if (needRefresh()) {
                    refreshConfig();
                }
            }
        }
        return enableDeptConfig;
    }

    private boolean needRefresh() {
        return System.currentTimeMillis() - lastUpdateTime > CACHE_TTL;
    }

    private void refreshConfig() {
        try {
            this.enableDeptConfig = internalApiProxy.fetchDeptSwitch();
            this.lastUpdateTime = System.currentTimeMillis();
        } catch (Exception e) {
            log.error("远程配置获取失败，使用缓存值", e);
        }
    }
}
