package com.xylink.configserver.service.deviceseries.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xylink.configserver.data.model.deviceseries.DeviceSeriesSubtypeEntity;
import com.xylink.configserver.mapper.deviceseries.DeviceSeriesSubtypeMapper;
import com.xylink.configserver.service.deviceseries.DeviceSeriesSubTypeService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-18 22:54
 */
@Service
public class DeviceSeriesSubTypeServiceImpl extends ServiceImpl<DeviceSeriesSubtypeMapper, DeviceSeriesSubtypeEntity> implements DeviceSeriesSubTypeService {
    @Override
    public List<DeviceSeriesSubtypeEntity> getBySeriesId(long seriesId) {
        return this.baseMapper.getBySeriesId(seriesId);
    }

    @Override
    public DeviceSeriesSubtypeEntity getSeries(int subtype) {
        List<DeviceSeriesSubtypeEntity> series = this.baseMapper.getSeries(subtype);
        if (!CollectionUtils.isEmpty(series)) {
            return series.get(0);
        }
        return null;
    }

    @Override
    public void saveOrUpdateSeriesSubtypeMapping(long seriesId, int subtype) {
        this.baseMapper.saveOrUpdateSeriesSubtypeMapping(seriesId, subtype);
    }

    @Override
    public void removeBySubtype(int subtype) {
        this.baseMapper.deleteBySubtype(subtype);
    }
}
