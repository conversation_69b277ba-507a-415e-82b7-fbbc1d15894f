package com.xylink.configserver.service.deviceseries.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xylink.configserver.data.model.deviceseries.ConfigDataValuesDto;
import com.xylink.configserver.data.model.deviceseries.DeviceConfigDictDataEntity;
import com.xylink.configserver.mapper.deviceseries.DeviceConfigDictDataMapper;
import com.xylink.configserver.service.deviceseries.DeviceConfigDictDataService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021-02-18 15:28
 */
@Service
public class DeviceConfigDictDataServiceImpl extends ServiceImpl<DeviceConfigDictDataMapper, DeviceConfigDictDataEntity> implements DeviceConfigDictDataService {

    @Override
    public List<DeviceConfigDictDataEntity> getByDictType(String dictType) {
        return this.baseMapper.getByDictType(dictType);
    }

    @Override
    public List<ConfigDataValuesDto> getDtoByDictType(String dictType) {
        List<DeviceConfigDictDataEntity> data = this.getByDictType(dictType);
        if (!CollectionUtils.isEmpty(data)) {
            return data.stream().map(this::convert).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private ConfigDataValuesDto convert(DeviceConfigDictDataEntity deviceConfigDictDataEntity) {
        ConfigDataValuesDto configDataValuesDto = new ConfigDataValuesDto();
        configDataValuesDto.setKey(deviceConfigDictDataEntity.getDictCode());
        configDataValuesDto.setValue(deviceConfigDictDataEntity.getDictValue());
        return configDataValuesDto;
    }
}
