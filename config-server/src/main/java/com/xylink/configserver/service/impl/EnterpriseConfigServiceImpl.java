package com.xylink.configserver.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xylink.configserver.data.dto.BaseConfigDto;
import com.xylink.configserver.data.enums.ResultCodeEnum;
import com.xylink.configserver.data.exception.RestException;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.mapper.DeviceConfigMapper;
import com.xylink.configserver.mapper.UserDeviceMapper;
import com.xylink.configserver.mapper.UserProfileMapper;
import com.xylink.configserver.service.*;
import com.xylink.configserver.util.ConfigUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-07-18 18:16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EnterpriseConfigServiceImpl implements EnterpriseConfigService {

    private final EnterpriseNemoConfigService enterpriseNemoConfigService;

    private final DefaultConfigService defaultConfigService;

    private final ConfigValueHandleService configValueHandleService;

    private final DeviceSubtypeModelCacheService subtypeModelCacheService;

    private final UserProfileMapper userProfileMapper;

    private final UserDeviceMapper libraUserDeviceMapper;

    private final NemoConfigService nemoConfigService;

    private final DeviceConfigMapper deviceConfigMapper;
    @Override
    public Map<String, Map<String, Object>> getEnterpriseConfig(List<BaseConfigDto> requiredConfigList,
                                                                Long userId,
                                                                Integer deviceType) {

        if (userId == null || userId < 0 || deviceType == null || deviceType < 0) {
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ResultCodeEnum.INVALID_PARAMETER.getCode(),
                    "无效的用户Id或设备类型");
        }

        UserProfileModel userProfileModel = userProfileMapper.getUserProfileByProfileId(userId);
        if (userProfileModel == null) {
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ResultCodeEnum.USER_IS_NOT_EXISTS.getCode(),
                    "用户不存在");
        }

        final String enterpriseId = userProfileModel.getEnterpriseId();
        if (StringUtils.isBlank(enterpriseId)) {
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ResultCodeEnum.INVALID_ENTERPRISE_ID.getCode(),
                    "无效企业id");
        }

        Map<String, Map<String, String>> combinedStringConfigs;
        List<EnterpriseNemoConfig> enterpriseNemoConfigList;
        if (CollectionUtils.isNotEmpty(requiredConfigList)) {
            combinedStringConfigs = defaultConfigService.getDefaultConfigByTypeAndConfigName(deviceType, -1, requiredConfigList);
            enterpriseNemoConfigList = enterpriseNemoConfigService.getEnterpriseDeviceConfigByEnterpriseIdAndConfigList(
                    enterpriseId, deviceType, -1, requiredConfigList);
        } else {
            combinedStringConfigs = defaultConfigService.getDefaultConfigByType(deviceType, -1);
            enterpriseNemoConfigList = enterpriseNemoConfigService.getEnterpriseDeviceConfigByEnterpriseId(enterpriseId, deviceType);
        }

        for (EnterpriseNemoConfig enterpriseNemoConfig : enterpriseNemoConfigList) {
            if (enterpriseNemoConfig == null) {
                continue;
            }
            final String entClientConfigName = ConfigUtils.transferCommonConfig(enterpriseNemoConfig.getClientConfigName());
            final String entConfigName = enterpriseNemoConfig.getConfigName();
            final String entConfigValue = enterpriseNemoConfig.getConfigValue();

            // 存在则覆盖旧值
            combinedStringConfigs.computeIfPresent(entClientConfigName, (oldKey, oldValue) -> {
                oldValue.put(entConfigName, entConfigValue);
                return oldValue;
            });

            // 不存在则增加新值
            combinedStringConfigs.computeIfAbsent(entClientConfigName, clientConfigName -> {
                Map<String, String> value = new HashMap<>();
                value.put(entConfigName, entConfigValue);
                return value;
            });
        }

        Map<String, Map<String, Object>> combinedObjConfigMap = new HashMap<>(16);
        combinedStringConfigs.forEach(
                (clientConfigName, value) ->
                        combinedObjConfigMap.put(clientConfigName,
                                configValueHandleService.parseConfigs(value, clientConfigName)));

        return combinedObjConfigMap;

    }

    @Override
    public Map<String, Map<String, Object>> getEnterpriseConfig(
            String enterpriseId,
            Integer deviceType,
            List<BaseConfigDto> requiredConfigList) {

        if (deviceType == null || deviceType < 0) {
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ResultCodeEnum.INVALID_PARAMETER.getCode(),
                                    "无效的设备类型");
        }
        if (StringUtils.isBlank(enterpriseId)) {
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ResultCodeEnum.INVALID_ENTERPRISE_ID.getCode(),
                                    "无效企业");
        }

        int type = -1;
        LibraDeviceSubtypeModel model = subtypeModelCacheService.getSpecificModel(deviceType);
        if (model != null) {
            type = model.getType();
        }

        Map<String, Map<String, String>> combinedStringConfigs;
        List<EnterpriseNemoConfig> enterpriseNemoConfigList;
        if (CollectionUtils.isNotEmpty(requiredConfigList)) {
            combinedStringConfigs = defaultConfigService.getDefaultConfigByTypeAndConfigName(deviceType, type, requiredConfigList);
            enterpriseNemoConfigList = enterpriseNemoConfigService.getEnterpriseDeviceConfigByEnterpriseIdAndConfigList(
                    enterpriseId, deviceType, -1, requiredConfigList);
        } else {
            combinedStringConfigs = defaultConfigService.getDefaultConfigByType(deviceType, type);
            enterpriseNemoConfigList = enterpriseNemoConfigService.getEnterpriseDeviceConfigByEnterpriseId(enterpriseId, deviceType);
        }

        for (EnterpriseNemoConfig enterpriseNemoConfig : enterpriseNemoConfigList) {
            if (enterpriseNemoConfig == null) {
                continue;
            }
            final String entClientConfigName = ConfigUtils.transferCommonConfig(enterpriseNemoConfig.getClientConfigName());
            final String entConfigName = enterpriseNemoConfig.getConfigName();
            final String entConfigValue = enterpriseNemoConfig.getConfigValue();

            // 存在则覆盖旧值
            combinedStringConfigs.computeIfPresent(entClientConfigName, (oldKey, oldValue) -> {
                oldValue.put(entConfigName, entConfigValue);
                return oldValue;
            });

            // 不存在则增加新值
            combinedStringConfigs.computeIfAbsent(entClientConfigName, clientConfigName -> {
                Map<String, String> value = new HashMap<>();
                value.put(entConfigName, entConfigValue);
                return value;
            });
        }

        Map<String, Map<String, Object>> combinedObjConfigMap = new HashMap<>(16);
        combinedStringConfigs.forEach(
                (clientConfigName, value) ->
                        combinedObjConfigMap.put(clientConfigName,
                                                 configValueHandleService.parseConfigs(value, clientConfigName)));

        return combinedObjConfigMap;

    }

    @Override
    public Map<String, Map<String, Object>> getEnterpriseAndMergeNemoConfig(String enterpriseId, String sn,
                                                                            Integer configType, List<BaseConfigDto> requiredConfigList) {

        UserDevice userDevice = libraUserDeviceMapper
                .selectOne(new LambdaQueryWrapper<UserDevice>().eq(UserDevice::getDeviceSN, sn));
        if (userDevice == null) {
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ResultCodeEnum.INVALID_PARAMETER.getCode(),
                    "设备不存在");
        }
        // 此处复用获取企业维度配置
        Map<String, Map<String, Object>> enterpriseConfig = getEnterpriseConfig(enterpriseId, userDevice.getSubType(),
                requiredConfigList);
        addDeviceConfig(enterpriseConfig, userDevice, requiredConfigList);
        return enterpriseConfig;
    }

    private void addDeviceConfig(Map<String, Map<String, Object>> allConfigs, UserDevice userDevice, List<BaseConfigDto> requiredConfigList) {
        List<BaseConfig> baseConfigPOS = deviceConfigMapper.getDeviceConfig(userDevice.getId());
        //将baseConfigPOS 转为 Map<String, Map<String, String>> allConfigs
        if (CollectionUtils.isEmpty(baseConfigPOS)) {
            return;
        }
        //将baseConfigPOS  转换为Map<String, Map<String, Object>,其中第一个key 为BaseConfig 中的clientconfigname
        //第二个key 为BaseConfig 中的configName，value为BaseConfig 中的configValue
        Map<String, Map<String, String>> deviceConfigs = new HashMap<>();
        for (BaseConfig baseConfig : baseConfigPOS) {
            baseConfig.addToConfigs(deviceConfigs);
        }
        //过滤出出仅需要的配置项（requiredConfigList）
        if (CollectionUtils.isNotEmpty(requiredConfigList)) {
            for (BaseConfigDto baseConfigDto : requiredConfigList) {
                String clientConfigName = baseConfigDto.getClientConfigName();
                String configName = baseConfigDto.getConfigName();
                Map<String, String> configMap = deviceConfigs.get(clientConfigName);
                if (configMap != null) {
                    String configValue = configMap.get(configName);
                    if (configValue != null) {
                        //allConfigs.get(clientConfigName) 可能为空，空的话需要创建一个
                        Map<String, Object> config = allConfigs.computeIfAbsent(clientConfigName, k -> new HashMap<>(16));
                        config.put(configName, configValue);
                    }
                }
            }
        }

    }

}
