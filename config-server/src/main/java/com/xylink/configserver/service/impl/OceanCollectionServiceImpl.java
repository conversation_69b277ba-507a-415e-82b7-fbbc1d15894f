
package com.xylink.configserver.service.impl;

import com.xylink.configserver.data.model.*;
import com.xylink.configserver.process.OceanConfigChangeProcessor;
import com.xylink.configserver.service.DeviceConfigService;
import com.xylink.configserver.service.DeviceService;
import com.xylink.configserver.service.NemoConfigHelper;
import com.xylink.configserver.service.OceanCollectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class OceanCollectionServiceImpl implements OceanCollectionService {
    private static final String DEVICES_CONFIG_UPDATE="devices_config_update";
    private static  final String USERS_CONFIG_UPDATE="users_config_update";

    @Value("${spring.kafka.producer.deviceConfig.topic}")
    private String deviceConfigTopic;

    @Autowired
    DeviceService deviceService;

    @Autowired
    private NemoConfigHelper helper;

    @Autowired
    DeviceConfigService deviceConfigService;

    @Autowired
    private KafkaTemplate kafkaTemplate;


    @Override
    public void deviceConfigUpdate(UserDevice device) {
        if (device == null){
            return;
        }
        Map<String, String> allConfigs = deviceConfigService.getCombinedConfig(device.getId());
        List<RestNemoConfig> configs = new ArrayList<>();
        DeviceInfo deviceInfo = deviceService.getPresenceDeviceInfo(device);
        if(allConfigs != null) {
            for(Map.Entry<String, String> entry : allConfigs.entrySet()) {
                RestNemoConfig restConfig = helper.process(entry.getKey(), entry.getValue(), deviceInfo);
                if(restConfig != null) {
                    configs.add(restConfig);
                }
            }
        }
        DeviceConfigOceanData data = new DeviceConfigOceanData(String.valueOf(device.getId()), configs);
        OceanDto dto = new OceanDto(DEVICES_CONFIG_UPDATE, data);
        String oceanRequestData =OceanConfigChangeProcessor.toJsonStr(dto);
        log.info("ocean collection data.topic:[" + deviceConfigTopic + "],payload :" + oceanRequestData);

        ListenableFuture<SendResult<String, String>> future = kafkaTemplate.send(deviceConfigTopic,oceanRequestData);
        future.addCallback(new ListenableFutureCallback<SendResult<String, String>>() {
            @Override
            public void onFailure(Throwable ex) {
                log.error("ocean collection data error.topic:[" + deviceConfigTopic + "],payload :" + oceanRequestData, ex);
            }

            @Override
            public void onSuccess(SendResult<String, String> result) {
                log.info("send success");
            }
        });

    }
}

