package com.xylink.configserver.service.impl;

import com.google.common.collect.Lists;
import com.xylink.configserver.data.dto.ServiceInspectionDTO;
import com.xylink.configserver.data.enums.ResultCodeEnum;
import com.xylink.configserver.data.exception.RestException;
import com.xylink.configserver.mapper.UserDeviceMapper;
import com.xylink.configserver.service.SeaMonitorService;
import com.xylink.configserver.service.ServiceHealthService;
import com.xylink.configserver.util.CompletableFutureUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/6/9 14:02
 */
@Slf4j
@Service
@EnableScheduling
@RequiredArgsConstructor
public class ServiceHealthServiceImpl implements ServiceHealthService {

    private final Checker checker;

    @Value("${business.service-inspection.enable:true}")
    private boolean enableServiceInspect;


    // 一分钟同步一次
    @Scheduled(fixedRate = 60 * 1000)
    public void refresh() {
        List<ServiceInspectionDTO> resultList = inspect();
        log.info("exec scheduled inspect, result:{}", resultList);
    }


    @Override
    public List<ServiceInspectionDTO> inspect() {

        if (!enableServiceInspect) {
            throw RestException.newInstanceOfHttpStatus400(ResultCodeEnum.SERVICE_INSPECTION_FORBIDDEN,
                    "已停用巡检功能, 请检查服务配置");
        }

        List<ServiceInspectionDTO> results = checker.check(
                Checker.Type.DB,
                Checker.Type.KAFKA,
                Checker.Type.REDIS

        );
        if (CollectionUtils.isEmpty(results)) {
            return Collections.emptyList();
        }
        return results;
    }

}

@Service
@RequiredArgsConstructor
@Slf4j
class Checker {

    private final SeaMonitorService seaMonitorService;

    private final DBChecker dbChecker;

    private final RedisChecker redisChecker;

    private final KafkaChecker kafkaChecker;

    @Getter
    @AllArgsConstructor
    enum Type {
        DB("MS_mysql_conn"),
        REDIS("MS_redis_conn"),
        KAFKA("MS_kafka_conn");

        private final String inspectionItem;
    }

    public List<ServiceInspectionDTO> check(Type... types) {
        if (types.length == 0) {
            return Collections.emptyList();
        }

        List<ServiceInspectionDTO> results = Lists.newArrayListWithExpectedSize(types.length);
        for (Type inputType : types) {
            // 增加超时时间以适应Kafka连接检查
            CompletableFutureUtil.runAsync(10, TimeUnit.SECONDS, () -> {
                switch (inputType) {
                    case DB: {
                        results.add(doCheck(inputType, dbChecker::check));
                        break;
                    }
                    case REDIS: {
                        results.add(doCheck(inputType, redisChecker::check));
                        break;
                    }
                    case KAFKA: {
                        results.add(doCheck(inputType, kafkaChecker::check));
                        break;
                    }
                    default: {
                    }
                }
            }).join();
        }

        return results;
    }

    private ServiceInspectionDTO doCheck(Type type, Procedure input) {
        try {
            input.run();
        } catch (Exception e) {
            log.error("inspection error: type-{}", type, e);
            //巡检异常增加夜莺告警上报
            seaMonitorService.reportSignalAlarm(type.getInspectionItem(), null);
            return ServiceInspectionDTO.buildErr(type.getInspectionItem(), e.getLocalizedMessage());
        }
        return ServiceInspectionDTO.buildSucc(type.getInspectionItem());
    }

    @FunctionalInterface
    private interface Procedure {
        void run();
    }

}

@Service
@RequiredArgsConstructor
class DBChecker {

    private final UserDeviceMapper userDeviceMapper;

    public void check() {
        userDeviceMapper.selectById(0);
    }

}

@Service
class RedisChecker {

    @Setter(onMethod_ = {@Autowired, @Qualifier("redisCraftTemplate")})
    private RedisTemplate<String, String> redisCraftTemplate;

    @Setter(onMethod_ = {@Autowired, @Qualifier("redisConfigTemplate")})
    private RedisTemplate<String, String> redisConfigTemplate;

    @Setter(onMethod_ = {@Autowired, @Qualifier("redisAccessTemplate")})
    private RedisTemplate<String, String> redisAccessTemplate;

    public void check() {
        final String defKey = "redis.key";
        redisCraftTemplate.opsForValue().get(defKey);
        redisConfigTemplate.opsForValue().get(defKey);
        redisAccessTemplate.opsForValue().get(defKey);
    }

}

@Service
@RequiredArgsConstructor
class KafkaChecker {

    @Value("${spring.kafka.bootstrap.servers}")
    private String businessBootstrapServers;

    @Value("${spring.kafka.security.protocol:}")
    private String securityProtocol;

    @Value("${spring.kafka.security.sasl.mechanism:}")
    private String securitySaslMechanism;

    @Value("${spring.kafka.security.sasl.config:}")
    private String securityJaasConfig;

    public void check() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, this.businessBootstrapServers);
        // 增加超时时间以适应网络延迟
        props.put(AdminClientConfig.DEFAULT_API_TIMEOUT_MS_CONFIG, 5000);
        props.put(AdminClientConfig.REQUEST_TIMEOUT_MS_CONFIG, 5000);
        props.put(AdminClientConfig.METADATA_MAX_AGE_CONFIG, 5000);

        // 添加安全配置支持
        if (StringUtils.hasText(securityProtocol)) {
            props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, securityProtocol);
        }
        if (StringUtils.hasText(securitySaslMechanism)) {
            props.put(SaslConfigs.SASL_MECHANISM, securitySaslMechanism);
        }
        if (StringUtils.hasText(securityJaasConfig)) {
            props.put(SaslConfigs.SASL_JAAS_CONFIG, securityJaasConfig);
        }

        AdminClient adminClient = null;
        try {
            adminClient = AdminClient.create(props);
            // 使用更简单的连接测试，避免复杂的元数据操作
            adminClient.describeCluster().clusterId().get(4, TimeUnit.SECONDS);
        } catch (Exception e) {
            throw new RuntimeException("Kafka连接检查失败: " + e.getMessage(), e);
        } finally {
            if (adminClient != null) {
                try {
                    adminClient.close(Duration.ofSeconds(2));
                } catch (Exception e) {
                    // 忽略关闭异常
                }
            }
        }
    }

}