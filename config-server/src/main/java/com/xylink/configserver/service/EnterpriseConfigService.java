package com.xylink.configserver.service;

import com.xylink.configserver.data.dto.BaseConfigDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-07-18 18:16
 */
public interface EnterpriseConfigService {


    Map<String, Map<String, Object>> getEnterpriseConfig(List<BaseConfigDto> requiredConfigList, Long userId, Integer deviceType);

    Map<String, Map<String, Object>> getEnterpriseConfig(
            String enterpriseId, Integer deviceType, List<BaseConfigDto> requiredConfigList);

    Map<String, Map<String, Object>> getEnterpriseAndMergeNemoConfig(String enterpriseId, String sn, Integer configType,
            List<BaseConfigDto> requiredConfigList);

}
