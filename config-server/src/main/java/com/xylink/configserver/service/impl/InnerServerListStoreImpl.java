package com.xylink.configserver.service.impl;

import com.xylink.configserver.service.BaseServerListStore;
import com.xylink.configserver.service.ServerListStore;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

@Service("innerServerListStore")
public class InnerServerListStoreImpl extends BaseServerListStore implements ServerListStore {

    private volatile AtomicReference<Map<String,Object>> enServersInfoSubject = new AtomicReference<>();

    @PostConstruct
    void parseServerList() {
        InputStream enServersInfoFile = this.getClass().getClassLoader().getResourceAsStream("servers_info_en_inner.json");
        enServersInfoSubject.set(processServerList(enServersInfoFile));
    }

    @Override
    public Map<String, Object> getEnterpriseServerInfo() {
        return enServersInfoSubject.get();
    }
}
