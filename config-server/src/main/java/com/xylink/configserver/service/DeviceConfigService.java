package com.xylink.configserver.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xylink.configserver.data.dto.BatchUpdateDeviceConfigResponseDTO;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.data.response.R;
import com.xylink.configserver.data.vo.DeviceConfigVo;

import java.util.List;
import java.util.Map;

public interface DeviceConfigService extends IService<DeviceConfig> {

    Map<String, String> getCombinedConfig(long deviceId);
    Map<String,String> getCombinedCallConfig(long nemoId);

    Map<String, Map<String, String>> getOriginHardConfig(UserDevice device);

    List<DeviceConfig> getDeviceClientConfigs(long deviceId, String clientConfigName);

    String getNemoConfig(long deviceId, String configName);

    String getNemoConfig(long deviceId, String configName, String clientConfigName);

    String getDeviceConfig(UserDevice userDevice, String configName);

    void deleteConfig(DeviceConfig config);

    void deleteConfigByName(DeviceConfigVo[] deviceConfigList);

    void addDeviceConfig(DeviceConfig config);

    void applyNemoConfig(long nemoId, String configName,String configValue, long expireTime, String clientConfigName);

    void deleteUserNemoRelation(long nemoId);

    void addUserNemoRelation(long userId, long nemoId);

    void updateInternalDeviceConfig(DeviceConfigUpdate[] configs, long deviceId);

    void updateDeviceConfig(Map<String, String> configs, long deviceId);

    /**
     * 获取H323配置信息
     *
     * @param gwDevicePO
     * @return
     */
    Map<String, String> getCombinedH323Config(GwDevice gwDevicePO);


    R<BatchUpdateDeviceConfigResponseDTO<DeviceConfigUpdateWithSn>> batchUpdateDeviceConfig(List<DeviceConfigUpdateWithSn> deviceConfigList);

    void updateInternalDeviceConfigBySn(DeviceConfigUpdate config, String sn);

    void deleteConfigByNameAndSn(DeviceConfigUpdate config, String sn);
}
