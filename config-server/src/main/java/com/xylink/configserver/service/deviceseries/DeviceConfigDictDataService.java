package com.xylink.configserver.service.deviceseries;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xylink.configserver.data.model.deviceseries.ConfigDataValuesDto;
import com.xylink.configserver.data.model.deviceseries.DeviceConfigDictDataEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-02-18 15:19
 */
public interface DeviceConfigDictDataService extends IService<DeviceConfigDictDataEntity> {
    /**
     * query by dictType
     *
     * @param dictType
     * @return
     */
    List<DeviceConfigDictDataEntity> getByDictType(String dictType);

    /**
     * query by dictType
     *
     * @param dictType
     * @return
     */
    List<ConfigDataValuesDto> getDtoByDictType(String dictType);
}
