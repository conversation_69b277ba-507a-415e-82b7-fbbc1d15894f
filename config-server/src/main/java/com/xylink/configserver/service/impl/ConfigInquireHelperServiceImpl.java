package com.xylink.configserver.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xylink.configserver.data.enums.DeviceSubType;
import com.xylink.configserver.data.enums.Resolution;
import com.xylink.configserver.data.exception.RestException;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.data.vo.InquireHelperVO;
import com.xylink.configserver.enums.Configs;
import com.xylink.configserver.mapper.CommonGroupConfigMapper;
import com.xylink.configserver.mapper.DeviceConfigMapper;
import com.xylink.configserver.mapper.SpecialConfigMapper;
import com.xylink.configserver.service.*;
import com.xylink.configserver.util.DeviceSnParse;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.util.TextUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

@Service
public class ConfigInquireHelperServiceImpl implements ConfigInquireHelperService {

    //service
    @Autowired
    private DeviceService deviceService;

    @Autowired
    private NewUserDeviceService newUserDeviceService;
    @Autowired
    RechargeConfigService rechargeConfigService;

    @Autowired
    private EnterpriseNemoConfigService enterpriseNemoConfigService;

    @Autowired
    private PlatformConfigService platformConfigService;

    @Autowired
    private DeviceSoftVersionService deviceSoftVersionService;

    @Autowired
    private DefaultConfigService defaultConfigService;

    //mapper
    @Autowired
    private CommonGroupConfigMapper commonGroupConfigMapper;

    @Autowired
    private DeviceConfigMapper deviceConfigMapper;

    @Autowired
    private SpecialConfigMapper specialConfigMapper;

    @Autowired
    DeviceConfigService deviceConfigService;

    @Autowired
    UserConfigService userConfigService;

    /*
     * 倒排查询生效配置
     * 所有查询的生效配置项封装为ConfigTableEntity对象
     * */
    @Override
    public ContainerConfigTableModel inquireConfig(InquireHelperVO inquireHelperVO) {
        //区分软硬查询
        Integer queryType = inquireHelperVO.getQueryType();
        HashMap<String, ConfigTableEntity> hashMap = new HashMap<>();
        UserDeviceEntity userDevice = null;
        if ((ObjectUtils.isNotEmpty(inquireHelperVO.getUserDeviceId()) || ObjectUtils.isNotEmpty(inquireHelperVO.getDeviceSn()) && queryType == 0)) {
            if (StringUtils.isNotBlank(inquireHelperVO.getDeviceSn())) {
                userDevice = newUserDeviceService.getOne(new QueryWrapper<UserDeviceEntity>().eq("device_sn", inquireHelperVO.getDeviceSn()));
            } else if (ObjectUtils.isNotEmpty(inquireHelperVO.getUserDeviceId())) {
                userDevice = newUserDeviceService.getOne(new QueryWrapper<UserDeviceEntity>().eq("id", inquireHelperVO.getUserDeviceId()));
            }
            if (ObjectUtils.isEmpty(userDevice)) {
                throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, "设备不存在");
            }
            inquireHelperVO.setEnterpriseId(userDevice.getEnterpriseId());
        }
        List<String> configs = inquireHelperVO.getConfigs();

        //查询1: 如果configs为空  执行单设备覆盖查询
        if (CollectionUtils.isEmpty(configs)) {
            //正向查询
            if (queryType == 0) {
                Map<String, String> combinedConfig = deviceConfigService.getCombinedConfig(inquireHelperVO.getUserDeviceId());
                hashMap.put("info", new ConfigTableEntity("info", "正向查询配置", "", combinedConfig));
            } else {
                //软终端查询,如果type是1,用户id必须有值
                Map<String, String> combinedConfig = userConfigService.getCombinedConfig(Long.parseLong(inquireHelperVO.getUserProfileId()), inquireHelperVO.getType());
                hashMap.put("info", new ConfigTableEntity("info", "正向查询配置", "", combinedConfig));
            }


        } else {
            //查询2:如果configs不为空,执行单一查询
            if (queryType == 0) {
                execSingleQuery(hashMap, userDevice, configs, inquireHelperVO);
            } else {
                //查询软终端
            }
        }

        ContainerConfigTableModel containerConfigTableModel = new ContainerConfigTableModel();
        containerConfigTableModel.setConfigTableEntityHashMap(hashMap);
        containerConfigTableModel.setUserDeviceEntity(userDevice);
        return containerConfigTableModel;
    }


    private void execSingleQuery(HashMap<String, ConfigTableEntity> hashMap, UserDeviceEntity userDevice, List<String> configs, InquireHelperVO inquireHelperVO) {
        //1. 4k查询
        selectConfigsBy4k(hashMap, userDevice, configs);
        cleanConfigsList(hashMap, configs);
        //2. 终端自定义配置查询
        if (!CollectionUtils.isEmpty(configs)) {
            selectConfigsByDeviceCustomize(hashMap, userDevice, configs, inquireHelperVO);
        }

        //3. 特殊功能配置查询
        if (!CollectionUtils.isEmpty(configs)) {
            selectConfigsBySpecialConfiguration(hashMap, userDevice, configs, inquireHelperVO);
        }

        //4.企业配置查询
        if (!CollectionUtils.isEmpty(configs) && !TextUtils.isEmpty(inquireHelperVO.getEnterpriseId())) {
            selectConfigsByEnterpriseConfiguration(hashMap, userDevice, configs, inquireHelperVO);
        }

        //5.平台配置查询
        if (!CollectionUtils.isEmpty(configs) && !TextUtils.isEmpty(inquireHelperVO.getPlatform())) {
            selectConfigsByPlatformConfiguration(hashMap, userDevice, configs, inquireHelperVO);
        }

        //6.默认版本配置
        if (!CollectionUtils.isEmpty(configs)) {
            selectConfigsByDefaultVersionConfigByVersion(hashMap, userDevice, configs, inquireHelperVO);
        }

        //7.默认企业配置
        if (!CollectionUtils.isEmpty(configs)) {
            selectConfigsByDefaultEnterpriseConfig(hashMap, userDevice, configs, inquireHelperVO);
        }

        //8.默认配置
        if (!CollectionUtils.isEmpty(configs)) {
            selectConfigsByDefaultConfig(hashMap, userDevice, configs, inquireHelperVO);
        }

    }

    private void selectConfigsByDefaultConfig(HashMap<String, ConfigTableEntity> hashMap, UserDeviceEntity userDevice, List<String> configs, InquireHelperVO inquireHelperVO) {
        Map<String, Map<String, String>> defaultNemoConfig;
        if (userDevice.getSubType() <= DeviceSubType.NEMO.getValue()) {
            defaultNemoConfig = defaultConfigService.getDefaultNemoConfig(DeviceSnParse.getProductFamilyBySn(userDevice.getDeviceSn()).name(), userDevice.getSubType());
        } else {
            defaultNemoConfig = defaultConfigService.getDefaultConfigByType(userDevice.getSubType(), userDevice.getDeviceType());
        }

        String clientConfigName = inquireHelperVO.getClientConfigName();
        if (TextUtils.isEmpty(clientConfigName)) {
            clientConfigName = "common";
        }
        Map<String, String> map;
        map = defaultNemoConfig.get(clientConfigName);


        if (MapUtils.isNotEmpty(map)) {
            Iterator<String> iterator = configs.iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                String value = map.get(key);
                if (!TextUtils.isEmpty(value)) {
                    hashMap.put(key, new ConfigTableEntity(key, value, "libra_default_config", null));
                    iterator.remove();
                }
            }
        }
    }

    private void selectConfigsByDefaultEnterpriseConfig(HashMap<String, ConfigTableEntity> hashMap, UserDeviceEntity userDevice, List<String> configs, InquireHelperVO inquireHelperVO) {
        //{"common":{"configName":"configValue"}}
        Map<String, Map<String, String>> enterpriseConfig = defaultConfigService.getEnterpriseConfig(userDevice.getSubType(), userDevice.getDeviceType());
        Map<String, String> map;
        if (TextUtils.isEmpty(inquireHelperVO.getClientConfigName())) {
            inquireHelperVO.setClientConfigName("common");
        }
        map = enterpriseConfig.get(inquireHelperVO.getClientConfigName());

        if (MapUtils.isNotEmpty(map)) {
            Iterator<String> iterator = configs.iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                String value = map.get(key);
                if (!TextUtils.isEmpty(value)) {
                    hashMap.put(key, new ConfigTableEntity(key, value, "[默认企业配置]libra_enterprise_nemo_config", null));
                    iterator.remove();
                }
            }
        }

    }

    private void selectConfigsByDefaultVersionConfigByVersion(HashMap<String, ConfigTableEntity> hashMap, UserDeviceEntity userDevice, List<String> configs, InquireHelperVO inquireHelperVO) {
        HashMap<String, String> map = new HashMap<>();
        long deviceFirstBindTime = deviceService.getNemoFirstBindTime(userDevice.getDeviceSn());
        if (deviceFirstBindTime <= 0) {
            deviceFirstBindTime = userDevice.getBindTimestamp();
        }
        DeviceSoftVersion softVersionPO = deviceSoftVersionService.getDeviceVersionBySn(userDevice.getDeviceSn());
        List<NemoDefaultVersionConfig> defaultVersionConfigPOS;
        if (softVersionPO != null && StringUtils.isNotBlank(softVersionPO.getCurrentSoftVersion())) {
            defaultVersionConfigPOS = defaultConfigService.getNemoDefaultConfigBytype(userDevice.getDeviceType());
            if (!CollectionUtils.isEmpty(defaultVersionConfigPOS)) {
                for (NemoDefaultVersionConfig defaultVersionConfigPO : defaultVersionConfigPOS) {
                    if (softVersionPO.getCurrentSoftVersion().compareTo((defaultVersionConfigPO.getConfigVersionList())) >= 0) {
                        map.put(defaultVersionConfigPO.getConfigName(), defaultVersionConfigPO.getConfigValue());
                    }

                }
            }
            defaultVersionConfigPOS = defaultConfigService.getNemoDefaultConfigBytype(userDevice.getSubType());
            if (defaultVersionConfigPOS != null && defaultVersionConfigPOS.size() > 0) {
                for (NemoDefaultVersionConfig defaultVersionConfigPO : defaultVersionConfigPOS) {

                    if (softVersionPO.getCurrentSoftVersion().compareTo((defaultVersionConfigPO.getConfigVersionList())) >= 0) {
                        map.put(defaultVersionConfigPO.getConfigName(), defaultVersionConfigPO.getConfigValue());
                    }
                }
            }
        }


        if (!TextUtils.isEmpty(inquireHelperVO.getAv())) {
            try {
                int av = Integer.parseInt(inquireHelperVO.getAv());
                List<NemoDefaultVersionConfig> defaultVersionConfigsForType = defaultConfigService.getDefaultVersionConfigByTypeAndVersion(userDevice.getDeviceType(), av);
                if (!CollectionUtils.isEmpty(defaultVersionConfigsForType)) {
                    defaultVersionConfigsForType.forEach((item) -> map.put(item.getConfigName(), item.getConfigValue())
                    );
                }
                List<NemoDefaultVersionConfig> defaultVersionConfigsForSubType = defaultConfigService.getDefaultVersionConfigByTypeAndVersion(userDevice.getSubType(), av);
                if (!CollectionUtils.isEmpty(defaultVersionConfigsForSubType)) {
                    defaultVersionConfigsForSubType.forEach((item) -> map.put(item.getConfigName(), item.getConfigValue())
                    );
                }
            } catch (NumberFormatException e) {
            }
        }

        Iterator<String> iterator = configs.iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            String value = map.get(key);
            if (!TextUtils.isEmpty(value)) {
                hashMap.put(key, new ConfigTableEntity(key, value, "libra_default_version_cfg", null));
                iterator.remove();
            }
        }

    }


    private void selectConfigsByPlatformConfiguration(HashMap<String, ConfigTableEntity> hashMap, UserDeviceEntity userDevice, List<String> configs, InquireHelperVO inquireHelperVO) {
        HashMap<String, String> map = new HashMap<>();
        List<NemoDefaultPlatformConfig> platformAndConfigType = platformConfigService.getByPlatformAndConfigType(inquireHelperVO.getPlatform(), userDevice.getDeviceType());
        if (!CollectionUtils.isEmpty(platformAndConfigType)) {
            for (NemoDefaultPlatformConfig nemoDefaultPlatformConfig : platformAndConfigType) {
                map.put(nemoDefaultPlatformConfig.getConfigName(), nemoDefaultPlatformConfig.getConfigValue());
            }
        }

        List<NemoDefaultPlatformConfig> platformAndConfigSubtype = platformConfigService.getByPlatformAndConfigType(inquireHelperVO.getPlatform(), userDevice.getSubType());
        if (!CollectionUtils.isEmpty(platformAndConfigSubtype)) {
            for (NemoDefaultPlatformConfig nemoDefaultPlatformConfig : platformAndConfigSubtype) {
                map.put(nemoDefaultPlatformConfig.getConfigName(), nemoDefaultPlatformConfig.getConfigValue());
            }
        }

        Iterator<String> iterator = configs.iterator();
        while (iterator.hasNext()) {
            String configName = iterator.next();
            String value = map.get(configName);
            if (!TextUtils.isEmpty(value)) {
                hashMap.put(configName, new ConfigTableEntity(configName, value, "libra_default_platform_cfg", null));
                iterator.remove();
            }
        }
    }

    private void selectConfigsByEnterpriseConfiguration(HashMap<String, ConfigTableEntity> hashMap, UserDeviceEntity userDevice, List<String> configs, InquireHelperVO inquireHelperVO) {
        //1.查询出企业配置
        //1.1
        HashMap<String, String> map = enterpriseNemoConfigService
                .selectConfigsByEnterpriseConfiguration(inquireHelperVO.getEnterpriseId(), inquireHelperVO.getClientConfigName(),
                        userDevice.getDeviceType(), userDevice.getSubType(), userDevice.getDeviceSn());

        //1.2 过滤
        Iterator<String> iterator = configs.iterator();
        while (iterator.hasNext()) {
            String configName = iterator.next();
            String value = map.get(configName);
            if (!TextUtils.isEmpty(value)) {
                hashMap.put(configName, new ConfigTableEntity(configName, value, "libra_enterprise_nemo_config", null));
                iterator.remove();
            }
        }


    }

    private void cleanConfigsList(HashMap<String, ConfigTableEntity> hashMap, List<String> configs) {
        configs.removeIf(configName -> hashMap.get(configName) != null);
    }


    private void selectConfigsBy4k(HashMap<String, ConfigTableEntity> hashMap, UserDeviceEntity userDevice, List<String> configs) {
        List<RechargeConfig> rechargeConfigPOS = rechargeConfigService.getRechargeConfigs(userDevice.getDeviceSn());
        String enable4kResolution = null;
        long currentTimeMill = System.currentTimeMillis();

        if (!CollectionUtils.isEmpty(rechargeConfigPOS)) {
            for (RechargeConfig baseConfig : rechargeConfigPOS) {
                if (baseConfig.getConfigExpireTime() != null && currentTimeMill < baseConfig.getConfigExpireTime()) {
                    if (Configs.ENABLE_4K_RESOLUTION.equalsIgnoreCase(baseConfig.getConfigName())) {
                        enable4kResolution = baseConfig.getConfigValue();
                        break;
                    }
                }
            }
        }

        if (Boolean.TRUE.toString().equalsIgnoreCase(enable4kResolution)) {
            //如果开启4k ,获取所有4K配置
            List<CommonGroupConfig> groupConfigs = commonGroupConfigMapper.getGroupConfigsByType(Resolution.K4.name(), 0);
            HashMap<String, String> sourceMap = new HashMap<>();
            for (BaseConfig groupConfig : groupConfigs) {
                sourceMap.put(groupConfig.getConfigName(), groupConfig.getConfigValue());
            }
            for (String configName : configs) {
                String value = sourceMap.get(configName);
                if (!TextUtils.isEmpty(value)) {
                    ConfigTableEntity configTableEntity = new ConfigTableEntity(configName, value, "libra_common_group_config", null);
                    hashMap.put(configName, configTableEntity);
                }
            }

        }
    }


    private void selectConfigsByDeviceCustomize(HashMap<String, ConfigTableEntity> hashMap, UserDeviceEntity userDevice, List<String> configs, InquireHelperVO inquireHelperVO) {
        List<BaseConfig> baseConfigPOS = deviceConfigMapper.getDeviceConfigByCondition(userDevice.getId(), inquireHelperVO.getClientConfigName());
        if (!CollectionUtils.isEmpty(baseConfigPOS)) {
            HashMap<String, String> map = new HashMap<>();
            for (BaseConfig baseConfigPO : baseConfigPOS) {
                map.put(baseConfigPO.getConfigName(), baseConfigPO.getConfigValue());
            }

            Iterator<String> iterator = configs.iterator();
            while (iterator.hasNext()) {
                String next = iterator.next();
                String value = map.get(next);
                if (!TextUtils.isEmpty(value)) {
                    hashMap.put(next, new ConfigTableEntity(next, value, "libra_nemo_config", null));
                    iterator.remove();
                }
            }
        }
    }

    private void selectConfigsBySpecialConfiguration(HashMap<String, ConfigTableEntity> hashMap, UserDeviceEntity userDevice, List<String> configs, InquireHelperVO inquireHelperVO) {
        List<SpecialConfig> configList = specialConfigMapper.getSpecialConfigByCondition(userDevice.getDeviceSn(), inquireHelperVO.getUserProfileId(), inquireHelperVO.getClientConfigName());
        if (!CollectionUtils.isEmpty(configList)) {
            HashMap<String, String> map = new HashMap<>();
            for (SpecialConfig specialConfig : configList) {
                map.put(specialConfig.getConfigName(), specialConfig.getConfigValue());
            }

            Iterator<String> iterator = configs.iterator();
            while (iterator.hasNext()) {
                String next = iterator.next();
                String value = map.get(next);
                if (!TextUtils.isEmpty(value)) {
                    hashMap.put(next, new ConfigTableEntity(next, value, "libra_special_feature_config", null));
                    iterator.remove();
                }
            }

        }
    }


    @Override
    public List<Map<String, Object>> selectCondition(String condition) {

        return newUserDeviceService.selectCondition(condition);
    }

}