package com.xylink.configserver.service.deviceseries.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xylink.configserver.data.model.DeviceSubtypeModelV2;
import com.xylink.configserver.data.model.UserDevice;
import com.xylink.configserver.data.model.deviceseries.*;
import com.xylink.configserver.mapper.deviceseries.DeviceSubtypeMapper;
import com.xylink.configserver.service.DeviceConfigService;
import com.xylink.configserver.service.DeviceService;
import com.xylink.configserver.service.deviceseries.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021-01-18 15:58
 */
@Service
public class DeviceSubtypeServiceImpl extends ServiceImpl<DeviceSubtypeMapper, DeviceSubtypeModelV2> implements DeviceSubtypeService {

    @Resource
    private DeviceSeriesService deviceSeriesService;
    @Resource
    private DeviceSubtypeSeriesConfigDictionaryService deviceSubtypeSeriesConfigDictionaryService;
    @Resource
    private DeviceSubtypeSeriesConfigService deviceSubtypeSeriesConfigService;
    @Resource
    private DeviceSeriesSubTypeService deviceSeriesSubTypeService;
    @Resource
    private DeviceService deviceService;
    @Resource
    private DeviceConfigService deviceConfigService;
    @Resource
    private DeviceSubtypeSeriesConfigDictionaryDataService deviceSubtypeSeriesConfigDictionaryDataService;
    @Resource
    private DeviceConfigDictDataService deviceConfigDictDataService;

    @Override

    public Page<DeviceSubtypeListDto> page(Page<DeviceSubtypeListDto> page, String key) {
        return this.baseMapper.page(page, key);
    }

    @Override
    public DeviceSubtypeDetailDto detail(int subtype) {
        DeviceSubtypeDto deviceSubtype = this.baseMapper.getBySubtype(subtype);
        if (deviceSubtype == null) {
            return null;
        }
        deviceSubtype.setConfigs(getSubtypeConfigs(subtype));
        deviceSubtype.setTypeName(ConfigDictionaryUtils.typeName(deviceSubtype.getType()));
        DeviceSubtypeDetailDto dto = new DeviceSubtypeDetailDto();
        dto.setDeviceSubtype(deviceSubtype);
        DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity = deviceSeriesSubTypeService.getSeries(deviceSubtype.getSubtype());
        if (deviceSeriesSubtypeEntity != null) {
            DeviceSeriesDto series = deviceSeriesService.getDeviceSeriesDto(deviceSeriesSubtypeEntity.getSeriesId());
            // 丢弃系列没有选中的配置项
            series.setConfigs(ConfigDictionaryUtils.excludeNotSelectedTree(series.getConfigs()));
            dto.setDeviceSeries(series);
        }
        return dto;
    }

    @Override
    public boolean updateSubtypeCategoryDisplayName(String categoryDisplay, int subtype) {
        this.baseMapper.updateSubtypeCategoryDisplayName(categoryDisplay, subtype);
        return true;
    }

    @Override
    public boolean saveOrUpdateDeviceSubtype(DeviceSubtypeDetailUpdateDto deviceSubtypeDetailUpdateDto) {
        DeviceSubtypeDetailUpdateDto.DeviceSubtypeUpdateDto deviceSubtype = deviceSubtypeDetailUpdateDto.getDeviceSubtype();
        int subtype = deviceSubtype.getSubtype();
        //更新终端显示名称
        this.updateSubtypeCategoryDisplayName(deviceSubtype.getCategoryDisplayName(), subtype);
        //保存终端选择配置
        deviceSubtypeSeriesConfigService.deleteSubtypeConfigBySubtype(subtype);
        List<DeviceSubtypeSeriesConfigEntity> saveEntity = of(subtype, deviceSubtype.getConfigs());
        if (!CollectionUtils.isEmpty(saveEntity)) {
            deviceSubtypeSeriesConfigService.saveBatch(saveEntity);
        }
        // 保存系列和终端的对应关系
        DeviceSeriesDto deviceSeries = deviceSubtypeDetailUpdateDto.getDeviceSeries();
        if (deviceSeries == null || deviceSeries.getId() == null) {
            deviceSeriesSubTypeService.removeBySubtype(subtype);
        } else {
            deviceSeriesSubTypeService.saveOrUpdateSeriesSubtypeMapping(deviceSeries.getId(), subtype);
        }
        return true;
    }

    @Override
    public DeviceSubtypeDto getBySubtype(int subtype) {
        return this.baseMapper.getBySubtype(subtype);
    }

    @Override
    public DeviceSubtypeDto getByDeviceId(long deviceId, PlatformEnum platformEnum) {
        UserDevice po = deviceService.getUserDeviceByNemoId(deviceId);
        Objects.requireNonNull(po, "Device not found by id: " + deviceId);
        int subtype = po.getSubType();
        DeviceSubtypeDto deviceSubtypeDto = this.getBySubtype(subtype);
        if (deviceSubtypeDto != null) {
            deviceSubtypeDto.setDeviceId(deviceId);
            List<DeviceSeriesSubtypeConfigsDto> subtypeConfigs = ConfigDictionaryUtils.excludeNotSelectedTree(getSubtypeConfigs(subtype, platformEnum));
            if (!CollectionUtils.isEmpty(subtypeConfigs)) {
                deviceSubtypeDto.setConfigs(subtypeConfigs);
                Map<String, Map<String, String>> configsValue = deviceConfigService.getOriginHardConfig(po);
                if (!CollectionUtils.isEmpty(configsValue)) {
                    // 为当前选择的配置项赋值
                    subtypeConfigs.forEach(item -> configsData(item, configsValue));
                }
            }
        }
        return deviceSubtypeDto;
    }

    private List<DeviceSeriesSubtypeConfigsDto> getSubtypeConfigs(int subtype, PlatformEnum... platformEnums) {
        List<DeviceSubtypeSeriesConfigDictionaryEntity> configDictionary = getDeviceSubtypeSeriesConfigDictionaryEntities(platformEnums);
        if (!CollectionUtils.isEmpty(configDictionary)) {
            List<DeviceSubtypeSeriesConfigEntity> selectedConfig = getSelectedDeviceSubtypeSeriesConfigEntities(subtype, platformEnums);
            return ConfigDictionaryUtils.parseTree(configDictionary, selectedConfig);
        }
        return Collections.emptyList();
    }

    private List<DeviceSubtypeSeriesConfigDictionaryEntity> getDeviceSubtypeSeriesConfigDictionaryEntities(PlatformEnum... platformEnums) {
        List<DeviceSubtypeSeriesConfigDictionaryEntity> configDictionary = deviceSubtypeSeriesConfigDictionaryService.getSubtypeConfigDictionary();
        if (CollectionUtils.isEmpty(configDictionary)) {
            return Collections.emptyList();
        }
        if (platformEnums.length == 0) {
            // 转换加上平台的概念
            List<DeviceSubtypeSeriesConfigDictionaryEntity> appendLists = new ArrayList<>();
            configDictionary = configDictionary.stream().peek(item -> {
                if (ConfigTypeEnum.ITEM.name().equalsIgnoreCase(item.getConfigType())) {
                    // 处理
                    for (PlatformEnum platformEnum : PlatformEnum.values()) {
                        DeviceSubtypeSeriesConfigDictionaryEntity append = new DeviceSubtypeSeriesConfigDictionaryEntity();
                        BeanUtils.copyProperties(item, append);
                        append.setId(-1L);
                        append.setConfigCode(platformEnum.name());
                        append.setPId(item.getId());
                        append.setConfigName(platformEnum.getText());
                        append.setConfigComment(platformEnum.getText());
                        append.setConfigOrder(platformEnum.ordinal() + 1);
                        appendLists.add(append);
                    }
                    item.setConfigType(ConfigTypeEnum.GROUP.name());
                }
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(appendLists)) {
                configDictionary.addAll(appendLists);
            }
        }
        return configDictionary;
    }

    private List<DeviceSubtypeSeriesConfigEntity> getSelectedDeviceSubtypeSeriesConfigEntities(int subtype, PlatformEnum[] platformEnums) {
        List<DeviceSubtypeSeriesConfigEntity> selectedConfig = deviceSubtypeSeriesConfigService.getSubtypeConfig(subtype);
        if (platformEnums.length > 0) {
            selectedConfig = selectedConfig.stream().filter(item -> {
                for (PlatformEnum platformEnum : platformEnums) {
                    if (platformEnum.name().equalsIgnoreCase(item.getPlatform())) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
        }
        return selectedConfig;
    }

    private List<DeviceSubtypeSeriesConfigEntity> of(int subtype, List<DeviceSubtypeDetailUpdateDto.DeviceSubtypeUpdateDto.SelectedConfigs> deviceSeriesSubtypeConfigsDto) {
        if (CollectionUtils.isEmpty(deviceSeriesSubtypeConfigsDto)) {
            return Collections.emptyList();
        }
        return deviceSeriesSubtypeConfigsDto.stream().map(item -> {
            DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
            deviceSubtypeSeriesConfigEntity.setSubtype(subtype);
            deviceSubtypeSeriesConfigEntity.setConfigId(item.getConfigId());
            deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SUBTYPE.name());
            deviceSubtypeSeriesConfigEntity.setPlatform(item.getPlatform());
            return deviceSubtypeSeriesConfigEntity;
        }).collect(Collectors.toList());
    }

    /**
     * 设置关联的配置项、当前值以及取值范围
     *
     */
    private void configsData(DeviceSeriesSubtypeConfigsDto deviceSeriesSubtypeConfigsDto, Map<String, Map<String, String>> configsValue) {
        //设置关联的配置项、当前值以及取值范围
        List<DeviceSubtypeSeriesConfigDictionaryDataEntity> configDataEntity = deviceSubtypeSeriesConfigDictionaryDataService.getByConfigIdAndSpecial(deviceSeriesSubtypeConfigsDto.getConfigId(), 0);
        if (!CollectionUtils.isEmpty(configDataEntity)) {
            List<ConfigDataDto> configDataDtoList = new ArrayList<>(configDataEntity.size());
            for (DeviceSubtypeSeriesConfigDictionaryDataEntity dataEntity : configDataEntity) {
                ConfigDataDto configDataDto = new ConfigDataDto();
                configDataDto.setClientConfigName(dataEntity.getClientConfigName());
                configDataDto.setConfigName(dataEntity.getConfigName());
                configDataDto.setConfigValuesType(dataEntity.getConfigValuesType());
                // 设置系列当前值
                configDataDto.setConfigValue(getCurrentConfigValue(configsValue, dataEntity));
                // 设置取值范围
                List<ConfigDataValuesDto> configDataValuesDto = deviceConfigDictDataService.getDtoByDictType(dataEntity.getConfigDictType());
                if (!CollectionUtils.isEmpty(configDataValuesDto)) {
                    configDataDto.setConfigValues(configDataValuesDto);
                    if (configDataDto.getConfigValue() == null) {
                        configDataDto.setConfigValue(configDataDto.getConfigValues().get(0).getValue());
                    }else{
                        String currentConfigValue = String.valueOf(configDataDto.getConfigValue());
                        if ("true".equalsIgnoreCase(currentConfigValue) || "false".equalsIgnoreCase(currentConfigValue)) {
                            configDataDto.setConfigValue(currentConfigValue.toLowerCase());
                        }
                    }
                }
                configDataDtoList.add(configDataDto);
            }
            deviceSeriesSubtypeConfigsDto.setConfigItems(configDataDtoList);
        }
        // 设置children关联的配置项、当前值以及取值范围
        List<DeviceSeriesSubtypeConfigsDto> children = deviceSeriesSubtypeConfigsDto.getChildren();
        if (children != null && !children.isEmpty()) {
            children.forEach(item ->
                    configsData(item, configsValue)
            );
        }
    }

    private String getCurrentConfigValue(Map<String, Map<String, String>> configsValue, DeviceSubtypeSeriesConfigDictionaryDataEntity config) {
        if (CollectionUtils.isEmpty(configsValue) || config == null) {
            return null;
        }
        return configsValue.get(config.getClientConfigName()).get(config.getConfigName());
    }
}
