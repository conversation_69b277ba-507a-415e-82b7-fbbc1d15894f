package com.xylink.configserver.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xylink.configserver.data.model.NemoDefaultPlatformConfig;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020-05-06 17:54
 */
public interface PlatformConfigService extends IService<NemoDefaultPlatformConfig> {

    /**
     * 保存配置
     *
     * @param request
     * @return
     */
    NemoDefaultPlatformConfig updateConfig(NemoDefaultPlatformConfig request);

    /**
     * 查询指定条件记录
     *
     * @param configPlatform
     * @param configName
     * @param clientConfigName
     * @param configType
     * @return
     */
    NemoDefaultPlatformConfig getUniqPlatformConfig(String configPlatform, String configName, String clientConfigName, int configType);

    /**
     * 条件查询
     *
     * @param configPlatform
     * @param configType
     * @return
     */
    List<NemoDefaultPlatformConfig> getByPlatformAndConfigType(String configPlatform, int configType);

    /**
     * 添加配置项
     *
     * @param configType
     * @param configPlatform
     * @param targetConfigs
     */
    void addPlatformConfig(String configPlatform, int configType, Map<String, Map<String, String>> targetConfigs);

}
