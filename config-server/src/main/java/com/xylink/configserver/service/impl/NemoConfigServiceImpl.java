package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.enums.Configs;
import com.xylink.configserver.enums.DeviceType;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.service.*;
import com.xylink.configserver.util.Media2AudioConfigUtil;
import com.xylink.configserver.util.RestApiContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.support.RequestContextUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;

@Slf4j
@Service
public class NemoConfigServiceImpl implements NemoConfigService {

    private static final ObjectMapper jsonMapper = new ObjectMapper();

    @Autowired
    DeviceConfigService deviceConfigService;

    @Autowired
    private NemoConfigHelper helper;

    @Autowired
    OpenNemoService openNemoService;

    @Autowired
    CustomizeFeatureService customizeFeatureService;

    @Autowired
    DeviceService deviceService;

    @Autowired
    PresenceService presenceService;

    @Autowired
    FeatureListStore featureListStore;

    @Autowired
    NotificationService notificationService;
    @Autowired
    OceanCollectionService oceanCollectionService;

    @Autowired
    RechargeConfigService rechargeConfigService;

    private static final ThreadFactory defaultThreadFactory = new ThreadFactoryBuilder().setNameFormat("NemoConfigServiceImpl-%d").build();


    ExecutorService deviceMessageExecutor = new ThreadPoolExecutor(2,
            10,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100),
            defaultThreadFactory,
            new ThreadPoolExecutor.CallerRunsPolicy()
    );
    @Override
    public List<RestNemoConfig> getExternalNemoConfigs(long nemoId, String hardVersion, String softVersion, String os, String model, HttpServletRequest request) {
        DeviceInfo deviceInfo = getDeviceInfo(hardVersion, softVersion, os, model, request);
        return getNemoConfigs(deviceInfo,nemoId);
    }

    @Override
    public List<RestNemoConfig> getInternalNemoConfigs(long nemoId) {
        UserDevice po = deviceService.getUserDeviceByNemoId(nemoId);
        DeviceInfo deviceInfo = deviceService.getPresenceDeviceInfo(po);
        return getNemoConfigs(deviceInfo,nemoId);
    }

    @Override
    public List<RestNemoConfig> getInternalNemoCallConfigs(long nemoId) {
        return getNemoCallConfigs(nemoId);
    }

    private List<RestNemoConfig> getNemoCallConfigs(long nemoId) {
        List<RestNemoConfig> ret = new ArrayList<>();
        Map<String, String> config = deviceConfigService.getCombinedCallConfig(nemoId);
        if(config != null) {
            for(Map.Entry<String, String> entry : config.entrySet()) {
                ret.add(new RestNemoConfig(entry.getKey(),entry.getValue()));

            }
        }
        return ret;
    }

    @Override
    public List<RestNemoConfig> getNemoConfigs(DeviceInfo deviceInfo,long nemoId){
        List<RestNemoConfig> ret = new ArrayList<>();
        Map<String, String> config = deviceConfigService.getCombinedConfig(nemoId);
        if(deviceInfo != null) {
            List<FeatureProvision> features = featureListStore.getDeviceFeatureProvision(deviceInfo);
            if(features != null) {
                for(FeatureProvision feature : features) {
                    String value = String.valueOf(feature.isEnable());
                    String name = feature.getFeatureName();
                    if(config != null && config.containsKey(name)){
                        value = config.get(name);
                        config.remove(name);
                    }
                    feature.setValue(value);
                    RestNemoConfig restConfig = helper.process(feature, deviceInfo);
                    if(restConfig != null) {
                        ret.add(restConfig);
                    }
                }
            }
        }
        if(config != null) {
            for(Map.Entry<String, String> entry : config.entrySet()) {
                RestNemoConfig restConfig = helper.process(entry.getKey(), entry.getValue(), deviceInfo);
                if(restConfig != null) {
                    ret.add(restConfig);
                }
            }
        }
        String openNemoConfig = openNemoService.getOpenNemoConfig(nemoId);
        if(openNemoConfig != null) {
            ret.add(new RestNemoConfig(Configs.NemoConfig.OPEN_NEMO_CONFIG, openNemoConfig, false));
        }

        List<RestNemoConfig> thirdAppFeatureConfigs = getThirdAppFeatureConfigs();
        if(thirdAppFeatureConfigs != null) {
            for(RestNemoConfig restNemoConfig : thirdAppFeatureConfigs) {
                ret.add(restNemoConfig);
            }
        }
        return ret;
    }

    @Override
    public String getNemoConfigByName(long nemoId, String configName) {
        UserDevice po = deviceService.getUserDeviceByNemoId(nemoId);
        if (null == po){
            throw new ServiceException("userdevice not exist", ErrorStatus.INVALID_PARAMETER);
        }
        DeviceInfo device = deviceService.getPresenceDeviceInfo(po);
        return getNemoConfig(device, nemoId, configName);
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void updateNemoConfig(RestNemoConfig[] configs,long nemoId) throws ServiceException{
        if(configs.length == 1 && Configs.NemoConfig.TIME_AND_CITY.equalsIgnoreCase(configs[0].getName())) {
            applyTimeAndCityConfig(configs, nemoId);
        } else if(configs.length == 1 && Configs.NemoConfig.NEMO_RELATED_USER_ID.equalsIgnoreCase(configs[0].getName())) {
            updateNemoUserRelationConfig(configs[0].getValue(), nemoId);
        } else if(configs.length == 1 && Configs.NemoConfig.NEMONUMBER_CALL_PASSWORD.equalsIgnoreCase(configs[0].getName())) {
            updateNemoNumberCallPassword(configs[0].getValue(), nemoId);
        } else {
            applyNemoConfig(configs, nemoId);
        }
        deviceMessageExecutor.execute(()->{
            UserDevice device = deviceService.getUserDeviceByNemoId(nemoId);
            oceanCollectionService.deviceConfigUpdate(device);
        });
    }


    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void updateInternalNemoConfig(RestNemoConfig[] configs, long nemoId) throws ServiceException{
        applyNemoConfig(configs, nemoId);
        notificationService.notifyChangeNemoConfig(configs,nemoId);
        deviceMessageExecutor.execute(()->{
            UserDevice device = deviceService.getUserDeviceByNemoId(nemoId);
            oceanCollectionService.deviceConfigUpdate(device);
        });
    }


    private void updateNemoNumberCallPassword(String password, long nemoId) {
        if(StringUtils.isNotBlank(password)) {
            password = password.trim();
        }
        applyNemoConfig(new RestNemoConfig[]{new RestNemoConfig(Configs.NemoConfig.NEMONUMBER_CALL_PASSWORD, password, false)}, nemoId);
    }

    private void updateNemoUserRelationConfig(String configValue, long nemoId) {
        if(StringUtils.isEmpty(configValue)) {
            deviceConfigService.deleteUserNemoRelation(nemoId);
        } else {
            deviceConfigService.addUserNemoRelation(Long.parseLong(configValue), nemoId);
        }
        applyNemoConfig(new RestNemoConfig[]{new RestNemoConfig(Configs.NemoConfig.NEMO_RELATED_USER_ID, configValue, false)}, nemoId);
    }

    private void applyTimeAndCityConfig(RestNemoConfig[] configs, long nemoId) {
        log.debug("Apply time and city config for: " + nemoId + " value is: " + configs[0].getValue());
        NemoTimeCity oldConfig = null;
        try {
            String oldStr = deviceConfigService.getNemoConfig(nemoId, Configs.NemoConfig.TIME_AND_CITY);
            if(oldStr != null) {
                oldConfig = jsonMapper.readValue(oldStr, NemoTimeCity.class);
            }
        }catch(Exception e) {
            log.error("Failed to get or parse old config.", e);
        }

        if(oldConfig == null || oldConfig.isAuto()) {
            //auto config, apply the config from nemo, or ignore it
            this.applyNemoConfig(configs, nemoId);
        } else {
            log.info("Old config is set to manual, don't need to update it. ");
        }
    }

    private void applyNemoConfig(RestNemoConfig[] configs, long nemoId) throws ServiceException {
        if(configs != null) {
            try {
                log.debug("Apply nemo config change for nemo:{}", nemoId);
                Map<String,String> device4kConfigs = new HashMap<>();
                for(RestNemoConfig config : configs) {
                    log.debug("Apply config:" ,config);
                    if(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION.equals(config.getName())) {
                        applyJsonValueConfig(config.getValue(), nemoId, Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION);
                    } else if(Configs.configsWithClientName.contains(config.getName())) {
                        applyJsonValueConfig(config.getValue(), nemoId, config.getName());
                    }else if(Configs.DEVICE_4K_CONFIG_NAME.contains(config.getName())){
                        device4kConfigs.put(config.getName(),config.getValue());
                    }  else {
                        checkAllowStranger4OpenNemo(nemoId, config);
                        deviceConfigService.applyNemoConfig(nemoId, config.getName(),
                                config.getValue(), Long.MAX_VALUE, null);
                    }
                }
                if(device4kConfigs.size() > 0){
                    UserDevice userDevicePO = deviceService.getUserDeviceByNemoId(nemoId);
                    if(DeviceType.isHardDevice(userDevicePO.getType())){
                        boolean result = rechargeConfigService.applyDevice4kConfig(userDevicePO,device4kConfigs);
                        if(result){
                            Map<String,String> allConfigs = deviceConfigService.getCombinedConfig(userDevicePO.getId());
                            Map<String,String> computedConfigs = new HashMap<>();
                            for (String jsonConfigName : Configs.DEVICE_4K_RELATED_CONFIG) {
                                if (allConfigs.containsKey(jsonConfigName)) {
                                    computedConfigs.put(jsonConfigName, allConfigs.get(jsonConfigName));
                                }
                            }
                            notificationService.notifyConfigChange(computedConfigs, userDevicePO, DeviceType.isHardDeviceExceptBigEndpoint(userDevicePO.getType()));
                        }
                    }
                }
                log.debug("Send change nemo config events for nemo:{}" ,nemoId);
            } catch(DataAccessException e) {
                log.error("Failed  to save nemo config", e);
                throw new ServiceException("Failed  to save nemo config", ErrorStatus.INTERNAL_DATABASE_ERROR);
            }
        }
    }

    private void checkAllowStranger4OpenNemo(long nemoId, RestNemoConfig config) throws ServiceException {
        if(Configs.NemoConfig.ALLOW_STRANGER.equals(config.getName())
                && "false".equalsIgnoreCase(config.getValue())) {
            String nemoType = deviceConfigService.getNemoConfig(nemoId, Configs.NemoConfig.NEMO_TYPE);
            if(Configs.NemoType.PUBLIC.getValue().equals(nemoType)
                    || Configs.NemoType.EXPERIENCE.getValue().equals(nemoType)) {
                throw new ServiceException("Cannot disable allow stranger for opennemo", ErrorStatus.OPEN_NEMO_CHANGE_ALLOWSTRANGER_DENY);
            }
        }
    }

    public void applyJsonValueConfig(String value, long nemoId, String clientConfigName) {
        try {
            log.debug("Apply ui display config: " + value + " for nemo: " + nemoId);
            Map map = jsonMapper.readValue(value, Map.class);
            String media2AudioClientConfigName = "";
            Set<String> media2AudioConfigNames = null;
            for(Object key : map.keySet()) {
                String configName = (String) key;
                if (Configs.configsWithClientName.contains(clientConfigName) && Media2AudioConfigUtil.syncMedia2AudioConfigName.contains(configName)){
                    media2AudioConfigNames.add(Media2AudioConfigUtil.getMedia2AudioConfig(configName,clientConfigName));
                }
            }
            if (media2AudioConfigNames!=null && media2AudioConfigNames.size()>0){
                if (clientConfigName.equals(Configs.NemoConfig.MEDIA_CONFIG)){
                    media2AudioClientConfigName = Configs.NemoConfig.AUDIO_CONFIG;
                }
                if (clientConfigName.equals(Configs.NemoConfig.AUDIO_CONFIG)){
                    media2AudioClientConfigName = Configs.NemoConfig.MEDIA_CONFIG;
                }

            }

            List<DeviceConfig> deviceConfigPOS = deviceConfigService.getDeviceClientConfigs(nemoId, clientConfigName);
            if (deviceConfigPOS!= null && deviceConfigPOS.size()>0){
                for(DeviceConfig config : deviceConfigPOS) {
                    if(map.containsKey(config.getConfigName())) {
                        deviceConfigService.deleteConfig(config);
                    }
                }
            }
            if (StringUtils.isNotBlank(media2AudioClientConfigName) && media2AudioConfigNames!=null){
                List<DeviceConfig> media2AudioConfigs = deviceConfigService.getDeviceClientConfigs(nemoId, media2AudioClientConfigName);
                if (media2AudioConfigs!= null && media2AudioConfigs.size()>0){
                    for(DeviceConfig config : media2AudioConfigs) {
                        if(media2AudioConfigNames.contains(config.getConfigName())) {
                            deviceConfigService.deleteConfig(config);
                        }
                    }
                }
            }

            for(Object key : map.keySet()) {
                DeviceConfig config = new DeviceConfig(nemoId, (String) key, String.valueOf(map.get(key)),
                        clientConfigName);
                deviceConfigService.addDeviceConfig(config);
            }
            if (StringUtils.isNotBlank(media2AudioClientConfigName) && media2AudioConfigNames!=null){
                for(String  key : media2AudioConfigNames) {
                    DeviceConfig config = new DeviceConfig(nemoId, key, String.valueOf(map.get(key)),
                            media2AudioClientConfigName);
                    deviceConfigService.addDeviceConfig(config);
                }
            }
        } catch (IOException e) {
            log.error("Failed to deserialize value: " + value, e);
        }
    }

    protected String getNemoConfig(DeviceInfo device, long nemoId, String configName) {
        if(Configs.NemoConfig.OPEN_NEMO_CONFIG.equals(configName)) {
            return openNemoService.getOpenNemoConfig(nemoId);
        }
        String configValue = getNemoConfig(nemoId, configName);
        if(device != null) {
            List<FeatureProvision> features = featureListStore.getDeviceFeatureProvision(device);
            if(features != null) {
                for(FeatureProvision feature : features) {
                    String name = feature.getFeatureName();
                    if(name.equals(configName)) {
                        RestNemoConfig restConfig = helper.process(feature, device);
                        if(restConfig != null) {
                            return restConfig.getValue();
                        }
                    }

                }
            }
        }
        RestNemoConfig restConfig = helper.process(configName, configValue, device);
        if(restConfig != null) {
            return restConfig.getValue();
        }
        return configValue;
    }

    private String getNemoConfig(long nemoId, String configName){
        UserDevice userDevice = deviceService.getUserDeviceByNemoId(nemoId);
        return deviceConfigService.getDeviceConfig(userDevice, configName);
    }

    private List<RestNemoConfig> getThirdAppFeatureConfigs() {
        Map<String, List<String>> mapFeatures = new HashMap<>();
        List<RestNemoConfig> ret = new ArrayList<>();
        List<ThirdAppFeature> appFeaturePOList = customizeFeatureService.getAllThirdAppFeatures();
        if(appFeaturePOList != null) {
            for(ThirdAppFeature thirdAppFeaturePO : appFeaturePOList) {
                List<String> list = mapFeatures.get(thirdAppFeaturePO.getFeatureName());
                if(list == null) {
                    list = new ArrayList<>();
                    mapFeatures.put(thirdAppFeaturePO.getFeatureName(), list);
                }
                list.add(thirdAppFeaturePO.getPackageName());
            }
        }
        for (Map.Entry<String, List<String>> entry : mapFeatures.entrySet()) {
            try {
                ret.add(new RestNemoConfig(entry.getKey(), jsonMapper.writeValueAsString(entry.getValue()), false));
            } catch (JsonProcessingException e) {
                log.error("Failed to serialize value for key: " + entry.getKey());
            }
        }
        return ret;
    }

    private DeviceInfo getDeviceInfo(String hardVersion, String softVersion, String os, String model, HttpServletRequest request) {
        UserDevice userDevice = RestApiContext.getCurrentDevice();
        String sn = userDevice.getDeviceSN();
        DeviceInfo device = new DeviceInfo();

        if(hardVersion == null || hardVersion.trim().isEmpty()) {
            device.setHardVersion("1.2.4");
        } else {
            device.setHardVersion(hardVersion);
        }
        device.setSoftVersion(softVersion);
        device.setOS(os);
        device.setModel(model);
        device.setSn(sn);
        Locale locale= RequestContextUtils.getLocale(request);
        device.setLocale(locale.getDisplayName());
        return device;
    }
}
