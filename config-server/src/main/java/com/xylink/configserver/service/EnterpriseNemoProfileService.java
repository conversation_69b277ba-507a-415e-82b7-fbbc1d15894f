package com.xylink.configserver.service;

import com.xylink.configserver.data.model.*;
import com.xylink.configserver.exception.ServiceException;

import java.util.List;
import java.util.Map;

public interface EnterpriseNemoProfileService {

    EnterpriseNemoProfileUpdateReq getEnterpriseProfile(String enterpriseId);

    void removeEnterpriseProfileConfig(EnterpriseNemoProfileUpdateReq req);

    String updateEnterpriseNemoProfileByEnterpriseId(String enterpriseId, EnterpriseNemoProfile profile);

    String updateEnterpriseNemoProfileByProfileId(String profileId, EnterpriseNemoProfile profile) throws ServiceException;

    void applyEnterpriseProfileConfig(List<DeviceConfigUpdate> configs, String profileId);

    void applyEnterpiseProfileConfig(Map<String, String> configs, String profileId);

    void applyEnterpriseProfileFeature(List<EnterpriseNemoFeature> features, String profileId);

    Map<String, String> getEnterpriseConfig(String enterpriseId);

    String updateEnterpriseNemoProfileByFieldUpdate(EnterpriseNemoProfileFieldUpdateReq fieldUpdateReq);

    EnterpriseNemoProfile getByEnterpriseId(String enterpriseId);

    List<EnterpriseNemoConfig> getSimpleConfigsByEnterpriseId(String enterpriseId, String configName);

    EnterpriseNemoProfile getByCustomizedKey(String customizedKey);


}
