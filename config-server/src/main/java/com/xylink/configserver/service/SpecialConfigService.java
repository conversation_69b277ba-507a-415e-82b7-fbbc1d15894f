package com.xylink.configserver.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xylink.configserver.data.model.SpecialConfig;

import java.util.List;

public interface SpecialConfigService  extends IService<SpecialConfig> {

    List<SpecialConfig> getSpecialConfig(String deviceSN);

    List<SpecialConfig> getSpecialConfigByUserId(long userProfileId);

    List<SpecialConfig> getSpecialClientConfig(String deviceSN, String clientConfigName);

    SpecialConfig getDeviceSpecialConfig(String nemoSN, String configName);

    CharSequence getSpecialConfigByNameAndClient(String deviceSN, String configName, String clientConfigName);

    List<SpecialConfig> getSpecialConfigsByFeatureById(String featureId);
}
