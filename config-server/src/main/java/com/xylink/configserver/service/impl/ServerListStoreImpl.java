package com.xylink.configserver.service.impl;

import com.xylink.configserver.service.BaseServerListStore;
import com.xylink.configserver.service.ServerListStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ResourceUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.net.URL;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service("serverListStore")
public class ServerListStoreImpl extends BaseServerListStore implements ServerListStore {

    private volatile AtomicReference<Map<String,Object>> serversInfoSubject = new AtomicReference<>();

    @PostConstruct
    void parseServerList(){

        InputStream serversInfoFile = this.getClass().getClassLoader().getResourceAsStream("servers_info_en.json");
        serversInfoSubject.set(processServerList(serversInfoFile));
    }

    @Override
    public Map<String, Object> getEnterpriseServerInfo() {
        return serversInfoSubject.get();
    }


}
