package com.xylink.configserver.service.deviceseries;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xylink.configserver.data.model.deviceseries.DeviceSeriesSubtypeConfigsDto;
import com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigDictionaryEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-14 22:43
 */
public interface DeviceSubtypeSeriesConfigDictionaryService extends IService<DeviceSubtypeSeriesConfigDictionaryEntity> {
    /**
     * 获取系列支持的配置 dictionaryType = {@link com.xylink.configserver.data.model.deviceseries.DeviceGroupEnum#SERIES}
     *
     * @return
     */
    List<DeviceSubtypeSeriesConfigDictionaryEntity> getSeriesConfigDictionary();

    /**
     * 获取终端支持的配置 dictionaryType = {@link com.xylink.configserver.data.model.deviceseries.DeviceGroupEnum#SUBTYPE}
     *
     * @return
     */
    List<DeviceSubtypeSeriesConfigDictionaryEntity> getSubtypeConfigDictionary();

    /**
     * 获取系列支持的配置 树形结构
     *
     * @return
     */
    List<DeviceSeriesSubtypeConfigsDto> getTreeSeriesConfigDictionary();

}
