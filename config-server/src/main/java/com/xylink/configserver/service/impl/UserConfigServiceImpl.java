package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.xylink.configserver.data.bo.department.DepartmentBO;
import com.xylink.configserver.data.bo.department.UidLevelMainDept;
import com.xylink.configserver.data.bo.department.UserDeviceDeptCascadeMainDeptBO;
import com.xylink.configserver.data.exception.RestException;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.data.model.specialfeature.SpecialFeatureConfigEntity;
import com.xylink.configserver.data.model.specialfeature.SpecialFeatureUserEntity;
import com.xylink.configserver.enums.Configs;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.mapper.UserConfigMapper;
import com.xylink.configserver.mapper.UserDeviceMapper;
import com.xylink.configserver.mapper.UserProfileMapper;
import com.xylink.configserver.mapper.specialfeatue.SpecialFeatureConfigMapper;
import com.xylink.configserver.mapper.specialfeatue.SpecialFeatureUserMapper;
import com.xylink.configserver.proxy.InternalApiProxy;
import com.xylink.configserver.service.*;
import com.xylink.configserver.service.common.CommonVariableService;
import com.xylink.configserver.util.ConfigUtils;
import com.xylink.configserver.util.Jackson;
import com.xylink.configserver.util.RestApiContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Slf4j
@Service
public class UserConfigServiceImpl extends ServiceImpl<UserConfigMapper, UserConfig> implements UserConfigService {

    @Autowired
    UserDeviceMapper userDeviceMapper;

    @Autowired
    DefaultConfigService defaultConfigService;

    @Autowired
    EnterpriseNemoConfigService enterpriseNemoConfigService;

    @Autowired
    SpecialConfigService specialConfigService;

    @Autowired
    BrandConfigService brandConfigService;

    @Autowired
    UserConfigMapper userConfigMapper;

    @Autowired
    FeatureListStore featureListStore;

    @Autowired
    PresenceService presenceService;

    @Autowired
    PlatformConfigService platformConfigService;

    @Autowired
    private UserProfileMapper userProfileMapper;

    @Autowired
    private SpecialFeatureConfigMapper specialFeatureConfigMapper;
    @Autowired
    private CommonVariableService commonVariableService;

    @Autowired
    private DeptConfigService deptConfigService;

    @Autowired
    private SpecialFeatureUserMapper specialFeatureUserMapper;

    @Autowired
    private InternalApiProxy internalApiProxy;

    private final ConfigValueHandleService configValueHandleService;

    private static final ObjectMapper jsonMapper = new ObjectMapper();

    public UserConfigServiceImpl(ConfigValueHandleService configValueHandleService) {
        this.configValueHandleService = configValueHandleService;
    }

    @Override
    public Map<String, String> getCombinedConfig(long userId, Integer deviceType) {
        Map<String, String> combinedConfig = new HashMap<>();
        Integer type = deviceType;
        if (null == deviceType) {
            UserDevice userDevice;
            try {
                userDevice = RestApiContext.getCurrentDevice();
            } catch (Exception e) {
                throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER.getErrorCode(), ErrorStatus.INVALID_PARAMETER.getResId());
            }
            type = userDevice.getType();
        }

        Map<String, Map<String, String>> allConfigs = getAllConfigs(userId, type);

        return getCombinedConfig(combinedConfig, allConfigs);
    }

    private Map<String, String> getCombinedConfig(Map<String, String> combinedConfig, Map<String, Map<String, String>> allConfigs) {
        for (String clientConfigName : Configs.configsWithClientName) {
            Map<String, String> clientConfigs = allConfigs.get(clientConfigName);
            if (clientConfigs != null) {
                try {
                    combinedConfig.put(clientConfigName, configValueHandleService.transferToJson(configValueHandleService.parseConfigs(clientConfigs, clientConfigName)));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        Map<String, String> commonConfigs = allConfigs.get(DeviceConfig.COMMON_CONFIG_KEY);
        if (MapUtils.isNotEmpty(commonConfigs)) {
            combinedConfig.putAll(commonConfigs);
        }
        List<FeatureProvision> featureProvisionList = getConfigedFeatures();
        if (featureProvisionList != null) {
            for (FeatureProvision featureProvision : featureProvisionList) {
                if (!combinedConfig.containsKey(featureProvision.getFeatureName())) {
                    combinedConfig.put(featureProvision.getFeatureName(), featureProvision.getValue());
                }
            }
        }
        return combinedConfig;
    }

    @Override
    public String getUserConfig(long userId, String configName) {
        Map<String, String> combinedConfig = getCombinedConfig(userId, null);
        return combinedConfig.get(configName);
    }

    @Override
    public Map<String, Map<String, String>> getAllConfigs(long userProfileId, int deviceType) {
        Map<String, Map<String, String>> allConfigs = new MyHashMap<>();
        addDefaultConfig(deviceType, allConfigs);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addDefaultVersionConfig(deviceType, allConfigs);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addEnterpriseDefaultConfig(userProfileId, deviceType, allConfigs);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addDeptConfig(userProfileId, allConfigs);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addSpecialDeptConfig(userProfileId, allConfigs);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addSpecialUserConfig(userProfileId, allConfigs);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addPlatformConfig(deviceType, allConfigs);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addBrandModelConfig(deviceType, allConfigs);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addUserConfig(userProfileId, deviceType, allConfigs);
        configValueHandleService.removeErrorConfigs(allConfigs);

        return allConfigs;
    }

    private void addDeptConfig(long userProfileId, Map<String, Map<String, String>> allConfigs) {
        if (!commonVariableService.isEnableDeptConfig()) {
            return;
        }
        String enterpriseId = userDeviceMapper.getUserProfileEnterpriseByUserId(userProfileId);
        String deptId = internalApiProxy.getOneLevelDeptByUserProfileIdFromContact(enterpriseId, userProfileId);
        List<DeptConfigPO> list = deptConfigService.getSourceDeptConfig(enterpriseId, deptId);
        for (DeptConfigPO deptConfigItem : list) {
            deptConfigItem.addToConfigs(allConfigs);
        }
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void applyUserConfig(long userId, Map<String, String> config, int deviceType)
            throws ServiceException {
        log.debug("Update user configuration for user: " + userId);
        try {
            for (Map.Entry<String, String> e : config.entrySet()) {
                if (Configs.configsWithClientName.contains(e.getKey())) {
                    applyJsonValueConfig(e.getKey(), e.getValue(), userId, deviceType);
                } else {
                    applyUserConfig(userId, e.getKey(), e.getValue(),
                            Long.MAX_VALUE, null, deviceType);
                }
            }
        } catch (DataAccessException e) {
            log.error("Failed to update user config", e);
            throw new ServiceException("Failed to update user config.", ErrorStatus.INTERNAL_DATABASE_ERROR);
        }
    }

    private void applyJsonValueConfig(String configName, String configValue, long userId, int deviceType) throws DataAccessException {
        Map configs = new HashMap<>();
        try {
            configs = Jackson.getObjectMapper().readValue(configValue, Map.class);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        for (Object key : configs.keySet()) {
            applyUserConfig(userId, String.valueOf(key),
                    String.valueOf(configs.get(key)), Long.MAX_VALUE, configName, deviceType);
        }
    }

    public void applyUserConfig(long userId, String configName, String configValue, long expireTime,
                                String clientConfigName, int deviceType) throws DataAccessException {
        List<UserConfig> configPOS = userConfigMapper.getUserConfigs(userId, configName, deviceType, clientConfigName);
        UserConfig userConfigPO = configPOS.size() > 0 ? configPOS.get(0) : null;
        if (userConfigPO == null) {
            userConfigPO = new UserConfig(configName, configValue, clientConfigName, deviceType, userId);
        }
        userConfigPO.setConfigValue(configValue);

        // 配置校验
        List<UserConfig> userConfigList = new ArrayList<>(1);
        userConfigList.add(userConfigPO);
        configValueHandleService.verifyConfig(userConfigList);

        Long id = userConfigPO.getId();
        if (id == null || id < 1) {
            userConfigMapper.addUserConfig(userConfigPO);
        } else {
            userConfigMapper.updateUserConfig(userConfigPO);
        }
    }


    private List<FeatureProvision> getConfigedFeatures() {
        String manufacture = null;
        String model;
        AppDetailInfo appInfo = RestApiContext.getCurrentClientInfo();
        if (appInfo == null) {
            UserDevice device = RestApiContext.getCurrentDevice();
            model = presenceService.getAppInfo(device.getId(), AppDetailInfo.REDIS_KEY_MODEL);
        } else {
            manufacture = appInfo.getMf();
            model = appInfo.getMo();
        }
        return featureListStore.getAppFeatureProvision(manufacture, model);
    }

    private void addUserConfig(long userProfileId, int deviceType, Map<String, Map<String, String>> allConfigs) {
        try {
            List<UserConfig> userConfigPOS = userConfigMapper.getUserConfig(userProfileId, deviceType);
            DeviceConfig.configPOs2All(userConfigPOS, allConfigs);
        } catch (DataAccessException e) {
            log.error("Failed to get user configs.", e);
        }

    }

    private void addBrandModelConfig(int deviceType, Map<String, Map<String, String>> allConfigs) {
        if (RestApiContext.getCurrentClientInfo() != null && StringUtils.isNotBlank(RestApiContext.getCurrentClientInfo().getMf())) {
            List<NemoDefaultBrandConfig> brandDefaultConfigPOS = brandConfigService.getDefaultBrandConfigBytypeAndBrand(RestApiContext.getCurrentClientInfo().getMf(), deviceType, NemoDefaultBrandConfig.DEFAULT_BRAND_MODEL_KEY);
            if (brandDefaultConfigPOS != null && brandDefaultConfigPOS.size() > 0) {
                DeviceConfig.configPOs2All(brandDefaultConfigPOS, allConfigs);
            }
            if (StringUtils.isNotBlank(RestApiContext.getCurrentClientInfo().getMo())) {
                List<NemoDefaultBrandConfig> brandModelConfigPOS = brandConfigService.getDefaultBrandConfigBytypeAndBrand(RestApiContext.getCurrentClientInfo().getMf(), deviceType, RestApiContext.getCurrentClientInfo().getMo());
                if (brandModelConfigPOS != null && brandModelConfigPOS.size() > 0) {
                    DeviceConfig.configPOs2All(brandModelConfigPOS, allConfigs);
                }
            }
        }
    }

    /**
     * get platform config [libra_default_platform_config]
     */
    private void addPlatformConfig(int deviceType, Map<String, Map<String, String>> allConfigs) {
        AppDetailInfo currentClientInfo = RestApiContext.getCurrentClientInfo();
        if (currentClientInfo != null && StringUtils.isNotBlank(currentClientInfo.getPl())) {
            platformConfigService.addPlatformConfig(currentClientInfo.getPl(), deviceType, allConfigs);
        }
    }

    private void addDefaultConfig(int deviceType, Map<String, Map<String, String>> allConfigs) {
        ConfigUtils.putAll(allConfigs, defaultConfigService.getDefaultConfigByType(deviceType, -1));
    }

    private void addEnterpriseDefaultConfig(long userProfileId, int deviceType, Map<String, Map<String, String>> allConfigs) {
        String enterpriseId = userDeviceMapper.getUserProfileEnterpriseByUserId(userProfileId);
        if (StringUtils.isNotBlank(enterpriseId)) {
            ConfigUtils.putAll(allConfigs, defaultConfigService.getEnterpriseConfig(deviceType, -1));
            List<EnterpriseNemoConfig> enterpriseNemoConfigPOS =
                    enterpriseNemoConfigService.getEnterpriseDeviceConfigByEnterpriseId(enterpriseId, deviceType);
            DeviceConfig.configPOs2All(enterpriseNemoConfigPOS, allConfigs);
        }
    }

    private void addSpecialConfig(long userProfileId, Map<String, Map<String, String>> allConfigs) {
        List<SpecialConfig> specialConfigPOS = specialConfigService.getSpecialConfigByUserId(userProfileId);
        DeviceConfig.configPOs2All(specialConfigPOS, allConfigs);
    }

    private void addSpecialDeptConfig(long userProfileId, Map<String, Map<String, String>> allConfigs) {
        // fetch cascade root dept info by contact callback
        UserProfileModel user = userProfileMapper.selectById(userProfileId);
        if (user == null) {
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, "用户不存在");
        }
        String enterpriseId = user.getEnterpriseId();
        if (StringUtils.isBlank(enterpriseId)) {
            log.warn("invalid enterpriseId");
            return;
        }

        //  complete timeout and hystrix strategy
        UserDeviceDeptCascadeMainDeptBO mainDept = internalApiProxy.getMainDept(userProfileId, -1, enterpriseId);
        if (mainDept == null) {
            log.warn("empty main dept info");
            return;
        }
        List<UidLevelMainDept> userDepts = mainDept.getUser();
        if (CollectionUtils.isEmpty(userDepts)) {
            return;
        }
        UidLevelMainDept userDept = userDepts.get(0);
        if (userDept == null) {
            return;
        }
        List<DepartmentBO> depts = userDept.getDepartmentAndLevels();
        if (CollectionUtils.isEmpty(depts)) {
            return;
        }

        // sorted it in order to find the nearest main department
        List<Long> deptIds = Lists.newArrayListWithExpectedSize(depts.size());
        depts.stream().sorted(Comparator.comparingInt(DepartmentBO::getLevel)).forEach((e) -> {
            Long deptId = e.getDeptId();
            deptIds.add(deptId);
        });

        // mapping config to dept`s id
        Map<Long, Map<String, Map<String, String>>> mapping = new HashMap<>(deptIds.size());
        List<SpecialFeatureUserEntity> featureUserEntities =
                specialFeatureUserMapper.selectDeptFeature(deptIds);
        if (CollectionUtils.isEmpty(featureUserEntities)) {
            return;
        }
        for (SpecialFeatureUserEntity featureUserEntity : featureUserEntities) {
            if (featureUserEntity == null || StringUtils.isBlank(featureUserEntity.getFeatureId())) {
                continue;
            }

            String featureId = featureUserEntity.getFeatureId();
            Long deptId = featureUserEntity.getUserProfileId();
            //List<SpecialFeatureConfigEntity> configEntities =
            //        specialFeatureConfigMapper.getSpecialConfigsByFeatureById(featureId);
            List<SpecialFeatureConfigEntity> configEntities =
                    specialFeatureConfigMapper.selectList(new LambdaQueryWrapper<SpecialFeatureConfigEntity>().eq(SpecialFeatureConfigEntity::getSpecialFeatureId, featureId));

            for (SpecialFeatureConfigEntity configEntity : configEntities) {
                String clientConfigName = configEntity.getClientConfigName();
                String configName = configEntity.getConfigName();
                String configValue = configEntity.getConfigValue();

                mapping.putIfAbsent(deptId, new HashMap<>());
                Map<String, Map<String, String>> innerConfig = mapping.get(deptId);
                innerConfig.putIfAbsent(clientConfigName, new HashMap<>());
                innerConfig.get(clientConfigName).put(configName, configValue);
            }
        }

        // Warn: there is a performance problem: if dept config is belonged to a common function,
        //      we need to traversal all config from high to low priority due to incomplete config items.
        for (Long deptId : deptIds) {
            ConfigUtils.putAll(allConfigs, mapping.get(deptId));
        }
    }

    private void addSpecialUserConfig(long userProfileId, Map<String, Map<String, String>> allConfigs) {
        List<SpecialConfig> specialFeatureConfigEntityPOS = specialConfigService.getSpecialConfigByUserId(userProfileId);
        DeviceConfig.configPOs2All(specialFeatureConfigEntityPOS, allConfigs);
    }

    /**
     * 版本维度配置
     */
    private void addDefaultVersionConfig(int deviceType, Map<String, Map<String, String>> allConfigs) {
        AppDetailInfo currentClientInfo = RestApiContext.getCurrentClientInfo();
        if (currentClientInfo != null && StringUtils.isNotBlank(currentClientInfo.getAv())) {
            try {
                int av = Integer.parseInt(currentClientInfo.getAv());
                List<NemoDefaultVersionConfig> defaultVersionConfigs = defaultConfigService.getDefaultVersionConfigByTypeAndVersion(deviceType, av);
                if (!CollectionUtils.isEmpty(defaultVersionConfigs)) {
                    defaultVersionConfigs.forEach((item) -> {
                                log.info("add default version config : " + item.toString());
                                item.addToConfigs(allConfigs);
                            }
                    );
                }
            } catch (NumberFormatException e) {
                log.error("NumberFormatException for {}", currentClientInfo.getAv(), e);
            }
        }
    }

    private static class MyHashMap<K, V> extends HashMap<K, V> {
        @Override
        public void putAll(Map<? extends K, ? extends V> m) {
            if (m != null) {
                super.putAll(m);
            }
        }
    }
}
