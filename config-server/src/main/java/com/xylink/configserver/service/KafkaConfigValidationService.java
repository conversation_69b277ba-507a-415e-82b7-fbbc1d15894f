package com.xylink.configserver.service;

import com.xylink.configserver.util.KafkaConnectionTestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Kafka配置验证服务
 * 用于验证Kafka配置的正确性和连接状态
 * 
 * <AUTHOR>
 * @since 2025-07-16
 */
@Slf4j
@Service
public class KafkaConfigValidationService {

    @Value("${spring.kafka.bootstrap.servers}")
    private String bootstrapServers;
    
    @Value("${spring.kafka.security.protocol:}")
    private String securityProtocol;
    
    @Value("${spring.kafka.security.sasl.mechanism:}")
    private String securitySaslMechanism;
    
    @Value("${spring.kafka.security.sasl.config:}")
    private String securityJaasConfig;

    /**
     * 验证Kafka配置并测试连接
     * 
     * @return 验证结果
     */
    public KafkaValidationResult validateConfiguration() {
        log.info("开始验证Kafka配置...");
        
        KafkaValidationResult result = new KafkaValidationResult();
        
        // 1. 验证基础配置
        if (bootstrapServers == null || bootstrapServers.trim().isEmpty()) {
            result.setValid(false);
            result.setErrorMessage("Kafka bootstrap.servers配置为空");
            return result;
        }
        
        // 2. 验证服务器地址格式
        if (!isValidBootstrapServers(bootstrapServers)) {
            result.setValid(false);
            result.setErrorMessage("Kafka bootstrap.servers格式不正确: " + bootstrapServers);
            return result;
        }
        
        // 3. 测试连接
        boolean connectionSuccess = KafkaConnectionTestUtil.testConnection(
            bootstrapServers, securityProtocol, securitySaslMechanism, securityJaasConfig, 10);
        
        if (connectionSuccess) {
            result.setValid(true);
            result.setMessage("Kafka配置验证成功，连接正常");
            log.info("Kafka配置验证成功");
        } else {
            result.setValid(false);
            result.setErrorMessage("Kafka连接测试失败，请检查服务器地址和网络连接");
            log.error("Kafka配置验证失败");
        }
        
        return result;
    }

    /**
     * 验证bootstrap.servers格式
     */
    private boolean isValidBootstrapServers(String servers) {
        if (servers == null || servers.trim().isEmpty()) {
            return false;
        }
        
        String[] serverArray = servers.split(",");
        for (String server : serverArray) {
            server = server.trim();
            if (!server.contains(":")) {
                return false;
            }
            
            String[] parts = server.split(":");
            if (parts.length != 2) {
                return false;
            }
            
            try {
                int port = Integer.parseInt(parts[1]);
                if (port <= 0 || port > 65535) {
                    return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取当前Kafka配置信息
     */
    public String getConfigurationInfo() {
        StringBuilder info = new StringBuilder();
        info.append("Kafka配置信息:\n");
        info.append("Bootstrap Servers: ").append(bootstrapServers).append("\n");
        info.append("Security Protocol: ").append(securityProtocol.isEmpty() ? "未配置" : securityProtocol).append("\n");
        info.append("SASL Mechanism: ").append(securitySaslMechanism.isEmpty() ? "未配置" : securitySaslMechanism).append("\n");
        info.append("JAAS Config: ").append(securityJaasConfig.isEmpty() ? "未配置" : "已配置").append("\n");
        
        return info.toString();
    }

    /**
     * Kafka验证结果类
     */
    public static class KafkaValidationResult {
        private boolean valid;
        private String message;
        private String errorMessage;

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }

        @Override
        public String toString() {
            if (valid) {
                return "验证成功: " + message;
            } else {
                return "验证失败: " + errorMessage;
            }
        }
    }
}
