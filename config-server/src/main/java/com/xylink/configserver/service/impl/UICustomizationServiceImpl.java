package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.xylink.configserver.data.model.UserDevice;
import com.xylink.configserver.enums.Configs;
import com.xylink.configserver.enums.DeviceType;
import com.xylink.configserver.enums.UserPermission;
import com.xylink.configserver.mapper.UserDeviceMapper;
import com.xylink.configserver.proxy.InternalApiProxy;
import com.xylink.configserver.service.*;
import com.xylink.configserver.util.RestApiContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class UICustomizationServiceImpl implements UICustomizationService {

    @Autowired
    UserDeviceMapper userDeviceMapper;

    @Autowired
    UserConfigService userConfigService;

    @Autowired
    DefaultConfigService defaultConfigService;

    @Autowired
    DeviceService deviceService;

    @Autowired
    EnterpriseNemoConfigService enterpriseNemoConfigService;

    @Autowired
    SpecialConfigService specialConfigService;

    @Autowired
    DeviceConfigService deviceConfigService;

    @Autowired
    InternalApiProxy internalApiProxy;

    private JsonMapper jsonMapper = new JsonMapper();

    private final ConfigValueHandleService configValueHandleService;

    private final Map<String, String> permissionToConfig = new HashMap<>();
    private final Map<String, String> defautPermissionConfigMap = new HashMap<>();

    public UICustomizationServiceImpl(ConfigValueHandleService configValueHandleService) {
        this.configValueHandleService = configValueHandleService;
    }

    @PostConstruct
    void init() {
        permissionToConfig.put(UserPermission.CREATE_MEETING.getRoleKey(), Configs.UserConfig.ENABLE_CREATE_MEETING);
        permissionToConfig.put(UserPermission.CREATE_LIVE.getRoleKey(), Configs.UserConfig.ENABLE_CREATE_LIVE);
        permissionToConfig.put(UserPermission.CONTROL_MEETING.getRoleKey(), Configs.UserConfig.SHOW_HOST_MEETING);
        permissionToConfig.put(UserPermission.INVITE_MEETING.getRoleKey(), Configs.UserConfig.SHOW_INVITE_MEETING);
        permissionToConfig.put(UserPermission.SHARED_FOLDER.getRoleKey(), Configs.UserConfig.SHOW_SHARED_FOLDER);
        permissionToConfig.put(UserPermission.ATTENDEE.getRoleKey(), Configs.UserConfig.SHOW_ATTENDEE);
        permissionToConfig.put(UserPermission.RECORDING.getRoleKey(), Configs.UserConfig.SHOW_RECORDING);
        permissionToConfig.put(UserPermission.CLOUD_RECORDING.getRoleKey(), Configs.UserConfig.SHOW_CLOUD_RECORDING);
        permissionToConfig.put(UserPermission.LOCAL_RECORDING.getRoleKey(), Configs.UserConfig.SHOW_LOCAL_RECORDING);

        defautPermissionConfigMap.put(Configs.UserConfig.ENABLE_CREATE_MEETING, "false");
        defautPermissionConfigMap.put(Configs.UserConfig.ENABLE_CREATE_LIVE, "false");
        defautPermissionConfigMap.put(Configs.UserConfig.SHOW_HOST_MEETING, "false");
        defautPermissionConfigMap.put(Configs.UserConfig.SHOW_INVITE_MEETING, "false");
        defautPermissionConfigMap.put(Configs.UserConfig.SHOW_SHARED_FOLDER, "false");
        defautPermissionConfigMap.put(Configs.UserConfig.SHOW_ATTENDEE, "false");
        defautPermissionConfigMap.put(Configs.UserConfig.SHOW_RECORDING, "false");
        defautPermissionConfigMap.put(Configs.UserConfig.SHOW_CLOUD_RECORDING, "false");
        defautPermissionConfigMap.put(Configs.UserConfig.SHOW_LOCAL_RECORDING, "false");
    }

    @Override
    public String getUICustomization(long deviceId) {
        UserDevice device = RestApiContext.getCurrentDevice();
        if (null == device) {
            log.error("getUICustomization deviceId:{}", deviceId);
            return "";
        }
        if (DeviceType.isHardDevice(device.getType())) {
            return deviceConfigService.getDeviceConfig(device, Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION);
        } else {
            return getUICustomization(device.getUserProfileID(), device.getType());
        }
    }

    @Override
    public String getInternalUiCustomization(long deviceId) {
        UserDevice device = deviceService.getUserDeviceByNemoId(deviceId);
        if (null == device) {
            log.error("getUICustomization deviceId:{} error.Case by device not exists.", deviceId);
            return "";
        }
        if (DeviceType.isHardDevice(device.getType())) {
            return deviceConfigService.getDeviceConfig(device, Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION);
        } else {
            return getUICustomization(device.getUserProfileID(), device.getType());
        }
    }

    private String getUICustomization(long userProfileId, int deviceType) {

        Map<String, String> allConfigs = userConfigService.getAllConfigs(userProfileId, deviceType).get(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION);
        if (allConfigs == null) {
            allConfigs = new HashMap<>(10);
        }
        String enterpriseId = userDeviceMapper.getUserProfileEnterpriseByUserId(userProfileId);
        if (StringUtils.isNotBlank(enterpriseId)) {
            allConfigs.putAll(permissionListToUIConfigMap(internalApiProxy.getIauthRoleResource(userProfileId)));
            allConfigs.put(Configs.UserConfig.ENABLE_HR_RECORD, String.valueOf(isEnterpirseAllowHrRecord(enterpriseId)));
        }

        return configValueHandleService.transferToJson(
                configValueHandleService.parseConfigs(allConfigs, Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION));
    }

    private Map<String, String> permissionListToUIConfigMap(List<String> permissions) {
        Map<String, String> configMap = new HashMap<>(defautPermissionConfigMap);
        if(permissions != null) {

            for(String permission : permissions) {
                String configKey = permissionToConfig.get(permission);
                if (StringUtils.isNotBlank(configKey)){
                    configMap.put(configKey, "true");
                }
            }
        }
        return configMap;
    }

    private boolean isEnterpirseAllowHrRecord(String enterpriseId) {
        Map<String, String> configs = internalApiProxy.getEnterpriseConfig(enterpriseId);
        if (configs == null) {
            return false;
        }
        return "true".equalsIgnoreCase(configs.get(Configs.HIGH_RESOLUTION_RECORD));
    }
}
