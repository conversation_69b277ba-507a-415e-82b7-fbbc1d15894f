package com.xylink.configserver.service;

import com.xylink.configserver.data.model.DeviceInfo;
import com.xylink.configserver.data.model.FeatureProvision;
import com.xylink.configserver.data.model.RestNemoConfig;

public interface NemoConfigHelper {

    RestNemoConfig process(String configName, String configValue, DeviceInfo device);

    RestNemoConfig process(FeatureProvision feature, DeviceInfo device);
}
