package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xylink.configserver.data.dto.ExcludeIncludeServerConfigDto;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.enums.DeviceType;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.RestException;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.mapper.EnterpriseServersConfigMapper;
import com.xylink.configserver.mapper.UserDeviceMapper;
import com.xylink.configserver.proxy.InternalApiProxy;
import com.xylink.configserver.service.DeviceService;
import com.xylink.configserver.service.EnterpriseServersConfigService;
import com.xylink.configserver.service.ServerListStore;
import com.xylink.configserver.service.SitePathService;
import com.xylink.configserver.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xylink.configserver.util.Constants.CONNECTION_TEST;
import static com.xylink.configserver.util.Constants.SITE_PATH;
import static com.xylink.configserver.util.RestApiContext.getCurrentDevice;

@Slf4j
@Service
public class EnterpriseServersConfigServiceImpl implements EnterpriseServersConfigService {

    @Autowired
    ServerListStore serverListStore;

    @Autowired
    ServerListStore innerServerListStore;

    @Autowired
    DeviceService deviceService;

    @Value("${common.connectionTestTimeout}")
    long connectionTestTimeout = 3000;

    @Autowired
    InternalApiProxy internalApiProxy;

    @Autowired
    EnterpriseServersConfigMapper enterpriseServersConfigMapper;

    @Autowired
    SitePathService sitePathService;

    @Autowired
    UserDeviceMapper userDeviceMapper;

    private static final String IOS = "IOS";
    private static int DEFAULT_USER_CONFIG_TYPE =1;

    @Override
    public Map<String, Object> getServerConfigs() {
        UserDevice device = getCurrentDevice();
        return getServerConfigs(device,null);
    }

    @Override
    public Map<String, Object> getInternalDeviceServerConfig(long deviceId,String configName) {
        UserDevice device = deviceService.getUserDeviceByNemoId(deviceId);
        RestApiContext.setCurrentDevice(device);
        return getServerConfigs(device,configName);
    }

    @Override
    public Map<String, Object> getInternalUserServerConfig(long userId,int userConfigType,String configName) {
        UserDevice device = new UserDevice();
        if (userConfigType<=0){
            userConfigType = DEFAULT_USER_CONFIG_TYPE;
        }
        device.setUserProfileID(userId);
        device.setType(userConfigType);
        device.setSubType(userConfigType);
        return getServerConfigs(device,configName);
    }

    @Override
    public Map<String, Object> getDefaultServerConfig(String configName) {
        if (StringUtils.isNotBlank(configName)) {
            return getDefaultServerConfigs(configName);
        } else {
            return getDefaultServerConfigs();
        }
    }

    @Override
    public Map<String, Object> getDefaultServerConfigBatchByExcInc(ExcludeIncludeServerConfigDto excludeIncludeServerConfigDto) {

        if (excludeIncludeServerConfigDto == null) {
            return Collections.emptyMap();
        }

        List<String> excludeIncludeServerConfigList = excludeIncludeServerConfigDto.getExcludeConfigNameList();
        List<String> includeIncludeServerConfigList = excludeIncludeServerConfigDto.getIncludeConfigNameList();

        LambdaQueryWrapper<DefaultServerConfig> wrapper = Wrappers.<DefaultServerConfig>lambdaQuery().select();
        if (CollectionUtils.isNotEmpty(includeIncludeServerConfigList)) {
            wrapper.in(DefaultServerConfig::getConfigName, includeIncludeServerConfigList);
        }
        if (CollectionUtils.isNotEmpty(excludeIncludeServerConfigList)) {
            wrapper.notIn(DefaultServerConfig::getConfigName, excludeIncludeServerConfigList);
        }

        List<DefaultServerConfig> defaultServerConfigs = enterpriseServersConfigMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(defaultServerConfigs)) {
            return Collections.emptyMap();
        }
        Map<String, DefaultServerConfig> serverConfigMap = defaultServerConfigs.stream()
                .collect(Collectors.toMap(DefaultServerConfig::getConfigName, Function.identity()));

        return SerializeClientConfigUtil.coverValueType(serverConfigMap);

    }

    @Override
    public Map<String, Object> getDefaultServerConfigs(String configName) {
        Map<String, DefaultServerConfig> serverConfigs = enterpriseServersConfigMapper.getDefaultServerConfigByConfigName(configName);
        return new HashMap<>(SerializeClientConfigUtil.coverValueType(serverConfigs));
    }

    @Override
    public Map<String, Object> getDefaultServerConfigs() {
        Map<String, DefaultServerConfig> serverConfigs = enterpriseServersConfigMapper.getDefaultServerConfig();
        return new HashMap<>(SerializeClientConfigUtil.coverValueType(serverConfigs));
    }

    @Override
    @DS("master")
    public List<DefaultServerConfig> saveOrUpdateBatch(List<DefaultServerConfig> defaultServerConfigList) {

        if (CollectionUtils.isEmpty(defaultServerConfigList)) {
            return Collections.emptyList();
        }

        List<DefaultServerConfig> result = new ArrayList<>(defaultServerConfigList.size());
        defaultServerConfigList.forEach(item -> result.add(saveOrUpdate(item)));

        return result;

    }

    @Override
    @DS("master")
    public DefaultServerConfig saveOrUpdate(DefaultServerConfig defaultServerConfig) {

        String configName = defaultServerConfig.getConfigName();

        if (StringUtils.isEmpty(configName)) {
            throw new ServiceException("configName不能为空!", ErrorStatus.INVALID_PARAMETER);
        }

        DefaultServerConfig foundDefaultServerConfig = enterpriseServersConfigMapper.getDefaultServerConfigPoByConfigName(configName);
        if (foundDefaultServerConfig == null) {
            if (defaultServerConfig.getValueType() == 0) {
                defaultServerConfig.setValueType(1);
            }
            enterpriseServersConfigMapper.insert(defaultServerConfig);
            return defaultServerConfig;
        }
        if (defaultServerConfig.getValueType() > 0) {
            foundDefaultServerConfig.setValueType(defaultServerConfig.getValueType());
        }
        foundDefaultServerConfig.setConfigValue(defaultServerConfig.getConfigValue());
        enterpriseServersConfigMapper.updateById(foundDefaultServerConfig);

        return foundDefaultServerConfig;

    }


    private Map<String, Object> getServerConfigs(UserDevice device, String configName) {
        if (null == device){
            throw new ServiceException("invalid device id.",
                    ErrorStatus.INVALID_DEVICE_ID);
        }
        if(DeviceType.BIG_ENDPOINT_DEVICE.getValue()== device.getType()){
            return getBigEndpointServerConfig(device.getSecurityKey(), device.getDeviceSN());
        }

        SnAndIsNemoAndUserProfileId snAndIsNemoAndUserProfileId =
                getSnAndIsNemoAndUserProfileId(device.getSecurityKey(), device.getDeviceSN(), device.getType(),
                                               device.getUserProfileID());
        if(snAndIsNemoAndUserProfileId == null){
            log.error("snAndIsNemoAndUserProfileId null,securityKey:{}",device.getSecurityKey());
            return null;
        }

        Map<String, DefaultServerConfig> serverConfigs = enterpriseServersConfigMapper.getDefaultServerConfig();

        //软终端，subtype 为device type
        Map<String, DefaultServerConfig> deviceTypeServerConfigs = enterpriseServersConfigMapper.getDeviceTypeServerConfigs(device.getSubType());
        serverConfigs.putAll(deviceTypeServerConfigs);

        Map<String, DefaultServerConfig> enterpriseServerConfigs = getEnterpriseServerConfigs(device);
        if (null != enterpriseServerConfigs){
            serverConfigs.putAll(enterpriseServerConfigs);
        }

        Map<String, DefaultServerConfig> deviceServerConfigs = enterpriseServersConfigMapper.getDeviceServerConfig(device.getId());
        serverConfigs.putAll(deviceServerConfigs);

        Map<String,Object> info = new HashMap<>();
        info.putAll(SerializeClientConfigUtil.coverValueType(serverConfigs));

        if (StringUtils.isNotBlank(snAndIsNemoAndUserProfileId.enterpriseId)){
            Set<Map<String,Object>> connectionUri = getConnectionTestUri(snAndIsNemoAndUserProfileId.enterpriseId);
            if (connectionUri.size() > 0) {
                info.put(Constants.CONNECTION_TEST, connectionUri);
            }
        }
        info.put(SITE_PATH, sitePathService.getOldEnterpriseSitePathByEnterpriseId(snAndIsNemoAndUserProfileId.enterpriseId));
        if (StringUtils.isNotBlank(configName)){
            Map<String,Object> resultWithConfig= new HashMap<>();
            if (Objects.nonNull(info.get(configName))){
                resultWithConfig.put(configName,info.get(configName));

            }
            return resultWithConfig;
        }
        return info;
    }

    private Map<String, DefaultServerConfig> getEnterpriseServerConfigs(UserDevice device) {
        Map<String, DefaultServerConfig> enterpriseServerConfigs = null;
        String enterpriseId = device.getEnterpriseId();
        if (device.getType() == DeviceType.SOFT.getValue()|| device.getType() == DeviceType.DESKTOP.getValue()){
            enterpriseId = userDeviceMapper.getUserProfileEnterpriseByUserId(device.getUserProfileID());
        }
        if (StringUtils.isNotBlank(enterpriseId)) {
            enterpriseServerConfigs = enterpriseServersConfigMapper.getEnterpriseServerConfigs(enterpriseId);

        }
        return enterpriseServerConfigs;
    }


    public void initDefaultServerConfig(){
        ServerListStore serverListStore = (ServerListStore) SpringContextUtil.getBean("serverListStore");
        Map<String, Object> serverconfigs = serverListStore.getEnterpriseServerInfo();
        if (null != serverconfigs) {
            serverconfigs.put("netTestServer",Jackson.writeValueAsString(serverconfigs.get("netTestServer")));
            enterpriseServersConfigMapper.addDefaultServerConfig(serverconfigs);
        }
    }

    protected Map<String, Object> getBigEndpointServerConfig(String securityKey, String sn) {
        Map<String, DefaultServerConfig> serverConfigs = enterpriseServersConfigMapper.getDefaultServerConfig();
        Map<String,Object> info = new HashMap<>();
        info.putAll(SerializeClientConfigUtil.coverValueType(serverConfigs));

        if (StringUtils.isNotEmpty(securityKey)) {
            UserDevice device = getCurrentDevice();
            if (StringUtils.isNotBlank(device.getEnterpriseId())) {
                Set<Map<String,Object>> connectionTestUri = getConnectionTestUri(device.getEnterpriseId());
                if (connectionTestUri.size() > 0) {
                    info.put(CONNECTION_TEST, connectionTestUri);
                }
            }
            return info;
        } else if (StringUtils.isBlank(sn)) {
            ErrorStatus errorStatus = ErrorStatus.INVALID_PARAMETER;
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, errorStatus.getErrorCode(),
                                    errorStatus.getResId());
        } else {
            return info;
        }
    }

    protected Set<Map<String,Object>> getConnectionTestUri(String enterpriseId) {
        Optional<ConnectionTestDto[]> enterpriseConnectionTestUris = internalApiProxy.getEnterpriseConnectionTestUri(enterpriseId);

        Set<Map<String,Object>> result = new HashSet<>();
        // 判断客户端版本 是否需要加accessories :老的IOS增加返回会崩溃
        boolean accessoriesShow = isAccessoriesShow();
        if (enterpriseConnectionTestUris.isPresent()){
            ConnectionTestDto[] data = enterpriseConnectionTestUris.get();
            for (ConnectionTestDto item : data){
                Map<String, Object> map = new HashMap<>();
                map.put("addr",item.getInnerIp());
                map.put("test", new HashMap<String, Object>(){
                    {
                        put("result","ok");
                    }
                });
                map.put("timeout",connectionTestTimeout);
                Map<String, String> accessories = item.getAccessories();
                if (Objects.nonNull(accessories) && !accessories.isEmpty() && accessoriesShow){
                    map.put("accessories", accessories);
                }
                result.add(map);
            }
        }
        return result;
    }

    protected SnAndIsNemoAndUserProfileId getSnAndIsNemoAndUserProfileId(String securityKey, String nemoSn, int deviceType,long userProfileId) {
        SnAndIsNemoAndUserProfileId result;

        if (StringUtils.isNotEmpty(securityKey)) {
            // H323设备 获取server config，时候 security key
            if(DeviceType.GW_H323.getValue()== deviceType) {
                GwDevice gwDevicePO = deviceService.getGwDeviceBySkAndType(securityKey, GwDevice.GwType.H323);
                if (gwDevicePO == null) {
                    return null;
                } else {
                    result = new SnAndIsNemoAndUserProfileId();
                    result.sn = gwDevicePO.getSn();
                    result.isNemo = false;
                    result.enterpriseId = gwDevicePO.getEnterpriseId();
                    result.deviceType = DeviceType.GW_H323.getValue();
                }
            } else {
                UserDevice userDevice = getCurrentDevice();
                if (userDevice == null) {
                    result = null;
                } else {
                    result = new SnAndIsNemoAndUserProfileId();
                    result.sn = userDevice.getDeviceSN();
                    result.isNemo = userDevice.getType() == DeviceType.HARD.getValue();
                    result.userProfileId = userDevice.getUserProfileID();
                    if (!DeviceType.isHardDevice(deviceType)){
                        result.enterpriseId = deviceService.getUserProfileEnterpriseByUserId(userProfileId);
                    }else {
                        result.enterpriseId = userDevice.getEnterpriseId();
                    }
                    result.deviceType = userDevice.getType();
                }
            }
        } else {
            //modify by pengshuai : add internal user device info

            if (DeviceType.isHardDevice(deviceType)){
                if (StringUtils.isEmpty(nemoSn)) {
                    result = null;
                } else {
                    result = new SnAndIsNemoAndUserProfileId();
                    result.sn = DeviceSnParse.parse(nemoSn).getSn();
                    UserDevice userDevice = deviceService.getInUseUserHardDeviceByDevSn(result.sn);

                    if (DeviceType.HARD.getValue() == deviceType && userDevice != null) {
                        deviceService.validateDeviceSn(result.sn, userDevice.getSubType());
                    }
                    result.isNemo = true;
                    result.enterpriseId = deviceService.getInUseUserHardDeviceByDevSn(result.sn).getEnterpriseId();
                }
            }else {
                if (userProfileId<=0){
                    return null;
                }
                UserDevice device = deviceService.getUser2DeviceByUserId(userProfileId);
                if(Objects.isNull(device)){
                    return null;
                }
                result = new SnAndIsNemoAndUserProfileId();
                result.deviceType = deviceType;
                result.userProfileId = device.getUserProfileID();
                result.isNemo = false;
                result.enterpriseId = deviceService.getUserProfileEnterpriseByUserId(userProfileId);
            }

        }
        return result;
    }

    private boolean isAccessoriesShow() {
        boolean accessoriesShow = Boolean.TRUE;
        try {
            AppDetailInfo appDetailInfo = RestApiContext.getCurrentClientInfo();
            if (Objects.nonNull(appDetailInfo) && IOS.equalsIgnoreCase(appDetailInfo.getPl())){
                int av = Integer.parseInt(appDetailInfo.getAv());
                accessoriesShow = av>22810;
            }
        } catch (Exception e) {
            log.error("isAccessoriesShow error.",e);
        }
        return accessoriesShow;
    }
}
