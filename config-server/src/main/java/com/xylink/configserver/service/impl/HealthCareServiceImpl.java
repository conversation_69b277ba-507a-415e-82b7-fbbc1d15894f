package com.xylink.configserver.service.impl;

import com.xylink.configserver.data.enums.UniversalConstant;
import com.xylink.configserver.service.HealthCareService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/23 11:14
 */
@RequiredArgsConstructor
@Service
public class HealthCareServiceImpl implements HealthCareService {

    private final RedisTemplate<String, String> redisConfigTemplate;

    private final RedisTemplate<String, String> redisCraftTemplate;

    @Override
    public List<String> checkRedisState() {

        List<String> resList = new ArrayList<>(4);

        redisConfigTemplate.opsForValue().set(UniversalConstant.REDIS_HEALTH_CATE.getValue(), "config redis healthy:" + new Date());
        resList.add(redisConfigTemplate.opsForValue().get(UniversalConstant.REDIS_HEALTH_CATE.getValue()));

        redisCraftTemplate.opsForValue().set(UniversalConstant.REDIS_HEALTH_CATE.getValue(), "craft redis healthy" + new Date());
        resList.add(redisCraftTemplate.opsForValue().get(UniversalConstant.REDIS_HEALTH_CATE.getValue()));

        return resList;

    }

}
