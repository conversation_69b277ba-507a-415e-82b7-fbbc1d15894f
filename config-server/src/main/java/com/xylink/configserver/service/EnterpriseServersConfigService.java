package com.xylink.configserver.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xylink.configserver.data.dto.ExcludeIncludeServerConfigDto;
import com.xylink.configserver.data.model.DefaultServerConfig;

import java.util.List;
import java.util.Map;

public interface EnterpriseServersConfigService {

    Map<String,Object> getServerConfigs();

    void initDefaultServerConfig();

    Map<String, Object> getInternalDeviceServerConfig(long deviceId,String configName);

    Map<String, Object> getInternalUserServerConfig(long userId,int configType,String configName);

    Map<String, Object> getDefaultServerConfig(String configName);

    Map<String, Object> getDefaultServerConfigBatchByExcInc(ExcludeIncludeServerConfigDto excludeIncludeServerConfigDto);

    /**
     * Get default_server_config data.
     *
     * @param configName
     * @return
     */
    Map<String,Object> getDefaultServerConfigs(String configName);

    /**
     * Get default_server_config data.
     * @return
     */
    Map<String,Object> getDefaultServerConfigs();

    @DS("master")
    List<DefaultServerConfig> saveOrUpdateBatch(List<DefaultServerConfig> defaultServerConfigList);

    /**
     * saveorupdate
     */
    DefaultServerConfig saveOrUpdate(DefaultServerConfig defaultServerConfig);

}
