package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xylink.configserver.data.dto.BaseConfigDto;
import com.xylink.configserver.data.exception.RestException;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.data.vo.config.ConfigInfoVo;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.mapper.BaseDefaultConfigMapper;
import com.xylink.configserver.mapper.DefaultConfigMapper;
import com.xylink.configserver.mapper.EnterpriseNemoConfigMapper;
import com.xylink.configserver.mapper.LibraConfigTypeValueLimitMapper;
import com.xylink.configserver.service.DefaultConfigService;
import com.xylink.configserver.util.ConfigBaseCoverUtil;
import com.xylink.configserver.util.ConfigUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultConfigServiceImpl implements DefaultConfigService {

    int TYPE_NEMO_CONFIG = 2;
    private final AtomicReference<Map<Integer, Map<String, Map<String, String>>>> enterpriseConfig = new AtomicReference<>();
    private final AtomicReference<Map<Integer, Map<String, Map<String, String>>>> defaultConfigsByType = new AtomicReference<>();
    private final AtomicReference<Map<String, Map<String, Map<String, String>>>> defaultNemoConfig = new AtomicReference<>();

    private final DefaultConfigMapper defaultConfigMapper;

    private final ThreadPoolTaskExecutor sharedThreadPoolTaskExecutor;

    private final EnterpriseNemoConfigMapper enterpriseNemoConfigMapper;

    private final LibraConfigTypeValueLimitMapper valueLimitMapper;

    private final BaseDefaultConfigMapper basedefaultConfigMapper;

    @Override
    public Map<String, Map<String, String>> getDefaultNemoConfig(String productFamily, int subType) {
        Map<String, Map<String, String>> configs = new HashMap<>();
        Map<Integer, Map<String, Map<String, String>>> configsByType = defaultConfigsByType.get();
        if (subType > 0 && configsByType.containsKey(subType)) {
            ConfigUtils.putAll(configs, configsByType.get(subType));
        }
        ConfigUtils.putAll(configs, configsByType.get(TYPE_NEMO_CONFIG));
        Map<String, Map<String, Map<String, String>>> nemoConfig = defaultNemoConfig.get();
        if (nemoConfig.containsKey(productFamily)) {
            Map<String, Map<String, String>> familyConfigs = nemoConfig.get(productFamily);
            for (Map.Entry<String, Map<String, String>> entry : familyConfigs.entrySet()) {
                if (!configs.containsKey(entry.getKey())) {
                    configs.put(entry.getKey(), entry.getValue());
                } else {
                    configs.get(entry.getKey()).putAll(entry.getValue());
                }
            }
        }
        return configs;
    }

    @Override
    public List<NemoDefaultConfig> getDefaultNemoConfig(Integer configType, String clientConfigName, String configName) {
        if (configType == null) {
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER.getErrorCode(),
                    ErrorStatus.INVALID_PARAMETER.getResId());
        }
        List<NemoDefaultConfig> nemoDefaultConfigs = defaultConfigMapper.selectListByCondition(configType,
                ConfigUtils.transferCommonConfig(clientConfigName),
                configName);
        List<NemoBaseDefaultConfig> baseNemoDefaultConfigs = basedefaultConfigMapper.selectListByCondition(configType,
                ConfigUtils.transferCommonConfig(clientConfigName),
                configName);
        return handleDatas(nemoDefaultConfigs, baseNemoDefaultConfigs);
    }

    @Override
    public Map<String, Map<String, String>> getDefaultConfigByType(int subType, int type) {
        Map<String, Map<String, String>> configs = new HashMap<>(16);
        Map<Integer, Map<String, Map<String, String>>> configsByType = defaultConfigsByType.get();
        if (MapUtils.isNotEmpty(configsByType)) {
            if (type > 0 && configsByType.containsKey(type)) {
                ConfigUtils.putAll(configs, configsByType.get(type));
            }
            if (subType > 0 && configsByType.containsKey(subType)) {
                ConfigUtils.putAll(configs, configsByType.get(subType));
            }
        }

        return configs;
    }

    @Override
    public Map<String, Map<String, String>> getDefaultConfigByTypeAndConfigName(int subType, int type, List<BaseConfigDto> configDtoList) {

        Map<Integer, Map<String, Map<String, String>>> configsByType = defaultConfigsByType.get();
        if (MapUtils.isEmpty(configsByType)) {
            return Collections.emptyMap();
        }

        Map<String, Map<String, String>> configs = new HashMap<>(16);
        for (BaseConfigDto baseConfigDto : configDtoList) {
            final String baseConfigName = baseConfigDto.getConfigName();
            final String baseClientConfigName = ConfigUtils.transferCommonConfig(baseConfigDto.getClientConfigName());
            boolean existInSubtypeMap = false;

            if (subType > 0 && configsByType.containsKey(subType)) {
                Map<String, Map<String, String>> defaultConfigMapMap = configsByType.get(subType);
                if (MapUtils.isNotEmpty(defaultConfigMapMap) && defaultConfigMapMap.containsKey(baseClientConfigName)) {
                    Map<String, String> defaultConfigMap = defaultConfigMapMap.get(baseClientConfigName);
                    if (MapUtils.isNotEmpty(defaultConfigMap) && defaultConfigMap.containsKey(baseConfigName)) {
                        combineMap(configs, baseClientConfigName, baseConfigName, defaultConfigMap.get(baseConfigName));
                        existInSubtypeMap = true;
                    }
                }
            }

            if (!existInSubtypeMap) {
                if (type > 0 && configsByType.containsKey(type)) {
                    Map<String, Map<String, String>> defaultConfigMapMap = configsByType.get(type);
                    if (MapUtils.isNotEmpty(defaultConfigMapMap) && defaultConfigMapMap.containsKey(baseClientConfigName)) {

                        Map<String, String> defaultConfigMap = defaultConfigMapMap.get(baseClientConfigName);
                        if (MapUtils.isNotEmpty(defaultConfigMap) && defaultConfigMap.containsKey(baseConfigName)) {
                            combineMap(configs, baseClientConfigName, baseConfigName, defaultConfigMap.get(baseConfigName));
                        }
                    }
                }
            }
        }

        return configs;

    }

    private void combineMap(Map<String, Map<String, String>> map, String clientConfigName, String configName, String value) {
        map.computeIfAbsent(clientConfigName, key -> {
            HashMap<String, String> configMap = new HashMap<>(16);
            configMap.put(configName, value);
            return configMap;
        });

        map.computeIfPresent(clientConfigName, (key, oldMap) -> {
            oldMap.put(configName, value);
            return oldMap;
        });
    }

    @Override
    public Map<String, Map<String, String>> getEnterpriseConfig(int subType, int type) {
        Map<String, Map<String, String>> configs = new HashMap<>();
        if (type > 0 && enterpriseConfig.get().containsKey(type)) {
            ConfigUtils.putAll(configs, enterpriseConfig.get().get(type));
        }
        if (subType > 0 && enterpriseConfig.get().containsKey(subType)) {
            ConfigUtils.putAll(configs, enterpriseConfig.get().get(subType));
        }
        return configs;
    }

    @Override
    public List<NemoDefaultVersionConfig> getNemoDefaultConfigBytype(int type) {
        return defaultConfigMapper.getNemoDefaultConfigByType(type);
    }

    @Override
    public List<NemoDefaultConfig> getAllDefaultConfig() {
        List<NemoDefaultConfig> nemoDefaultConfigs = defaultConfigMapper.getAllDefaultConfig();
        List<NemoBaseDefaultConfig> baseNemoDefaultConfigs = basedefaultConfigMapper.getAllBaseDefaultConfig();
        return handleDatas(nemoDefaultConfigs, baseNemoDefaultConfigs);
    }

    private List<NemoDefaultConfig> handleDatas(List<NemoDefaultConfig> nemoDefaultConfigs, List<NemoBaseDefaultConfig> baseNemoDefaultConfigs){
        if (ConfigBaseCoverUtil.isNoBase()){
            return nemoDefaultConfigs;
        }
        try {
            // configName - configtype - clientconfigName  确定一个具体的配置项（如果不准确，可以在增加）
            // Map<configName - configtype - clientconfigName, NemoDefaultConfig>
            ConcurrentMap<String, NemoDefaultConfig> nemoDefaultConfigsMap = nemoDefaultConfigs.stream().
                    collect(Collectors.toConcurrentMap(nemoDefaultConfig ->
                                    nemoDefaultConfig.getKey(),
                            NemoDefaultConfig->NemoDefaultConfig, (v1, v2) -> v2));
            // Map<configName : clientconfigName : configtype, NemoDefaultConfig>
            ConcurrentMap<String, NemoDefaultConfig> baseNemoDefaultConfigsMap =  baseNemoDefaultConfigs.stream().
                    collect(Collectors.toConcurrentMap(baseNemoDefaultConfig ->
                                    baseNemoDefaultConfig.getKey(),
                            NemoBaseDefaultConfig->NemoBaseDefaultConfig, (v1, v2) -> v2));
            baseNemoDefaultConfigsMap.putAll(nemoDefaultConfigsMap);
            return baseNemoDefaultConfigsMap.values().stream().collect(Collectors.toList());
        } catch (Exception e) {
            // 如果出现异常，返回原始的nemoDefaultConfigs
            log.info("handleDatas error ",e);
            return nemoDefaultConfigs;
        }
    }

    @Override
    public List<NemoDefaultVersionConfig> getDefaultVersionConfigByTypeAndVersion(int type, int version) {
        return defaultConfigMapper.getNemoDefaultConfigByTypeAndVersion(type, version);
    }

    @Override
    public List<ConfigInfoVo> getAllConfigNameList() {

        List<NemoDefaultConfig> nemoDefaultConfigListData =
                defaultConfigMapper.selectList(new QueryWrapper<NemoDefaultConfig>()
                                                       .select("client_config_name", "config_name")
                                                       .groupBy("client_config_name", "config_name"));

        List<NemoBaseDefaultConfig> baseNemoDefaultConfigListData =
                basedefaultConfigMapper.selectList(new QueryWrapper<NemoBaseDefaultConfig>()
                        .select("client_config_name", "config_name")
                        .groupBy("client_config_name", "config_name"));

        List<NemoDefaultConfig> nemoDefaultConfigList = handleDatas(nemoDefaultConfigListData, baseNemoDefaultConfigListData);

        if (CollectionUtils.isEmpty(nemoDefaultConfigList)) {
            return Collections.emptyList();
        }

        List<ConfigTypeValueLimitModel> configTypeValueLimitModelList = valueLimitMapper.selectAll();
        Map<String, ConfigTypeValueLimitModel> configNameMap = configTypeValueLimitModelList.stream().collect(
                Collectors.toMap(obj -> obj.getClientConfigName() + obj.getConfigName(), obj -> obj));

        List<ConfigInfoVo> configInfoVoList = new ArrayList<>(nemoDefaultConfigList.size() * 2);
        for (NemoDefaultConfig nemoDefaultConfig : nemoDefaultConfigList) {
            if (nemoDefaultConfig == null) {
                continue;
            }
            String clientConfigName = ConfigUtils.transferCommonConfig(nemoDefaultConfig.getClientConfigName());
            String configName = nemoDefaultConfig.getConfigName();
            ConfigTypeValueLimitModel mapModel = configNameMap.get(clientConfigName + configName);


            ConfigInfoVo configInfoVo = new ConfigInfoVo();
            configInfoVo.setClientConfigName(clientConfigName);
            configInfoVo.setConfigName(configName);
            if (mapModel != null) {
                configInfoVo.setConfigDesc(mapModel.getConfigComment());
                configInfoVo.setConfigUIType(mapModel.getConfigUIType());
                configInfoVo.setConfigValueScope(mapModel.getValueScope());
            }
            configInfoVoList.add(configInfoVo);
        }

        return configInfoVoList;

    }

    @Transactional(rollbackFor = Exception.class)
    @DS("master")
    @Override
    public void saveOrUpdateDefaultNemoConfig(List<NemoDefaultConfig> nemoDefaultConfigs) {
        for (NemoDefaultConfig nemoDefaultConfig : nemoDefaultConfigs) {
            String configName = nemoDefaultConfig.getConfigName();
            Integer configType = nemoDefaultConfig.getConfigType();
            String configValue = nemoDefaultConfig.getConfigValue();
            String clientConfigName = nemoDefaultConfig.getClientConfigName();
            if ("common".equals(clientConfigName)){
                clientConfigName = null;
            }
            if (StringUtils.isBlank(configName) || StringUtils.isBlank(configValue)) {
                throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER.getErrorCode(), ErrorStatus.INVALID_NEW_PASSWORD.getResId());
            }
            //尝试更新
            nemoDefaultConfig.setBaseConfigType(0);
            Integer row = defaultConfigMapper.tryUpdateByCondition(configName, configValue, configType, clientConfigName);
            if (row < 1) {
                //新增
                defaultConfigMapper.insert(nemoDefaultConfig);
            }
        }

        sharedThreadPoolTaskExecutor.execute(this::refreshDefaultConfig);

    }

    @Override
    public void refreshDefaultConfigCache() {
        sharedThreadPoolTaskExecutor.execute(this::refreshDefaultConfig);
    }

    @Scheduled(fixedRate = 5 * 60 * 1000, initialDelay = 100)
    void refreshDefaultConfig() {
        log.info("refresh default config from database.");
        List<NemoDefaultConfig> configs = getAllDefaultConfig();
        Map<String, Map<String, Map<String, String>>> nemoConfig = new HashMap<>();
        Map<Integer, Map<String, Map<String, String>>> configsByType = new HashMap<>();

        if (configs != null) {
            for (NemoDefaultConfig config : configs) {
                int type = config.getConfigType();
                if (type < 0) {
                    log.error("Invalid config type: " + config.getConfigType());
                    continue;
                }
                Map<String, Map<String, String>> theDeviceConfigs;
                if (type == TYPE_NEMO_CONFIG && StringUtils.isNotBlank(config.getProductFamily())) {
                    theDeviceConfigs = nemoConfig.computeIfAbsent(config.getProductFamily().trim(), k -> new HashMap<>());
                } else {
                    theDeviceConfigs = configsByType.computeIfAbsent(type, k -> new HashMap<>());
                }
                config.addToConfigs(theDeviceConfigs);
            }
        }

        defaultNemoConfig.set(nemoConfig);
        defaultConfigsByType.set(configsByType);
        enterpriseConfig.set(getDefaultEnterpriseConfig());
    }

    private Map<Integer, Map<String, Map<String, String>>> getDefaultEnterpriseConfig() {
        List<EnterpriseNemoConfig> econfigs = enterpriseNemoConfigMapper.getEnterpriseNemoConfigByProfileId("default");
        Map<Integer, Map<String, Map<String, String>>> allConfigs = new HashMap<>();
        for (EnterpriseNemoConfig config : econfigs) {
            int type = config.getConfigType();
            Map<String, Map<String, String>> theDeviceConfigs = allConfigs.computeIfAbsent(type, k -> new HashMap<>());
            config.addToConfigs(theDeviceConfigs);
        }
        return allConfigs;
    }
}
