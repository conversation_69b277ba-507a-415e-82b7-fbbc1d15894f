package com.xylink.configserver.service.deviceseries;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigDictionaryDataEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-02-04 17:20
 */
public interface DeviceSubtypeSeriesConfigDictionaryDataService extends IService<DeviceSubtypeSeriesConfigDictionaryDataEntity> {

    /**
     * 获取配置项
     *
     * @param configId
     * @param special
     * @return
     */
    List<DeviceSubtypeSeriesConfigDictionaryDataEntity> getByConfigIdAndSpecial(long configId, int special);

    /**
     * 获取系列的配置项
     *
     * @param seriesId
     * @return
     */
    List<DeviceSubtypeSeriesConfigDictionaryDataEntity> getByConfigIdAndSpecial(long seriesId);




}
