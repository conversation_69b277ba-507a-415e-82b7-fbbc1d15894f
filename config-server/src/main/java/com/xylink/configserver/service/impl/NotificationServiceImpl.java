package com.xylink.configserver.service.impl;

import com.ainemo.message.sender.service.MessageSender;
import com.ainemo.message.sender.service.protocl.Message;
import com.ainemo.message.sender.service.util.IpUtil;
import com.ainemo.protocol.Device;
import com.ainemo.protocol.NemoConfig;
import com.ainemo.protocol.NemoConfigChangeContent;
import com.ainemo.protocol.PushNotificationRequest;
import com.ainemo.protocol.event.Event;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xylink.configserver.data.bo.Notification;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.enums.Configs;
import com.xylink.configserver.enums.DeviceType;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.mapper.EnterpriseNemoProfileMapper;
import com.xylink.configserver.service.*;
import com.xylink.configserver.util.Jackson;
import com.xylink.configserver.util.MessageSenderUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.xylink.configserver.util.Constants.sendMessage2DeviceLimitCount;

@Slf4j
@Service
public class NotificationServiceImpl implements NotificationService {

    @Autowired
    MessageSender messageSender;

    @Autowired
    ContentFormatter contentFormatter;

    @Autowired
    DeviceService deviceService;

    @Value("${spring.application.name}")
    private String serverName;

    @Autowired
    private NemoConfigHelper helper;

    @Autowired
    EnterpriseNemoProfileMapper enterpriseNemoProfileMapper;

    @Autowired
    PresenceService presenceService;

    @Autowired
    DeviceConfigService deviceConfigService;

    @Autowired
    SpecialConfigService specialConfigService;

    @Autowired
    OceanCollectionService oceanCollectionService;
    //多线程处理消息下发，可能有大量的IO阻塞，线程池属于IO密集型，阻塞系数暂定0.9
    //异步发送消息，不管消息是否发送成功，拒绝策略：直接抛弃

    private final ConfigValueHandleService configValueHandleService;

    private static final ThreadFactory defaultThreadFactory = new ThreadFactoryBuilder().setNameFormat("NotificationServiceImpl-EnterpriseDeviceMessage-%d").build();

    ExecutorService enterpriseDeviceMessageExecutor = new ThreadPoolExecutor(2,
            8,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000),
            defaultThreadFactory,
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    private static final List<String> BIG_ENDPOINT_DEVICE_NOTIFICATION_CONFIGS = Arrays.asList(Configs.NemoConfig.ENTERPRISE_LOGO,Configs.NemoConfig.SPLASH_PICTURE);

    public NotificationServiceImpl(ConfigValueHandleService configValueHandleService) {
        this.configValueHandleService = configValueHandleService;
    }

    @Override
    public void notifyChangeNemoConfig(RestNemoConfig[] configs, long nemoId) {
        String content = contentFormatter.formatChangeNemoConfigContent(nemoId, configs);
        Event event = new Event(com.xylink.configserver.enums.Event.Type.CHANGE_NEMO_CONFIG.getValue(), content);
        event.setTimestamp(System.currentTimeMillis());
        event.persistent = true;
        UserDevice device = deviceService.getUserDeviceByNemoId(nemoId);
        sendEventMessage(new Device(device.getUserProfileID(),device.getId()+"",device.getType(),device.getDeviceSN()),event);
    }
//
//    @Override
//    public void notifyChangeNemoConfig(long nemoId, RestNemoConfig[] configs, boolean notifyNemo) {
//        if(configs == null) {
//            return;
//        }
//        ObjectMapper mapper = new ObjectMapper();
//        try {
//            UserDevice device = deviceService.getUserDeviceByNemoId(nemoId);
//            NemoConfigChangeContent content = prepareNemoConfigNotify(device, configs);
//            if(community != null) {
//                List<Device> deviceList = new ArrayList<>();
//                if(notifyNemo) {
//                    deviceList.add(new Device(community.getUserProfileId(), String.valueOf(device.getId()), device.getType(), device.getDeviceSN()));
//                    PushNotificationRequest<String> deviceNotification = new PushNotificationRequest<>(UUID.randomUUID().toString(), mapper.writeValueAsString(content));
//                    Notification<PushNotificationRequest<String>> dn = new Notification<>(Notification.NotificationType.NEMO_CONFIG_CHANGE.getType(), deviceNotification);
//                    Message message = createInstantMessage(JSON.toJSONString(dn, SerializerFeature.WriteMapNullValue));
//                    messageSender.sendMessage(deviceList,message);
//                }
//
//                //for soft device, send with persistent message
//                deviceList = new ArrayList<> ();
//                List<Long> userIds = deviceService.getCommunityUserIds(community.getId());
//                for(long userid: userIds) {
//                    deviceList.add(new Device(userid, "", DeviceType.SOFT.getValue(), null));
//                }
//                deviceList.add(new Device(community.getUserProfileId(), "", DeviceType.SOFT.getValue(), null));
//                Notification<NemoConfigChangeContent> userNotification = new Notification<>(Notification.NotificationType.NEMO_CONFIG_CHANGE.getType(), content);
//                Message message = createInstantMessage(JSON.toJSONString(userNotification,SerializerFeature.WriteMapNullValue));
//                messageSender.sendMessage(deviceList,message);
//            }
//
//        } catch (Exception e) {
//            log.error("Failed to notify config change for nemo: " + nemoId,e);
//        }
//    }

    @Override
    public void notifyConfigChange(Map<String, String> computedConfigs, UserDevice userDevicePO, boolean isHardDeviceExceptBigEndpoint) {
        if(!computedConfigs.isEmpty()) {
            if(isHardDeviceExceptBigEndpoint) {
                List<RestNemoConfig> restNemoConfigs = new ArrayList<>();
                for (Map.Entry<String, String> entry : computedConfigs.entrySet()) {
                    restNemoConfigs.add(new RestNemoConfig(entry.getKey(), entry.getValue(), false));
                }
                notifyNemoConfigChange(userDevicePO, restNemoConfigs.toArray(new RestNemoConfig[0]));
            }
        }
        String screenSaverValue =computedConfigs.get(Configs.NemoConfig.CAROUSEL_IMAGES);
        computedConfigs.remove(Configs.NemoConfig.CAROUSEL_IMAGES);
        if (!computedConfigs.isEmpty()) {
            try {
                log.debug("Notify device: " + userDevicePO.getId() + " config changed");
                notifyChangeDeviceConfig(computedConfigs, userDevicePO);
            } catch (DataAccessException e) {
                log.error("Failed to notify device config change", e);
            }
        }
        if (screenSaverValue != null) {
            notifyScreenSaverChanged(userDevicePO.getId(), userDevicePO.getType());
        }
    }

    @Override
    public void notifyNemoConfigChange(UserDevice device, RestNemoConfig[] configs) {
        log.debug("Notify nemo config change start.");
        if(configs == null || device == null) {
            return;
        }
        try {
            NemoConfigChangeContent content = prepareNemoConfigNotify(device, configs);
            List<Device> deviceList = new ArrayList<Device>();
            deviceList.add(new Device(device.getUserProfileID(), String.valueOf(device.getId()), device.getType(), device.getDeviceSN()));
            String strContent = Jackson.getObjectMapper().writeValueAsString(content);
            log.debug("Notify nemo config content: " + strContent);
            PushNotificationRequest<String> deviceNotification = new PushNotificationRequest<String>(UUID.randomUUID().toString(), strContent);
            Notification<PushNotificationRequest<String>> dn = new Notification<PushNotificationRequest<String>>(Notification.NotificationType.NEMO_CONFIG_CHANGE.getType(),	deviceNotification);
            Message message = createInstantMessage(Jackson.getObjectMapper().writeValueAsString(dn));
            messageSender.sendMessage(deviceList,message);
            log.info("Notify nemo config change succeed.");
            oceanCollectionService.deviceConfigUpdate(device);
        } catch (Exception e) {
            log.error("Failed to notify config change for nemo: " + device.getId(), e);
        }
    }


    private void batchNotifyDeviceConfigChange(List<UserDevice> userDeviceList, RestNemoConfig updateConfig){
        if(updateConfig == null) {
            return;
        }
        try {
            List<Device> deviceList = userDeviceList.stream()
                    .map(deviceItem -> {
                        return new Device(deviceItem.getUserProfileID(), String.valueOf(deviceItem.getId()), deviceItem.getType(), deviceItem.getDeviceSN());
                    }).collect(Collectors.toList());
            NemoConfigChangeContent content = prepareNemoConfigNotify(userDeviceList.get(0), new RestNemoConfig[]{updateConfig});
            String strContent = Jackson.getObjectMapper().writeValueAsString(content);
            PushNotificationRequest<String> deviceNotification = new PushNotificationRequest<String>(UUID.randomUUID().toString(), strContent);
            Notification<PushNotificationRequest<String>> dn = new Notification<PushNotificationRequest<String>>(Notification.NotificationType.NEMO_CONFIG_CHANGE.getType(),	deviceNotification);
            Message message = createInstantMessage(Jackson.getObjectMapper().writeValueAsString(dn));
            messageSender.sendMessage(deviceList,message);
            log.info("notify device config change success");
            userDeviceList.forEach(oceanCollectionService::deviceConfigUpdate);
        } catch (Exception e) {
            log.error("notify device config change error ", e);
        }
    }

    @Override
    public void notifyChangeDeviceConfig(Map<String, String> configs, UserDevice userDevicePO) {
        UserDevice devicePO = deviceService.getUserDeviceByNemoId(userDevicePO.getId());
        DeviceConfigChangedContent deviceConfigChangedContent = new DeviceConfigChangedContent(userDevicePO.getId(), configs);
        String content = "";
        try {
            content = Jackson.getObjectMapper().writeValueAsString(deviceConfigChangedContent);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        Event event = new com.ainemo.protocol.event.Event(com.xylink.configserver.enums.Event.Type.DEVICE_CONFIG_CHANGED.getValue(), content);
        event.persistent=true;
        if(devicePO.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue()){
            long nemoNum = deviceService.getNemoNumberByDeviceId(userDevicePO.getId());
            if(nemoNum > 0) {
                sendEventMessage(Device.BruceDevice(nemoNum),event);
                List<RestNemoConfig> restNemoConfigs = new ArrayList<>();
                for(Map.Entry<String, String> entry : configs.entrySet()) {
                    restNemoConfigs.add(new RestNemoConfig(entry.getKey(), entry.getValue(), false));
                }
                UserDevice devicePOBak = new UserDevice();
                BeanUtils.copyProperties(devicePO,devicePOBak);
                devicePOBak.setUserProfileID(nemoNum);
                notifyNemoConfigChange(devicePOBak,restNemoConfigs.toArray(new RestNemoConfig[0]));
            }
        } else if(devicePO.getType() == DeviceType.DESKTOP.getValue()) {
            sendEventMessage(Device.deskDevice(devicePO.getUserProfileID()),event);
        }
    }

    @Override
    public void notifyScreenSaverChanged(long deviceId, int deviceType) {
        Event event = new Event(com.xylink.configserver.enums.Event.Type.IDLE_SCREEN_CHANGED.getValue(), "");
        if(deviceType == DeviceType.BIG_ENDPOINT_DEVICE.getValue()) {
            long nemoNum = deviceService.getNemoNumberByDeviceId(deviceId);
            if(nemoNum>0) {
                sendEventMessage(Device.BruceDevice(nemoNum),event);
            }
        } else if (deviceType == DeviceType.TVBOX.getValue() || deviceType == DeviceType.HARD.getValue()) {
            sendEventMessage(new Device(-1, String.valueOf(deviceId), deviceType),event);
        }
    }

    @Override
    public void norifyProfileRemoveNemoConfig(EnterpriseNemoProfileUpdateReq reqV2){
        log.debug("Notify nemos in this enterprise." + reqV2);
        Integer start = 0;
        String enterpriseProfileId =reqV2.getProfileId();
        List<DeviceConfigUpdate> configUpdates = reqV2.getConfigs();
        if (null == configUpdates || configUpdates.size()<=0){
            return;
        }
        for (DeviceConfigUpdate  config: configUpdates){
            boolean isDeviceType = false;
            String deviceTypeOrSubType = String.valueOf(config.getDeviceSubType());
            if (DeviceType.valueOf(Integer.parseInt(deviceTypeOrSubType)) != null){
                isDeviceType = true;
            }
            //大鱼下发消息，会主动再向server 同步一次，此时会覆盖掉企业配置，先去掉，不再想大鱼下发消息，由大鱼重启获取
            if (isDeviceType && config.getDeviceSubType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue()){
                return;
            }
            int enterpriseNemoCount = 0;
            if (isDeviceType){
                enterpriseNemoCount = enterpriseNemoProfileMapper.getEnterpriseProfileHardCount(enterpriseProfileId,Integer.valueOf(deviceTypeOrSubType));
            }else {
                enterpriseNemoCount = enterpriseNemoProfileMapper.getEnterpriseProfileDeviceBySubCount(enterpriseProfileId,Integer.valueOf(deviceTypeOrSubType));
            }
            int page = 1;
            if(enterpriseNemoCount <=0){
                return;
            }else {
                if(enterpriseNemoCount%sendMessage2DeviceLimitCount==0){
                    page=(int) (enterpriseNemoCount/sendMessage2DeviceLimitCount);
                }else{
                    page=(int) (enterpriseNemoCount/sendMessage2DeviceLimitCount+1);
                }
            }
            int offset=0;
            for (int i = 0; i < page ;i++){
                List<UserDevice> nemos = null ;
                if (isDeviceType){
                    nemos = deviceService.getEnterpriseProfileDeviceByType(enterpriseProfileId,Integer.valueOf(deviceTypeOrSubType),offset,sendMessage2DeviceLimitCount);
                }else {
                    nemos = deviceService.getEnterpriseProfileDeviceBySubType(enterpriseProfileId,Integer.valueOf(deviceTypeOrSubType),offset,sendMessage2DeviceLimitCount);
                }
                if (nemos==null || nemos.size()==0) {
                    break;
                }
                offset = nemos.get(nemos.size()-1).getId().intValue();
                List<UserDevice> finalNemos = nemos;
                enterpriseDeviceMessageExecutor.execute(()->{
                    try {
                        for(UserDevice nemo : finalNemos) {
                            if ( nemo.getType() != DeviceType.BIG_ENDPOINT_DEVICE.getValue() && presenceService.deviceIsOnline(nemo,"")){
                                //判断终端是否有nemoConfig 和specialConfig ，如果有，就不再发送消息，以免引起nemoConfig与企业或者模版配置不同引起错误
                                if (StringUtils.isBlank(deviceConfigService.getNemoConfig(nemo.getId(),config.getConfigName(),config.getClientConfigName())) &&
                                        StringUtils.isBlank(specialConfigService.getSpecialConfigByNameAndClient(nemo.getDeviceSN(),config.getConfigName(),config.getClientConfigName()))){
                                    Map<String, String> computedConfigs = new HashMap<>();
                                    Map<String, String> allConfigs =  deviceConfigService.getCombinedConfig(nemo.getId());
                                    if(allConfigs.containsKey(config.getClientConfigName())) {
                                        computedConfigs.put(config.getClientConfigName(), allConfigs.get(config.getClientConfigName()));
                                    }
                                    if ("common".equalsIgnoreCase(config.getClientConfigName())){
                                        computedConfigs.put(config.getConfigName(), allConfigs.get(config.getConfigName()));
                                    }
                                    log.info("Notify nemo config change: " + nemo.getId());
                                    List<RestNemoConfig> restNemoConfigs = new ArrayList<>();
                                    for(Map.Entry<String, String> entry : computedConfigs.entrySet()) {
                                        restNemoConfigs.add(new RestNemoConfig(entry.getKey(), entry.getValue(), false));
                                    }
                                    //大鱼下发消息，会主动再向server 同步一次，此时会覆盖掉企业配置，先去掉，不再想大鱼下发消息，由大鱼重启获取
                                    /*notificationService.notifyChangeDeviceConfig(computedConfigs,nemo.getId());*/
//                                    notifyChangeNemoConfig(restNemoConfigs.toArray(new RestNemoConfig[0]), nemo.getId());
                                    notifyNemoConfigChange(nemo, restNemoConfigs.toArray(new RestNemoConfig[0]));


                                }
                            }
                        }
                    } catch (Exception e){
                        throw new ServiceException("Failed to notify nemos config change", ErrorStatus.INTERNAL_DATABASE_ERROR);
                    }
                });
                if(null != nemos && nemos.size() > 0 && nemos.size() < sendMessage2DeviceLimitCount){
                    break;
                }
            }




        }


    }

    @Override
    public void notifyUserPasswordExpireTimeChanged(long userId, int deviceType, long deltaTime) {
        String content = "{\"deltaTime\":\"" + deltaTime + "\"}";

        if(deviceType == DeviceType.SOFT.getValue()) {
            Event event = new Event(com.xylink.configserver.enums.Event.Type.CHANGE_PASSWORD_EXPIRE_TIME.getValue(), content);
            sendEventMessage(Device.softDevice(userId), event);
        } else if(deviceType == DeviceType.DESKTOP.getValue()) {
            Event event = new Event(com.xylink.configserver.enums.Event.Type.CHANGE_PASSWORD_EXPIRE_TIME.getValue(), content);
            sendEventMessage(Device.deskDevice(userId), event);
        }
    }

    @Override
    public void norifyProfileNemoConfigChange(EnterpriseNemoConfig configPO){

        log.debug("Notify nemos in this enterprise." + configPO);

        Integer start = 0;
        boolean isDeviceType = false;
        boolean isCommon  = true;
        String enterpriseProfileId =configPO.getEnterpriseProfileId();
        String deviceTypeOrSubType = String.valueOf(configPO.getConfigType());

        if (DeviceType.valueOf(Integer.valueOf(deviceTypeOrSubType)) != null){
            isDeviceType = true;
        }
        if (configPO!=null && configPO.getClientConfigName()!=null && StringUtils.isNotBlank(configPO.getClientConfigName()) && !configPO.getClientConfigName().equals(Configs.COMMON_CONFIG_KEY)){
            isCommon = false;
        }
        //大鱼下发消息，会主动再向server 同步一次，此时会覆盖掉企业配置，先去掉，不再想大鱼下发消息，由大鱼重启获取
        if (bigEndPointDeviceConfigNotificationCheck(configPO, isDeviceType)){
            return;
        }
        RestNemoConfig[] nemoconfigs = new RestNemoConfig[]{new RestNemoConfig(configPO.getConfigName(),configPO.getConfigValue())};
        int enterpriseNemoCount = 0;
        if (isDeviceType){
            enterpriseNemoCount = enterpriseNemoProfileMapper.getEnterpriseProfileHardCount(enterpriseProfileId,Integer.valueOf(deviceTypeOrSubType));
        }else {
            enterpriseNemoCount = enterpriseNemoProfileMapper.getEnterpriseProfileDeviceBySubCount(enterpriseProfileId,Integer.valueOf(deviceTypeOrSubType));
        }

        int page = 1;
        if(enterpriseNemoCount <=0){
            return;
        }else {
            if(enterpriseNemoCount%sendMessage2DeviceLimitCount==0){
                page=(int) (enterpriseNemoCount/sendMessage2DeviceLimitCount);
            }else{
                page=(int) (enterpriseNemoCount/sendMessage2DeviceLimitCount+1);
            }
        }
        int offset=0;
        for (int i = 0; i < page ;i++){
            List<UserDevice> nemos;
            if (isDeviceType){
                nemos = deviceService.getEnterpriseProfileDeviceByType(enterpriseProfileId,Integer.valueOf(deviceTypeOrSubType),offset,sendMessage2DeviceLimitCount);
            }else {
                nemos = deviceService.getEnterpriseProfileDeviceBySubType(enterpriseProfileId,Integer.valueOf(deviceTypeOrSubType),offset,sendMessage2DeviceLimitCount);
            }
            if (nemos==null || nemos.size()==0) {
                break;
            }
            offset = nemos.get(nemos.size()-1).getId().intValue();
                /*logger.info("Notify nemos feature change: " + nemos);*/
            List<UserDevice> finalNemos = nemos;
            boolean finalIsCommon = isCommon;
            enterpriseDeviceMessageExecutor.execute(()->{
                try {
                    for(UserDevice nemo : finalNemos) {
                        try {
                            Thread.sleep(5L);
                        } catch (InterruptedException e) {
                            log.error("InterruptedException:", e);
                        }
                        if (StringUtils.isBlank(deviceConfigService.getNemoConfig(nemo.getId(),configPO.getConfigName(),configPO.getClientConfigName())) &&
                                StringUtils.isBlank(specialConfigService.getSpecialConfigByNameAndClient(nemo.getDeviceSN(),configPO.getConfigName(),configPO.getClientConfigName()))){
                            long number = -1;
                            if(nemo.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue()){
                                number = deviceService.getNemoNumberByDeviceId(nemo.getId());
                            }
                            if ( presenceService.deviceIsOnline(nemo,String.valueOf(number))){
                                //判断终端是否有nemoConfig 和specialConfig ，如果有，就不再发送消息，以免引起nemoConfig与企业或者模版配置不同引起错误
                                    if (finalIsCommon){
                                        if (Configs.NemoConfig.CAROUSEL_IMAGES.equals(configPO.getConfigName())){
                                            notifyScreenSaverChanged(nemo.getId(), nemo.getType());
                                        }
                                        log.info("Notify nemo config change: " + nemo.getId());
                                        // 大鱼发送格式不一样
                                        if (nemo.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue()) {
                                            List<RestNemoConfig> data = new ArrayList<>(nemoconfigs.length);
                                            Collections.addAll(data, nemoconfigs);
                                            notifyChangeDeviceConfig(restNemoConfigToMap(data), nemo);
                                        } else {
                                            notifyNemoConfigChange(nemo, nemoconfigs);
                                        }
                                    }else {
                                        Map<String, String> computedConfigs = new HashMap<>();
                                        Map<String, String> allConfigs = deviceConfigService.getCombinedConfig(nemo.getId());
                                        if(allConfigs.containsKey(configPO.getClientConfigName())) {
                                            computedConfigs.put(configPO.getClientConfigName(), allConfigs.get(configPO.getClientConfigName()));
                                        }
                                        log.info("Notify nemo config change: " + nemo.getId());
                                        List<RestNemoConfig> restNemoConfigs = new ArrayList<>();
                                        for(Map.Entry<String, String> entry : computedConfigs.entrySet()) {
                                            restNemoConfigs.add(new RestNemoConfig(entry.getKey(), entry.getValue(), false));
                                        }
                                        if (nemo.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue()) {
                                            notifyChangeDeviceConfig(restNemoConfigToMap(restNemoConfigs), nemo);
                                        } else {
                                            notifyNemoConfigChange(nemo, restNemoConfigs.toArray(new RestNemoConfig[0]));
                                        }
                                    }
                            }else {
                                log.info("device is brue or not online ,only send message to ocean");
                                oceanCollectionService.deviceConfigUpdate(nemo);
                            }
                        }

                    }
                } catch (Exception e){
                    throw new ServiceException("Failed to notify nemos config change", ErrorStatus.INTERNAL_DATABASE_ERROR);
                }
            });

        }

    }

    @Override
    public Map<String,String> restNemoConfigToMap(List<RestNemoConfig> restNemoConfigs){
        if (CollectionUtils.isEmpty(restNemoConfigs)) {
            return Collections.emptyMap();
        }
        return restNemoConfigs.stream().collect(Collectors.toMap(RestNemoConfig::getName, RestNemoConfig::getValue, (key1, key2) -> key1));
    }

    private boolean bigEndPointDeviceConfigNotificationCheck(EnterpriseNemoConfig configPO, boolean isDeviceType) {
        return isDeviceType && configPO.getConfigType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue() &&  !BIG_ENDPOINT_DEVICE_NOTIFICATION_CONFIGS.contains(configPO.getConfigName());
    }

    @Override
    public void notifyNemoFeatureChange(List<CustomizeFeature> customizeFeatures, UserDevice device) {
        //大鱼特别处理
        if (device != null && device.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue()) {
            long number = deviceService.getNemoNumberByDeviceId(device.getId());
            if (number>0){
                String content = formatNemoFeatureChangeContent(number, customizeFeatures);
                Event event = new Event(com.xylink.configserver.enums.Event.Type.NEMO_SERVICE_CHANGE.getValue(), content);
                sendEventMessage(new Device(device.getUserProfileID(),number+"" , DeviceType.BIG_ENDPOINT_DEVICE.getValue()),event);
            }
        } else {
            String content = formatNemoFeatureChangeContent(device.getId(), customizeFeatures);
            Event event = new Event(com.xylink.configserver.enums.Event.Type.NEMO_SERVICE_CHANGE.getValue(), content);
            sendEventMessage(new Device(device.getUserProfileID(),
                    String.valueOf(device.getId()),
                    device.getType()), event);
        }
    }

    @Override
    public void notifySpecialDeviceAddConfigChange(String nemoSN, Map<String, Map<String, String>> allConfigs) {
        Map<String, String> computedConfigs = new HashMap<>();
        UserDevice device = deviceService.getInUseUserHardDeviceByDevSn(nemoSN);

        boolean isNemoOrBox = false;
        if(device != null) {
            if(device.getType() == DeviceType.HARD.getValue() || device.getType() == DeviceType.TVBOX.getValue()) {
                isNemoOrBox = true;
            }
        }else {
            log.error(" device sn:" + nemoSN + " is null ignore!");
            return;
        }

        if (null!=allConfigs.get(Configs.COMMON_CONFIG_KEY)&& allConfigs.get(Configs.COMMON_CONFIG_KEY).size()>0){
            computedConfigs.putAll(allConfigs.get(Configs.COMMON_CONFIG_KEY));
        }
        for(String clientConfigName : Configs.configsWithClientName) {
            Map<String, String> clientConfigs = allConfigs.get(clientConfigName);
            if(clientConfigs != null) {
                computedConfigs.put(clientConfigName, configValueHandleService.transferToJson(
                        configValueHandleService.parseConfigs(clientConfigs, clientConfigName)));
            }
        }
        if(device.getType() == DeviceType.HARD.getValue() || device.getType() == DeviceType.TVBOX.getValue()
                || device.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue()) {

            if (null!=allConfigs.get(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION)&&allConfigs.get(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION).size()>0){
                try {
                    computedConfigs.put(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION,
                            Jackson.getObjectMapper().writeValueAsString(allConfigs.get(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION)));
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            }
        }
        if(isNemoOrBox && !computedConfigs.isEmpty() ) {
            List<RestNemoConfig> restNemoConfigs = new ArrayList<>();
            for(Map.Entry<String, String> entry : computedConfigs.entrySet()) {

                restNemoConfigs.add(new RestNemoConfig(entry.getKey(), entry.getValue(), false));
            }
            notifyNemoConfigChange(device, restNemoConfigs.toArray(new RestNemoConfig[0]));
        }

    }

    @Override
    public void sendMsgToHardDevice(String content, List<Device> msgDevices) {

        PushNotificationRequest<String> deviceNotification =
                new PushNotificationRequest<>(UUID.randomUUID().toString(), content);
        Notification<PushNotificationRequest<String>> dn =
                new Notification<>(Notification.NotificationType.SERVER_ABILITY_CHANGED.getType(), deviceNotification);
        Message message = createInstantMessage(Jackson.writeValueAsString(dn));
        messageSender.sendMessage(msgDevices, message);

    }

    @Override
    public void sendMsgToSoftDevice(String content, List<Device> msgDevices, boolean persistent) {

        Event event = new Event(
                com.xylink.configserver.enums.Event.Type.SERVER_ABILITY_CHANGED.getValue(),
                content,
                System.currentTimeMillis());
        List<Event> events = Collections.singletonList(event);
        String eventStr = MessageSenderUtils.eventsToMsgServerContent(events);
        Message message = MessageSenderUtils.createModuleType3Message(
                serverName, eventStr, persistent, System.currentTimeMillis());
        messageSender.sendMessage(msgDevices, message);

    }

    public NemoConfigChangeContent prepareNemoConfigNotify(UserDevice device, RestNemoConfig[] configs) {
        NemoConfigChangeContent content = new NemoConfigChangeContent();
        content.setNemoId(device.getId());
        DeviceInfo deviceInfo = deviceService.getPresenceDeviceInfo(device);
        NemoConfig[] cs = new NemoConfig[configs.length];
        for(int i = 0; i < configs.length; i++) {
            RestNemoConfig processedConfig = helper.process(configs[i].getName(), configs[i].getValue(), deviceInfo);
            if(processedConfig != null) {
                cs[i] = new NemoConfig(processedConfig.getName(), processedConfig.getValue());
            }
        }
        content.setConfig(cs);
        return content;
    }

    private void sendEventMessage(Device device, Event event) {
        log.info(" [sendMessageToPresence-carouselImages-{}] send device:{},event:{}", device.endpointId, device,event);
        event.setTimestamp(System.currentTimeMillis());
        List<com.ainemo.protocol.event.Event> eventList = Collections.singletonList(event);
        String content = "";
        try {
            content = Jackson.getObjectMapper().writeValueAsString(new EventsContent(eventList.size(), eventList));
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        Message message;
        if (event.persistent) {
            if (event.getType() == com.xylink.configserver.enums.Event.Type.IDLE_SCREEN_CHANGED.getValue()) {
                // 先处理为字符串content
                List<EventsStrContent.Event> events = new ArrayList<>(eventList.size());
                eventList.forEach((item) ->
                        events.add(EventsStrContent.Event.of(item))
                );
                try {
                    content = Jackson.getObjectMapper().writeValueAsString(new EventsStrContent(events.size(), events));
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            }
            message = createPersistentMessage(content);
        }else {
            message = createInstantMessage(content);
        }
        messageSender.sendMessage(device, message);
    }


    private Message createInstantMessage(String content) {
        return createDefaultMessage(2, false,content);
    }

    private Message createPersistentMessage(String content) {
        return createDefaultMessage(3, true,content);
    }

    private Message createDefaultMessage(int moduleType, boolean persistent, String content) {
        long time = System.currentTimeMillis();
        Message message = new Message();
        message.setModuleType(moduleType);
        message.setNeedOfflineNotify(false);
        message.setMsgId(messageId(serverName,time));
        message.setPersistent(persistent);
        message.setContent(content);
        message.setTime(time);
        return message;
    }

    private static String messageId(String serverName,long time) {
        return serverName + "-" + IpUtil.getLocalIp() + "-" + time;
    }

    private String formatNemoFeatureChangeContent(long nemoId,
                                                 List<CustomizeFeature> customizeFeatures) {
        NemoFeatureChangeContent content = new NemoFeatureChangeContent();
        content.setNemoId(nemoId);
        content.setCustomizedFeatures(customizeFeatures);
        try {
            return Jackson.getObjectMapper().writeValueAsString(content);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return "";
    }



    @Override
    public void batchNotifyDeviceConfigChange(List<UserDevice> deviceList, DeviceConfigUpdate updatedConfig) {

        // 非指定config的情况下, 过滤掉大鱼设备, 不发消息
        List<UserDevice> filterList = deviceList.stream()
                .filter(deviceItem -> {
                    if (deviceItem.getSubType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue() && !BIG_ENDPOINT_DEVICE_NOTIFICATION_CONFIGS.contains(updatedConfig)) {
                        log.info("filter big endpoint device:{}, no send message:{}", deviceItem, updatedConfig);
                        return false;
                    }
                    return true;
                })
                .collect(Collectors.toList());

        //将在线/离线/通话中设备分为3个list
//        Map<String, List<String>> stateMap = Optional.ofNullable(presenceService.batchGetDeviceState(filterList))
//                .map(result -> {
//                    return result.getPresenceInfoList().stream().collect(Collectors.toMap(PresenceInfo::getId, PresenceInfo::getState));
//                })
//                .map(item -> {
//                    Map<String, List<String>> tempStateMap = CollectionUtil.newMap();
//                    item.forEach((k, v) -> {
//                        tempStateMap.putIfAbsent(k, CollectionUtil.newArrayList()).add(v);
//                    });
//                    return tempStateMap;
//                })
//                .orElse(null);

//        if(CollectionUtil.isNotEmpty(stateMap)){
//            //过滤掉离线设备
//            filterList = filterList.stream().filter(item -> {
//                if (stateMap.get(1).contains(PresenceServiceImpl.getUriFromDevice(item, ""))){
//                    return false;
//                }
//                return true;
//            }).collect(Collectors.toList());
//        }

        //notify changed
        batchNotifyDeviceConfigChange(filterList, new RestNemoConfig(updatedConfig.getConfigName(), updatedConfig.getConfigValue()));

        //offline device send msg to ocean


    }

    //    @Override
//    public void notifyDeptConfigChange(List<UserDevice> deviceList, List<DeptConfigPO> configList) {
//
//        deviceList.stream().filter(deviceItem -> {
//            if (bigEndPointDeviceConfigNotificationCheck(deviceItem, DeviceType.valueOf(Integer.valueOf(deviceItem.getSubType())) != null)){
//
//            }
//        })
//
//
//        log.info("update devices:{} configs:{}", deviceList, configList);
//
//
//        Integer start = 0;
//        boolean isDeviceType = false;
//        boolean isCommon  = true;
//        String enterpriseProfileId =configPO.getEnterpriseProfileId();
//        String deviceTypeOrSubType = String.valueOf(configPO.getConfigType());
//
//        if (DeviceType.valueOf(Integer.valueOf(deviceTypeOrSubType)) != null){
//            isDeviceType = true;
//        }
//        if (configPO!=null && configPO.getClientConfigName()!=null && StringUtils.isNotBlank(configPO.getClientConfigName()) && !configPO.getClientConfigName().equals(Configs.COMMON_CONFIG_KEY)){
//            isCommon = false;
//        }
//        //大鱼下发消息，会主动再向server 同步一次，此时会覆盖掉企业配置，先去掉，不再想大鱼下发消息，由大鱼重启获取
//        if (bigEndPointDeviceConfigNotificationCheck(configPO, isDeviceType)){
//            return;
//        }
//        RestNemoConfig[] nemoconfigs = new RestNemoConfig[]{new RestNemoConfig(configPO.getConfigName(),configPO.getConfigValue())};
//        int enterpriseNemoCount = 0;
//        if (isDeviceType){
//            enterpriseNemoCount = enterpriseNemoProfileMapper.getEnterpriseProfileHardCount(enterpriseProfileId,Integer.valueOf(deviceTypeOrSubType));
//        }else {
//            enterpriseNemoCount = enterpriseNemoProfileMapper.getEnterpriseProfileDeviceBySubCount(enterpriseProfileId,Integer.valueOf(deviceTypeOrSubType));
//        }
//
//        int page = 1;
//        if(enterpriseNemoCount <=0){
//            return;
//        }else {
//            if(enterpriseNemoCount%sendMessage2DeviceLimitCount==0){
//                page=(int) (enterpriseNemoCount/sendMessage2DeviceLimitCount);
//            }else{
//                page=(int) (enterpriseNemoCount/sendMessage2DeviceLimitCount+1);
//            }
//        }
//        int offset=0;
//        for (int i = 0; i < page ;i++){
//            List<UserDevice> nemos;
//            if (isDeviceType){
//                nemos = deviceService.getEnterpriseProfileDeviceByType(enterpriseProfileId,Integer.valueOf(deviceTypeOrSubType),offset,sendMessage2DeviceLimitCount);
//            }else {
//                nemos = deviceService.getEnterpriseProfileDeviceBySubType(enterpriseProfileId,Integer.valueOf(deviceTypeOrSubType),offset,sendMessage2DeviceLimitCount);
//            }
//            if (nemos==null || nemos.size()==0) {
//                break;
//            }
//            offset = nemos.get(nemos.size()-1).getId().intValue();
//            /*logger.info("Notify nemos feature change: " + nemos);*/
//            List<UserDevice> finalNemos = nemos;
//            boolean finalIsCommon = isCommon;
//            enterpriseDeviceMessageExecutor.execute(()->{
//                try {
//                    for(UserDevice nemo : finalNemos) {
//                        try {
//                            Thread.sleep(5L);
//                        } catch (InterruptedException e) {
//                            log.error("InterruptedException:", e);
//                        }
//                        if (StringUtils.isBlank(deviceConfigService.getNemoConfig(nemo.getId(),configPO.getConfigName(),configPO.getClientConfigName())) &&
//                                StringUtils.isBlank(specialConfigService.getSpecialConfigByNameAndClient(nemo.getDeviceSN(),configPO.getConfigName(),configPO.getClientConfigName()))){
//                            long number = -1;
//                            if(nemo.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue()){
//                                number = deviceService.getNemoNumberByDeviceId(nemo.getId());
//                            }
//                            if ( presenceService.deviceIsOnline(nemo,String.valueOf(number))){
//                                //判断终端是否有nemoConfig 和specialConfig ，如果有，就不再发送消息，以免引起nemoConfig与企业或者模版配置不同引起错误
//                                if (finalIsCommon){
//                                    if (Configs.NemoConfig.CAROUSEL_IMAGES.equals(configPO.getConfigName())){
//                                        notifyScreenSaverChanged(nemo.getId(), nemo.getType());
//                                    }
//                                    log.info("Notify nemo config change: " + nemo.getId());
//                                    // 大鱼发送格式不一样
//                                    if (nemo.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue()) {
//                                        List<RestNemoConfig> data = new ArrayList<>(nemoconfigs.length);
//                                        Collections.addAll(data, nemoconfigs);
//                                        notifyChangeDeviceConfig(restNemoConfigToMap(data), nemo);
//                                    } else {
//                                        notifyNemoConfigChange(nemo, nemoconfigs);
//                                    }
//                                }else {
//                                    Map<String, String> computedConfigs = new HashMap<>();
//                                    Map<String, String> allConfigs = deviceConfigService.getCombinedConfig(nemo.getId());
//                                    if(allConfigs.containsKey(configPO.getClientConfigName())) {
//                                        computedConfigs.put(configPO.getClientConfigName(), allConfigs.get(configPO.getClientConfigName()));
//                                    }
//                                    log.info("Notify nemo config change: " + nemo.getId());
//                                    List<RestNemoConfig> restNemoConfigs = new ArrayList<>();
//                                    for(Map.Entry<String, String> entry : computedConfigs.entrySet()) {
//                                        restNemoConfigs.add(new RestNemoConfig(entry.getKey(), entry.getValue(), false));
//                                    }
//                                    if (nemo.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue()) {
//                                        notifyChangeDeviceConfig(restNemoConfigToMap(restNemoConfigs), nemo);
//                                    } else {
//                                        notifyNemoConfigChange(nemo, restNemoConfigs.toArray(new RestNemoConfig[0]));
//                                    }
//                                }
//                            }else {
//                                log.info("device is brue or not online ,only send message to ocean");
//                                oceanCollectionService.deviceConfigUpdate(nemo);
//                            }
//                        }
//
//                    }
//                } catch (Exception e){
//                    throw new ServiceException("Failed to notify nemos config change", ErrorStatus.INTERNAL_DATABASE_ERROR);
//                }
//            });
//
//        }
//
//    }


    @Override
    public void notifyAllDeviceConfigChange(List<UserDevice> userDeviceList, List<DeviceConfigUpdate> updateConfigList) {
        for (DeviceConfigUpdate updateConfigItem : updateConfigList){
            RestNemoConfig[] nemoconfigs = new RestNemoConfig[]{new RestNemoConfig(updateConfigItem.getConfigName(),updateConfigItem.getConfigValue())};
            enterpriseDeviceMessageExecutor.execute(()->{
                try {
                    for(UserDevice deviceItem : userDeviceList) {

                        //过滤掉一些脏数据, dev环境数据较乱
                        if (deviceItem.getType()<=0 || deviceItem.getSubType()<=0){
                            log.info("device not format:{}", deviceItem.getId());
                            continue;
                        }

                        if (StringUtils.isBlank(deviceConfigService.getNemoConfig(deviceItem.getId(),updateConfigItem.getConfigName(),updateConfigItem.getClientConfigName())) &&
                                StringUtils.isBlank(specialConfigService.getSpecialConfigByNameAndClient(deviceItem.getDeviceSN(),updateConfigItem.getConfigName(),updateConfigItem.getClientConfigName()))){
                            long number = -1;
                            if(deviceItem.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue()){
                                number = deviceService.getNemoNumberByDeviceId(deviceItem.getId());
                            }
                            if (presenceService.deviceIsOnline(deviceItem,String.valueOf(number))){
                                //判断终端是否有nemoConfig 和specialConfig ，如果有，就不再发送消息，以免引起nemoConfig与企业或者模版配置不同引起错误
                                if (StringUtils.equalsIgnoreCase("common", updateConfigItem.getClientConfigName())){
                                    if (Configs.NemoConfig.CAROUSEL_IMAGES.equals(updateConfigItem.getConfigName())){
                                        notifyScreenSaverChanged(deviceItem.getId(), deviceItem.getType());
                                    }
                                    // 大鱼发送格式不一样
                                    if (deviceItem.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue()) {
                                        List<RestNemoConfig> data = new ArrayList<>();
                                        Collections.addAll(data, nemoconfigs);
                                        notifyChangeDeviceConfig(restNemoConfigToMap(data), deviceItem);
                                    } else {
                                        notifyNemoConfigChange(deviceItem, nemoconfigs);
                                    }
                                }else {
                                    Map<String, String> computedConfigs = new HashMap<>();
                                    Map<String, String> allConfigs = deviceConfigService.getCombinedConfig(deviceItem.getId());
                                    if(allConfigs.containsKey(updateConfigItem.getClientConfigName())) {
                                        computedConfigs.put(updateConfigItem.getClientConfigName(), allConfigs.get(updateConfigItem.getClientConfigName()));
                                    }
                                    log.info("Notify nemo config change: " + deviceItem.getId());
                                    List<RestNemoConfig> restNemoConfigs = new ArrayList<>();
                                    for(Map.Entry<String, String> entry : computedConfigs.entrySet()) {
                                        restNemoConfigs.add(new RestNemoConfig(entry.getKey(), entry.getValue(), false));
                                    }
                                    if (deviceItem.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue()) {
                                        notifyChangeDeviceConfig(restNemoConfigToMap(restNemoConfigs), deviceItem);
                                    } else {
                                        notifyNemoConfigChange(deviceItem, restNemoConfigs.toArray(new RestNemoConfig[0]));
                                    }
                                    log.info("notify device change config success: " + deviceItem.getId());
                                }
                            }else {
                                log.info("device is brue or not online ,only send message to ocean");
                                oceanCollectionService.deviceConfigUpdate(deviceItem);
                            }
                        }

                    }
                } catch (Exception e){
                    throw new ServiceException("Failed to notify nemos config change", ErrorStatus.INTERNAL_DATABASE_ERROR);
                }
            });
        }
    }
}
