package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xylink.configserver.data.bo.department.DepartmentBO;
import com.xylink.configserver.data.bo.department.DidLevelMainDept;
import com.xylink.configserver.data.bo.department.UserDeviceDeptCascadeMainDeptBO;
import com.xylink.configserver.data.dto.BatchUpdateDeviceConfigResponseDTO;
import com.xylink.configserver.data.enums.ResultCodeEnum;
import com.xylink.configserver.data.exception.RestException;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.data.model.specialfeature.SpecialFeatureConfigEntity;
import com.xylink.configserver.data.model.specialfeature.SpecialFeatureNemoEntity;
import com.xylink.configserver.data.response.R;
import com.xylink.configserver.data.vo.DeviceConfigVo;
import com.xylink.configserver.enums.Configs;
import com.xylink.configserver.enums.DeviceSubType;
import com.xylink.configserver.enums.DeviceType;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.mapper.CommonGroupConfigMapper;
import com.xylink.configserver.mapper.DeviceConfigMapper;
import com.xylink.configserver.mapper.UserDeviceMapper;
import com.xylink.configserver.mapper.specialfeatue.SpecialFeatureConfigMapper;
import com.xylink.configserver.mapper.specialfeatue.SpecialFeatureDeviceMapper;
import com.xylink.configserver.proxy.InternalApiProxy;
import com.xylink.configserver.service.*;
import com.xylink.configserver.service.common.CommonVariableService;
import com.xylink.configserver.util.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.xylink.configserver.util.Constants.ME_NEMO_SHOW_JOIN_ENT_QRCODE;
import static com.xylink.configserver.util.RestApiContext.getCurrentDevice;

@Slf4j
@Service
public class  DeviceConfigServiceImpl extends ServiceImpl<DeviceConfigMapper, DeviceConfig> implements DeviceConfigService {

    @Value("${common.startShowJoinEnterpriseCodeTime}")
    private long startShowJoinEnterpriseCodeTime;

    @Autowired
    UserDeviceMapper userDeviceMapper;

    @Autowired
    DefaultConfigService defaultConfigService;

    @Autowired
    DeviceService deviceService;

    @Autowired
    DeviceSoftVersionService deviceSoftVersionService;

    @Autowired
    EnterpriseNemoConfigService enterpriseNemoConfigService;

    @Autowired
    SpecialConfigService specialConfigService;

    @Autowired
    DeviceConfigMapper deviceConfigMapper;

    @Autowired
    RechargeConfigService rechargeConfigService;

    @Autowired
    CommonGroupConfigMapper commonGroupConfigMapper;

    @Autowired
    UserConfigService userConfigService;

    @Autowired
    NotificationService notificationService;

    @Autowired
    NemoConfigService nemoConfigService;

    @Autowired
    PlatformConfigService platformConfigService;

    @Autowired
    OceanCollectionService oceanCollectionService;

    @Autowired
    InternalApiProxy internalApiProxy;

    @Autowired
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Autowired
    private DeptConfigService deptConfigService;

    @Autowired
    private CommonVariableService commonVariableService;

    @Autowired
    private  SpecialFeatureDeviceMapper specialFeatureDeviceMapper;

    @Autowired
    private  SpecialFeatureConfigMapper specialFeatureConfigMapper;

    @Autowired
    private  ConfigValueHandleService configValueHandleService;

    private static final ThreadFactory defaultThreadFactory = new ThreadFactoryBuilder().setNameFormat("DeviceConfigServiceImpl-%d").build();


    ExecutorService deviceMessageExecutor = new ThreadPoolExecutor(2,
            10,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(100),
            defaultThreadFactory,
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    @Override
    public Map<String, String> getCombinedConfig(long deviceId) {
        Map<String, String> combinedConfig = new HashMap<>();
        try {
            UserDevice device = RestApiContext.getCurrentDevice();
            if (null == device) {
                device = userDeviceMapper.getUserDeviceByNemoId(deviceId);
                if (null == device) {
                    log.error("getUICustomization deviceId:{}", deviceId);
                    return combinedConfig;
                }
            }
            if (DeviceType.isHardDevice(device.getType())) {
                Map<String, Map<String, String>> allConfigs = getOriginHardConfig(device);
                combinedConfig = ConfigUtils.combinedConfig(allConfigs, device.getType());
            } else {
                combinedConfig = userConfigService.getCombinedConfig(device.getUserProfileID(), null);
            }
        } catch (JsonProcessingException e) {
            throw new ServiceException("Cant access db for config of nemo",
                    ErrorStatus.INTERNAL_ERROR);
        }
        return combinedConfig;
    }

    @Override
    public Map<String, String> getCombinedCallConfig(long deviceId) {
        Map<String, String> combinedConfig = new HashMap<>();
        try {
            UserDevice device = userDeviceMapper.getUserDeviceByNemoId(deviceId);
            if (null == device) {
                return combinedConfig;
            }

            Map<String, Map<String, String>> allConfigs = new HashMap<>();
            addDefaultConfig(allConfigs, device);

            addEnterpriseDefaultConfig(allConfigs, device);

            addEnterpriseProfileConfig(allConfigs, device);

            addDeviceConfig(allConfigs, device);

            combinedConfig = ConfigUtils.combinedConfig(allConfigs, device.getType());

        } catch (JsonProcessingException e) {
            throw new ServiceException("Cant access db for config of nemo",
                    ErrorStatus.INTERNAL_ERROR);
        }
        return combinedConfig;
    }

    @Override
    public Map<String, Map<String, String>> getOriginHardConfig(UserDevice device) {
        Map<String, Map<String, String>> allConfigs = new HashMap<>();
        addDefaultConfig(allConfigs, device);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addEnterpriseDefaultConfig(allConfigs, device);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addNemoDefaultVersionConfigByVersion(allConfigs, device);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addPlatformConfig(device, allConfigs);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addEnterpriseProfileConfig(allConfigs, device);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addDeptConfig(allConfigs, device);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addSpecialDeptConfig(allConfigs, device);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addSpecialConfig(allConfigs, device);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addDeviceConfig(allConfigs, device);
        configValueHandleService.removeErrorConfigs(allConfigs);

        addRechargeConfig(allConfigs, device);
        configValueHandleService.removeErrorConfigs(allConfigs);
        return allConfigs;
    }

    private void addDeptConfig(Map<String, Map<String, String>> allConfigs, UserDevice device) {
        if (!commonVariableService.isEnableDeptConfig()){
            return;
        }
        String deptId = internalApiProxy.getOneLevelDeptByDeviceIdFromContact(device.getEnterpriseId(), device.getId());
        List<DeptConfigPO> list = deptConfigService.getSourceDeptConfig(device.getEnterpriseId(), deptId);
        for (DeptConfigPO deptConfigItem : list) {
            deptConfigItem.addToConfigs(allConfigs);
        }
    }


    private void addSpecialDeptConfig(Map<String, Map<String, String>> allConfigs, UserDevice device) {

        // fetch cascade root dept info by contact callback
        if (device == null || device.getId() == null) {
            return;
        }
        long deviceId = device.getId();
        String enterpriseId = device.getEnterpriseId();
        if (StringUtils.isBlank(enterpriseId)) {
            log.warn("invalid enterpriseId");
            return;
        }

        //  complete timeout and hystrix strategy
        UserDeviceDeptCascadeMainDeptBO mainDept = internalApiProxy.getMainDept(-1, deviceId, enterpriseId);
        if (mainDept == null) {
            log.warn("empty main dept info");
            return;
        }
        List<DidLevelMainDept> deviceDepts = mainDept.getDevice();
        if (CollectionUtils.isEmpty(deviceDepts)) {
            return;
        }
        DidLevelMainDept deviceDept = deviceDepts.get(0);
        if (deviceDept == null) {
            return;
        }
        List<DepartmentBO> depts = deviceDept.getDepartmentAndLevels();
        if (CollectionUtils.isEmpty(depts)) {
            return;
        }

        // sorted it in order to find the nearest main department
        List<Long> deptIds = Lists.newArrayListWithExpectedSize(depts.size());
        depts.stream().sorted(Comparator.comparingInt(DepartmentBO::getLevel)).forEach((e) -> {
            Long deptId = e.getDeptId();
            deptIds.add(deptId);
        });

        // mapping config to dept`s id
        Map<Long, Map<String, Map<String, String>>> mapping = new HashMap<>(deptIds.size());
        List<SpecialFeatureNemoEntity> featureNemoEntities =
                specialFeatureDeviceMapper.selectDeptFeature(deptIds);
        if (CollectionUtils.isEmpty(featureNemoEntities)) {
            return;
        }
        for (SpecialFeatureNemoEntity featureNemoEntity : featureNemoEntities) {
            if (featureNemoEntity == null || StringUtils.isBlank(featureNemoEntity.getFeatureId())) {
                continue;
            }

            String featureId = featureNemoEntity.getFeatureId();
            Long deptId = Long.parseLong(featureNemoEntity.getNemoSn());
            //List<SpecialFeatureConfigEntity> configEntities =
            //        specialFeatureConfigMapper.getSpecialConfigsByFeatureById(featureId);
            List<SpecialFeatureConfigEntity> configEntities =
                    specialFeatureConfigMapper.selectList(new LambdaQueryWrapper<SpecialFeatureConfigEntity>().eq(SpecialFeatureConfigEntity::getSpecialFeatureId, featureId));
            for (SpecialFeatureConfigEntity configEntity : configEntities) {
                String clientConfigName = configEntity.getClientConfigName();
                String configName = configEntity.getConfigName();
                String configValue = configEntity.getConfigValue();

                mapping.putIfAbsent(deptId, new HashMap<>());
                Map<String, Map<String, String>> innerConfig = mapping.get(deptId);
                innerConfig.putIfAbsent(clientConfigName, new HashMap<>());
                innerConfig.get(clientConfigName).put(configName, configValue);
            }
        }

        // Warn: there is a performance problem: if dept config is belonged to a common function,
        //      we need to traversal all config from high to low priority due to incomplete config items.
        for (Long deptId : deptIds) {
            ConfigUtils.putAll(allConfigs, mapping.get(deptId));
        }


    }

    private void addRechargeConfig(Map<String, Map<String, String>> allConfigs, UserDevice device) {
        List<RechargeConfig> rechargeConfigPOS = rechargeConfigService.getRechargeConfigs(device.getDeviceSN());
        String enable4kResolution = null;
        long currentTimeMill = System.currentTimeMillis();
        for(RechargeConfig baseConfig : rechargeConfigPOS) {
            if(baseConfig.getConfigExpireTime() != null && currentTimeMill < baseConfig.getConfigExpireTime()){
                baseConfig.addToConfigs(allConfigs);
                if(Configs.ENABLE_4K_RESOLUTION.equals(baseConfig.getConfigName())){
                    enable4kResolution = baseConfig.getConfigValue();
                }
            }
        }
        if(Boolean.TRUE.toString().equals(enable4kResolution)){
            rebuildMediaConfig(allConfigs, com.xylink.configserver.data.enums.Resolution.K4.name(), device.getType(), device.getSubType());
        }
    }

    public void rebuildMediaConfig(Map<String, Map<String, String>> allConfigs,
                                   String resolution,
                                   int deviceType,
                                   int deviceSubtype) {

        List<CommonGroupConfig> commonGroupConfigs;

        // 写入全局分组配置
        commonGroupConfigs = commonGroupConfigMapper.getGroupConfigsByType(resolution, 0);
        if (CollectionUtils.isNotEmpty(commonGroupConfigs)) {
            CommonGroupConfig.configPOs2All(commonGroupConfigs, allConfigs);
        } else {
            return;
        }

        // 写入大类分组配置
        commonGroupConfigs = commonGroupConfigMapper.getGroupConfigsByType(resolution, deviceType);
        if (CollectionUtils.isNotEmpty(commonGroupConfigs)) {
            CommonGroupConfig.configPOs2All(commonGroupConfigs, allConfigs);
        }

        // 写入子类分组配置
        commonGroupConfigs = commonGroupConfigMapper.getGroupConfigsByType(resolution, deviceSubtype);
        if (CollectionUtils.isNotEmpty(commonGroupConfigs)) {
            CommonGroupConfig.configPOs2All(commonGroupConfigs, allConfigs);
        }


    }

    @Override
    public List<DeviceConfig> getDeviceClientConfigs(long deviceId, String clientConfigName) {
        return deviceConfigMapper.getDeviceClientConfigs(deviceId, clientConfigName);
    }

    @Override
    public String getNemoConfig(long deviceId, String configName) {
        List<DeviceConfig> deviceConfigs = deviceConfigMapper.getNemoConfig(deviceId, configName);
        if (deviceConfigs != null && deviceConfigs.size() > 0) {
            return deviceConfigs.get(0).getConfigValue();
        } else {
            return null;
        }
    }

    @Override
    public String getNemoConfig(long deviceId, String configName, String clientConfigName) {
        List<DeviceConfig> deviceConfigs = deviceConfigMapper.getNemoConfigByConfigNameAndClientConfigName(deviceId, configName,clientConfigName);
        if (deviceConfigs != null && deviceConfigs.size() > 0) {
            return deviceConfigs.get(0).getConfigValue();
        } else {
            return null;
        }
    }

    @Override
    public String getDeviceConfig(UserDevice userDevice, String configName) {
        if (DeviceType.isHardDevice(userDevice.getType())) {
            if (Configs.configsWithClientName.contains(configName)
                    || Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION.equals(configName)) {

                Map<String, String> ret = new HashMap<>();
                ret.putAll(getDefaultClientConfig(userDevice.getDeviceSN(), userDevice.getType(), configName, userDevice.getSubType()));
                ret.putAll(getDefaultEnterpriseConfig(configName, userDevice.getSubType(), userDevice.getDeviceSN(), userDevice.getType()));
                ret.putAll(getEnterpriseNemoClientConfig(userDevice.getDeviceSN(), configName));
                ret.putAll(getDeptConfigMap(userDevice, configName));
                ret.putAll(getNemoSpecialClientConfig(userDevice.getDeviceSN(), configName));
                ret.putAll(getDeviceClientConfig(userDevice.getId(), configName));

                return configValueHandleService.transferToJson(configValueHandleService.parseConfigs(ret, configName));
            } else {
                return getSingleValueConfig(userDevice, configName);
            }
        } else {
            RestApiContext.setCurrentDevice(userDevice);
            return userConfigService.getUserConfig(userDevice.getUserProfileID(), configName);
        }
    }

    private Map<String, String> getDeptConfigMap(UserDevice userDevice, String configName) {
        try {
            String deptId="-1";
            if (DeviceType.isHardDevice(userDevice.getType())){
                deptId = internalApiProxy.getOneLevelDeptByDeviceIdFromContact(userDevice.getEnterpriseId(), userDevice.getId());
            } else {
                deptId = internalApiProxy.getOneLevelDeptByUserProfileIdFromContact(userDevice.getEnterpriseId(), userDevice.getUserProfileID());
            }
            log.info("device:{}, deptId:{}", userDevice.getId(), deptId);

            List<DeptConfigPO> deptConfigList = deptConfigService.getSourceDeptConfig(userDevice.getEnterpriseId(), deptId);
            return deptConfigList.stream().collect(Collectors.toMap(item->item.getConfigName(), item->item.getConfigValue()));
        } catch (Exception e){
            log.info("handler dept config error", e);
        }
        return null;
    }


    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void deleteConfig(DeviceConfig config) {
        deviceConfigMapper.deleteDeviceConfigById(config.getId());
    }

    @Override
    public void deleteConfigByName(DeviceConfigVo[] deviceConfigList) {
        if (deviceConfigList == null || deviceConfigList.length <= 0) {
            return;
        }
        for (DeviceConfigVo deviceConfig : deviceConfigList) {
            if (deviceConfig == null) {
                continue;
            }
            deviceConfigMapper.deleteDeviceConfigByConfigName(deviceConfig.getNemoId(), deviceConfig.getClientConfigName(), deviceConfig.getConfigName());
        }
    }


    @Override
    public void deleteConfigByNameAndSn(DeviceConfigUpdate config, String sn) {
        //sn 查询设备
        List<UserDevice> userDeviceByDevSn = userDeviceMapper.getUserDeviceByDevSn(sn);
        if (org.springframework.util.CollectionUtils.isEmpty(userDeviceByDevSn)) {
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ResultCodeEnum.DEVICE_IS_NOT_EXISTS.getCode(),
                    "设备不存在");
        }
        //获取设备
        UserDevice userDevice = userDeviceByDevSn.get(0);
        deviceConfigMapper.deleteDeviceConfigByConfigName(userDevice.getId(), config.getClientConfigName(), config.getConfigName());
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void addDeviceConfig(DeviceConfig config) {
        List<DeviceConfig> deviceConfigList = new ArrayList<>();
        deviceConfigList.add(config);
        configValueHandleService.verifyConfig(deviceConfigList);
        deviceConfigMapper.addDeviceConfig(config);
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void applyNemoConfig(long nemoId, String configName, String configValue, long expireTime, String clientConfigName) {
        List<BaseConfig> deviceConfigUpdateList = new ArrayList<>(1);
        BaseConfig baseConfig = new BaseConfig(configName, configValue, clientConfigName);
        deviceConfigUpdateList.add(baseConfig);
        configValueHandleService.verifyConfig(deviceConfigUpdateList);

        if (StringUtils.isBlank(clientConfigName) || clientConfigName.equals(Configs.COMMON_CONFIG_KEY)) {
            clientConfigName = Configs.COMMON_CONFIG_KEY;
        }
        DeviceConfig deviceConfig = deviceConfigMapper.getClientConfig(nemoId, configName, clientConfigName);
        if (deviceConfig != null) {
            deviceConfig.setConfigValue(configValue);
            deviceConfigMapper.updateDeviceConfigById(deviceConfig);
        } else {
            DeviceConfig config = new DeviceConfig(nemoId, configName, configValue, clientConfigName);
            config.setConfigExpireTime(expireTime);
            deviceConfigMapper.addDeviceConfig(config);
        }
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void deleteUserNemoRelation(long nemoId) {
        deviceConfigMapper.deleteUserNemoRelation(nemoId);
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void addUserNemoRelation(long userId, long nemoId) {
        deviceConfigMapper.addUserNemoRelation(userId, nemoId);
    }



    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void updateInternalDeviceConfigBySn(DeviceConfigUpdate config, String sn) {
        // 获取设备信息
        List<UserDevice> userDeviceByDevSn = userDeviceMapper.getUserDeviceByDevSn(sn);
        if (org.springframework.util.CollectionUtils.isEmpty(userDeviceByDevSn)) {
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ResultCodeEnum.DEVICE_IS_NOT_EXISTS.getCode(),
                    "设备不存在");
        }
        //获取设备
        UserDevice userDevice = userDeviceByDevSn.get(0);
        //将config  转为 DeviceConfigUpdate[] configs ，调用updateInternalDeviceConfig(DeviceConfigUpdate[] configs, long deviceId) 实现逻辑
        DeviceConfigUpdate[] configs = {config};
        log.debug("Update internal device config for device sn: {}, config: {}", sn, config);
        updateInternalDeviceConfig(configs, userDevice.getId());
    }




    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void updateInternalDeviceConfig(DeviceConfigUpdate[] configs, long deviceId) {
        Map<String, String> computedConfigs = new HashMap<>();
        if (configs == null) {
            return;
        }
        Map<String,String> device4kConfigs = new HashMap<>();
        Set<String> jsonConfigs = new HashSet<>();
        for (DeviceConfigUpdate deviceConfigUpdate : configs) {
            log.debug("Apply: " + deviceConfigUpdate + " for device: " + deviceId);
            if (Configs.DEVICE_4K_CONFIG_NAME.contains(deviceConfigUpdate.getConfigName())) {
                device4kConfigs.put(deviceConfigUpdate.getConfigName(),deviceConfigUpdate.getConfigValue());
            }else {
                applyNemoConfig(deviceId, deviceConfigUpdate.getConfigName(),
                        deviceConfigUpdate.getConfigValue(), Long.MAX_VALUE, deviceConfigUpdate.getClientConfigName());
            }
            if(deviceConfigUpdate.getClientConfigName() != null && !deviceConfigUpdate.getClientConfigName().trim().isEmpty()&& !deviceConfigUpdate.getClientConfigName().trim().equals(Configs.COMMON_CONFIG_KEY)) {
                jsonConfigs.add(deviceConfigUpdate.getClientConfigName().trim());
            } else {
                computedConfigs.put(deviceConfigUpdate.getConfigName(), deviceConfigUpdate.getConfigValue());
            }
        }
        Map<String, String> allConfigs;
        UserDevice userDevicePO = userDeviceMapper.getUserDeviceByNemoId(deviceId);
        if(DeviceType.isHardDevice(userDevicePO.getType()) && device4kConfigs.size() > 0){
            rechargeConfigService.applyDevice4kConfig(userDevicePO,device4kConfigs);
            jsonConfigs.add(Configs.NemoConfig.MEDIA_CONFIG);
            jsonConfigs.add(Configs.NemoConfig.REMOTE_SDK_CONFIG);
        }
        boolean isHardDeviceExceptBigEndpoint = false;
        if(userDevicePO != null) {
            if(DeviceType.isHardDeviceExceptBigEndpoint(userDevicePO.getType())) {
                isHardDeviceExceptBigEndpoint = true;
            }
            allConfigs = getCombinedConfig(deviceId);
            for (String jsonConfigName : jsonConfigs) {
                if (allConfigs.containsKey(jsonConfigName)) {
                    computedConfigs.put(jsonConfigName, allConfigs.get(jsonConfigName));
                }
            }
            for(String device4kConfig : device4kConfigs.keySet()) {
                if(allConfigs.containsKey(device4kConfig)) {
                    computedConfigs.put(device4kConfig, allConfigs.get(device4kConfig));
                }else{
                    computedConfigs.remove(device4kConfig);
                }
            }
        }
        final boolean hardDeviceExceptBigEndpoint = isHardDeviceExceptBigEndpoint;
        //向终端发送通知
        deviceMessageExecutor.execute(() -> {
            notificationService.notifyConfigChange(computedConfigs, userDevicePO, hardDeviceExceptBigEndpoint);
            UserDevice device = deviceService.getUserDeviceByNemoId(deviceId);
            oceanCollectionService.deviceConfigUpdate(device);
        });
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void updateDeviceConfig(Map<String, String> configs, long deviceId) {
        UserDevice currentDevice = getCurrentDevice();
        UserDevice devicePO = deviceService.getUserDeviceByNemoId(deviceId);

        if (devicePO == null) {
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ResultCodeEnum.DEVICE_IS_NOT_EXISTS.getCode(),
                    "设备不存在");
        }
        if (currentDevice.getUserProfileID() != null &&
                devicePO.getUserProfileID() != null &&
                !currentDevice.getUserProfileID().equals(devicePO.getUserProfileID())) {
            ErrorStatus errorStatus = ErrorStatus.PRIVILEGE_INVALID_USER;
            throw new RestException(HttpStatus.CLIENT_ERROR_FORBIDDEN, errorStatus.getErrorCode(),
                    errorStatus.getResId());
        }
        if(DeviceType.isHardDevice(devicePO.getType())) {
            updateDeviceConfig(devicePO, configs);
        } else {
            userConfigService.applyUserConfig(devicePO.getUserProfileID(), configs, devicePO.getType());
        }
        deviceMessageExecutor.execute(() -> {
            UserDevice device = deviceService.getUserDeviceByNemoId(deviceId);
            oceanCollectionService.deviceConfigUpdate(device);
        });

    }

    @Override
    public Map<String, String> getCombinedH323Config(GwDevice gwDevicePO) {
        GwDevice.GwType type = gwDevicePO.getType();
        int deviceType;
        switch (type) {
            case H323:
                deviceType = DeviceType.GW_H323.getValue();
                break;
            case HYGW:
                deviceType = DeviceType.GW_H323_HYGW.getValue();
                break;
            default:
                deviceType = 0;
        }
        Map<String, Map<String, String>> allConfigs = new HashMap<>(defaultConfigService.getDefaultConfigByType(deviceType, deviceType));
        try {
            return ConfigUtils.combinedConfig(allConfigs, deviceType);
        } catch (JsonProcessingException e) {
            throw new ServiceException("Cant access db for config of nemo", e, ErrorStatus.INTERNAL_ERROR);
        }
    }

    @Override
    public R<BatchUpdateDeviceConfigResponseDTO<DeviceConfigUpdateWithSn>> batchUpdateDeviceConfig(List<DeviceConfigUpdateWithSn> deviceConfigList) {
        int size = deviceConfigList.size();
        BatchUpdateDeviceConfigResponseDTO<DeviceConfigUpdateWithSn> batchUpdateDeviceConfigResponseDTO = new BatchUpdateDeviceConfigResponseDTO<>();
        //获取当前对象的代理对象,事务使用
        DeviceConfigService proxy = (DeviceConfigService) AopContext.currentProxy();
        if (size > 20) {
            //批量处理
            CompletableFuture.allOf(deviceConfigList.stream().map(deviceConfigUpdateWithSn -> CompletableFuture.runAsync(() -> {
                String sn = deviceConfigUpdateWithSn.getSn();
                if (StringUtils.isBlank(sn)) {
                    batchUpdateDeviceConfigResponseDTO.getFailedList().add(deviceConfigUpdateWithSn);
                    return;
                }
                //查询设备id
                UserDevice userDevice = userDeviceMapper.selectOne(new LambdaQueryWrapper<UserDevice>().eq(UserDevice::getDeviceSN, sn)
                        //过期时间大于当前时间
                        .gt(UserDevice::getExpirationTime, TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()))
                        //只取出一个
                        .last("limit 1")
                );
                if (userDevice == null) {
                    batchUpdateDeviceConfigResponseDTO.getFailedList().add(deviceConfigUpdateWithSn);
                    return;
                }
                List<DeviceConfigUpdate> deviceConfigUpdates = deviceConfigUpdateWithSn.getDeviceConfigUpdates();
                try {
                    //更新终端配置
                    proxy.updateInternalDeviceConfig(deviceConfigUpdates.toArray(new DeviceConfigUpdate[0]), userDevice.getId());
                    batchUpdateDeviceConfigResponseDTO.getSuccessList().add(deviceConfigUpdateWithSn);
                } catch (Exception e) {
                    log.error("batchUpdateDeviceConfig error", e);
                    batchUpdateDeviceConfigResponseDTO.getFailedList().add(deviceConfigUpdateWithSn);
                }
            }, threadPoolTaskExecutor)).toArray(CompletableFuture[]::new)).join();
        }else {
            //同步处理
            for (DeviceConfigUpdateWithSn deviceConfigUpdateWithSn : deviceConfigList) {
                String sn = deviceConfigUpdateWithSn.getSn();
                if (StringUtils.isBlank(sn)) {
                    batchUpdateDeviceConfigResponseDTO.getFailedList().add(deviceConfigUpdateWithSn);
                    continue;
                }
                //查询设备id
                UserDevice userDevice = userDeviceMapper.selectOne(new LambdaQueryWrapper<UserDevice>().eq(UserDevice::getDeviceSN, sn)
                        //过期时间大于当前时间
                        .gt(UserDevice::getExpirationTime, TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()))
                        //只取出一个
                        .last("limit 1")
                );
                if (userDevice == null) {
                    batchUpdateDeviceConfigResponseDTO.getFailedList().add(deviceConfigUpdateWithSn);
                    continue;
                }

                List<DeviceConfigUpdate> deviceConfigUpdates = deviceConfigUpdateWithSn.getDeviceConfigUpdates();

                try {
                    //更新终端配置
                    proxy.updateInternalDeviceConfig(deviceConfigUpdates.toArray(new DeviceConfigUpdate[0]), userDevice.getId());
                    batchUpdateDeviceConfigResponseDTO.getSuccessList().add(deviceConfigUpdateWithSn);
                } catch (Exception e) {
                    log.error("batchUpdateDeviceConfig error", e);
                    batchUpdateDeviceConfigResponseDTO.getFailedList().add(deviceConfigUpdateWithSn);
                }
            }
        }
        return R.ok(batchUpdateDeviceConfigResponseDTO);
    }

    public void updateDeviceConfig(UserDevice devicePO, Map<String, String> configs) {
        long deviceId = devicePO.getId();
        Map<String,String> device4kConfigs = new HashMap<>();
        for(Map.Entry<String, String> entry : configs.entrySet()) {
            log.debug("Change config:{} of device:{} to:{}" ,entry.getKey() ,deviceId ,entry.getValue());
            if (Configs.configsWithClientName.contains(entry.getKey())) {
                nemoConfigService.applyJsonValueConfig(entry.getValue(), deviceId, entry.getKey());
            }else if(Configs.DEVICE_4K_CONFIG_NAME.contains(entry.getKey())){
                device4kConfigs.put(entry.getKey(),entry.getValue());
            } else {
                applyNemoConfig(deviceId, entry.getKey(), entry.getValue(), Long.MAX_VALUE, null);
            }
        }
        if(device4kConfigs.size() > 0){
            rechargeConfigService.applyDevice4kConfig(devicePO,device4kConfigs);
        }
    }

    private void addDeviceConfig(Map<String, Map<String, String>> allConfigs, UserDevice userDevice) {
        List<BaseConfig> baseConfigPOS = deviceConfigMapper.getDeviceConfig(userDevice.getId());
        for (BaseConfig baseConfig : baseConfigPOS) {
            baseConfig.addToConfigs(allConfigs);
        }
    }

    private void addSpecialConfig(Map<String, Map<String, String>> allConfigs, UserDevice device) {
        List<SpecialConfig> specialConfigPOS = specialConfigService.getSpecialConfig(device.getDeviceSN());
        for (SpecialConfig specialConfig : specialConfigPOS) {
            specialConfig.addToConfigs(allConfigs);
        }
    }

    private void addNemoDefaultVersionConfigByVersion(Map<String, Map<String, String>> allConfigs, UserDevice userDevice) {

        long deviceFirstBindTime = deviceService.getNemoFirstBindTime(userDevice.getDeviceSN());
        if (deviceFirstBindTime <= 0) {
            deviceFirstBindTime = userDevice.getBindTimestamp();
        }
        DeviceSoftVersion softVersionPO = deviceSoftVersionService.getDeviceVersionBySn(userDevice.getDeviceSN());
        List<NemoDefaultVersionConfig> defaultVersionConfigPOS;
        if (softVersionPO != null && StringUtils.isNotEmpty(softVersionPO.getCurrentSoftVersion())) {
            log.info("current nemo soft version :" + softVersionPO.getCurrentSoftVersion());
            defaultVersionConfigPOS = defaultConfigService.getNemoDefaultConfigBytype(userDevice.getType());
            if (defaultVersionConfigPOS != null && defaultVersionConfigPOS.size() > 0) {
                for (NemoDefaultVersionConfig defaultVersionConfigPO : defaultVersionConfigPOS) {
                    if (softVersionPO.getCurrentSoftVersion().compareTo((defaultVersionConfigPO.getConfigVersionList())) >= 0) {
                        log.info("add default version config : " + defaultVersionConfigPO.toString());
                        defaultVersionConfigPO.addToConfigs(allConfigs);
                    }

                }
            }
            defaultVersionConfigPOS = defaultConfigService.getNemoDefaultConfigBytype(userDevice.getSubType());
            if (defaultVersionConfigPOS != null && defaultVersionConfigPOS.size() > 0) {
                for (NemoDefaultVersionConfig defaultVersionConfigPO : defaultVersionConfigPOS) {

                    if (softVersionPO.getCurrentSoftVersion().compareTo((defaultVersionConfigPO.getConfigVersionList())) >= 0) {
                        log.info("add default version config : " + defaultVersionConfigPO.toString());
                        defaultVersionConfigPO.addToConfigs(allConfigs);
                    }
                }
            }
            log.info("get user device current version config succ :" + userDevice.getDeviceSN());
        }
        // 根据当前请求接口的版本号去获取
        AppDetailInfo currentClientInfo = RestApiContext.getCurrentClientInfo();
        if (currentClientInfo != null && StringUtils.isNotBlank(currentClientInfo.getAv())) {
            log.info("GetDefaultVersionConfigs: userDevice={} header av={}", userDevice.getDeviceSN(), currentClientInfo.getAv());
            try {
                int av = Integer.parseInt(currentClientInfo.getAv());
                List<NemoDefaultVersionConfig> defaultVersionConfigsForType = defaultConfigService.getDefaultVersionConfigByTypeAndVersion(userDevice.getType(), av);
                if (!CollectionUtils.isEmpty(defaultVersionConfigsForType)) {
                    defaultVersionConfigsForType.forEach((item) -> {
                                log.info("add default version config : " + item.toString());
                                item.addToConfigs(allConfigs);
                            }
                    );
                }
                List<NemoDefaultVersionConfig> defaultVersionConfigsForSubType = defaultConfigService.getDefaultVersionConfigByTypeAndVersion(userDevice.getSubType(), av);
                if (!CollectionUtils.isEmpty(defaultVersionConfigsForSubType)) {
                    defaultVersionConfigsForSubType.forEach((item) -> {
                                log.info("add default version config : " + item.toString());
                                item.addToConfigs(allConfigs);
                            }
                    );
                }
            } catch (NumberFormatException e) {
                log.error("NumberFormatException for {}", currentClientInfo.getAv(), e);
            }
        }

        //二维码处理逻辑
        Map<String, String> commonConfig = allConfigs.get(Configs.COMMON_CONFIG_KEY);
        boolean showJoinEntQRCode = false;

        if (StringUtils.isNotBlank(commonConfig.get(ME_NEMO_SHOW_JOIN_ENT_QRCODE))) {
            if (StringUtils.isBlank(userDevice.getEnterpriseId()) && "true".equals(commonConfig.get(ME_NEMO_SHOW_JOIN_ENT_QRCODE)) && deviceFirstBindTime > startShowJoinEnterpriseCodeTime) {
                showJoinEntQRCode = true;
            }
            commonConfig.put(ME_NEMO_SHOW_JOIN_ENT_QRCODE, String.valueOf(showJoinEntQRCode));
        }
    }

    private void addDefaultConfig(Map<String, Map<String, String>> allConfigs, UserDevice userDevice) {

        if (userDevice.getSubType() <= DeviceSubType.NEMO.getValue()) {
            allConfigs.putAll(defaultConfigService.getDefaultNemoConfig(DeviceSnParse.getProductFamilyBySn(userDevice.getDeviceSN()).name(), userDevice.getSubType()));
        } else {
            allConfigs.putAll(defaultConfigService.getDefaultConfigByType(userDevice.getSubType(), userDevice.getType()));
        }
    }

    private void addEnterpriseDefaultConfig(Map<String, Map<String, String>> allConfigs, UserDevice userDevice) {
        if (deviceService.isEnterpriseNemo(userDevice.getDeviceSN())) {
            Map<String, Map<String, String>> configs = defaultConfigService.getEnterpriseConfig(userDevice.getSubType(), userDevice.getType());
            if (configs != null) {
                ConfigUtils.putAll(allConfigs, configs);
            }
        }
    }

    private void addEnterpriseProfileConfig(Map<String, Map<String, String>> allConfigs, UserDevice userDevice) {
        if (!PrivateCloud.isPrivate()) {
            List<EnterpriseNemoConfig> enterpriseNemoConfigPOS;
            if (StringUtils.isNotEmpty(userDevice.getEnterpriseId())) {
                enterpriseNemoConfigPOS = enterpriseNemoConfigService.getEnterpriseProfileConfig(userDevice.getEnterpriseId(), userDevice.getType());
                for (EnterpriseNemoConfig enterpriseNemoConfigPO : enterpriseNemoConfigPOS) {
                    enterpriseNemoConfigPO.addToConfigs(allConfigs);
                }
                enterpriseNemoConfigPOS = enterpriseNemoConfigService.getEnterpriseProfileConfig(userDevice.getEnterpriseId(), userDevice.getSubType());
                for (EnterpriseNemoConfig enterpriseNemoConfigPO : enterpriseNemoConfigPOS) {
                    enterpriseNemoConfigPO.addToConfigs(allConfigs);
                }
            }
            enterpriseNemoConfigPOS = enterpriseNemoConfigService.getEnterpriseDeviceConfig(userDevice.getDeviceSN(), userDevice.getType());
            for (EnterpriseNemoConfig enterpriseNemoConfigPO : enterpriseNemoConfigPOS) {
                enterpriseNemoConfigPO.addToConfigs(allConfigs);
            }
            enterpriseNemoConfigPOS = enterpriseNemoConfigService.getEnterpriseDeviceConfig(userDevice.getDeviceSN(), userDevice.getSubType());
            for (EnterpriseNemoConfig enterpriseNemoConfigPO : enterpriseNemoConfigPOS) {
                enterpriseNemoConfigPO.addToConfigs(allConfigs);
            }
        } else {
            List<EnterpriseNemoConfig> enterpriseNemoConfigPOS = enterpriseNemoConfigService.getEnterpriseDeviceConfigByEnterpriseId(
                    userDevice.getEnterpriseId(), userDevice.getType());
            for (EnterpriseNemoConfig enterpriseNemoConfigPO : enterpriseNemoConfigPOS) {
                enterpriseNemoConfigPO.addToConfigs(allConfigs);
            }
            enterpriseNemoConfigPOS = enterpriseNemoConfigService.getEnterpriseDeviceConfigByEnterpriseId(userDevice.getEnterpriseId(), userDevice.getSubType());
            for (EnterpriseNemoConfig enterpriseNemoConfigPO : enterpriseNemoConfigPOS) {
                enterpriseNemoConfigPO.addToConfigs(allConfigs);
            }
        }
    }

    private Map<String, String> getDefaultClientConfig(String deviceSN, int deviceType, String clientConfigName, int deviceSubType) {
        Map<String, String> configs;
        if (deviceType == DeviceType.HARD.getValue() && deviceSubType <= DeviceSubType.NEMO.getValue()) {
            configs = defaultConfigService.getDefaultNemoConfig(deviceSN, deviceSubType).get(clientConfigName);
        } else {
            configs = defaultConfigService.getDefaultConfigByType(deviceSubType, deviceType).get(clientConfigName);
        }
        return configs == null ? new HashMap<>() : configs;
    }

    private Map<String, String> getDefaultEnterpriseConfig(String clientConfigName, int subType, String deviceSN, int deviceType) {
        Map<String, String> configs = null;
        if (deviceService.isEnterpriseNemo(deviceSN)) {
            Map<String, Map<String, String>> enterpriseNemoConfig = defaultConfigService.getEnterpriseConfig(subType, deviceType);
            if (enterpriseNemoConfig != null) {
                configs = enterpriseNemoConfig.get(clientConfigName);
            }
        }
        return configs == null ? new HashMap<>() : configs;
    }

    private Map<String, String> getEnterpriseNemoClientConfig(String deviceSN, String clientConfigName) {
        Map<String, String> configs = new HashMap<>();
        List<EnterpriseNemoConfig> enterpriseNemoConfigPOS = enterpriseNemoConfigService.getEnterpriseDeviceClientConfig(deviceSN, clientConfigName);
        for (EnterpriseNemoConfig enterpriseNemoConfigPO : enterpriseNemoConfigPOS) {
            configs.put(enterpriseNemoConfigPO.getConfigName(), enterpriseNemoConfigPO.getConfigValue());
        }
        return configs;
    }

    private Map<String, String> getNemoSpecialClientConfig(String deviceSN, String clientConfigName) {
        Map<String, String> configs = new HashMap<>();
        List<SpecialConfig> specialConfigPOS = specialConfigService.getSpecialClientConfig(deviceSN, clientConfigName);
        for (SpecialConfig specialConfigPO : specialConfigPOS) {
            configs.put(specialConfigPO.getConfigName(), specialConfigPO.getConfigValue());
        }
        return configs;
    }

    private Map<String, String> getDeviceClientConfig(long deviceId, String clientConfigName) {
        Map<String, String> configs = new HashMap<>();
        List<DeviceConfig> deviceConfigPOS = getDeviceClientConfigs(deviceId, clientConfigName);
        for (DeviceConfig deviceConfig : deviceConfigPOS) {
            configs.put(deviceConfig.getConfigName(), deviceConfig.getConfigValue());
        }
        return configs;
    }

    private String getSingleValueConfig(UserDevice userDevice, String configName) {
        String configValue = getNemoConfig(userDevice.getId(), configName);
        if (configValue == null) {
            configValue = getNemoSpecialConfig(userDevice.getDeviceSN(), configName);
        }

        if (configValue == null) {
            configValue = getDeptConfig(userDevice, configName);
        }

        if (configValue == null) {
            configValue = getEnterpriseNemoConfig(userDevice.getDeviceSN(), configName, userDevice.getSubType());
        }

        if (configValue == null) {
            configValue = getEnterpriseNemoConfig(userDevice.getDeviceSN(), configName, userDevice.getType());
        }

        if (configValue == null) {
            configValue = getEnterpriseDeviceonfigByTypeAndSubType(userDevice, configName);
        }

        if (configValue == null) {
            Map<String, String> configs = getEnterpriseNemoClientConfig(userDevice.getDeviceSN(), DeviceConfig.COMMON_CONFIG_KEY);
            configValue = configs.get(configName);
        }

        if (configValue == null) {
            Map<String, String> configs = getDefaultEnterpriseConfig(DeviceConfig.COMMON_CONFIG_KEY, userDevice.getSubType(), userDevice.getDeviceSN(), userDevice.getType());
            configValue = configs.get(configName);
        }

        if (configValue == null) {
            Map<String, String> configs = getDefaultClientConfig(userDevice.getDeviceSN(), userDevice.getType(), DeviceConfig.COMMON_CONFIG_KEY, userDevice.getSubType());
            configValue = configs.get(configName);
        }
        return configValue;
    }

    private String getDeptConfig(UserDevice userDevice, String configName) {
        if (!commonVariableService.isEnableDeptConfig()){
            log.info("disable dept config");
            return null;
        }
        String deptId="-1";
        if (DeviceType.isHardDevice(userDevice.getType())){
            deptId = internalApiProxy.getOneLevelDeptByDeviceIdFromContact(userDevice.getEnterpriseId(), userDevice.getId());
        } else {
            deptId = internalApiProxy.getOneLevelDeptByUserProfileIdFromContact(userDevice.getEnterpriseId(), userDevice.getUserProfileID());
        }
        log.info("device:{}, deptId:{}", userDevice.getId(), deptId);


        List<DeptConfigPO> deptConfigList = deptConfigService.getSourceDeptConfig(userDevice.getEnterpriseId(), deptId);
        if (CollectionUtil.isNotEmpty(deptConfigList)){
            return deptConfigList.stream()
                    .filter(configItem -> StringUtils.equalsIgnoreCase(configItem.getConfigName(), configName))
                    .findFirst()
                    .map(config -> config.getConfigValue())
                    .orElse(null);
        }
        return null;
    }

    private String getEnterpriseDeviceonfigByTypeAndSubType(UserDevice device, String configName) {
        EnterpriseNemoConfig enterpriseNemoConfig;
        log.info("get device enterprise nemoconfig : " + device.toString());
        enterpriseNemoConfig = enterpriseNemoConfigService.getEntDeviceConfigBySubType(device.getEnterpriseId(), configName, device.getSubType());
        if (enterpriseNemoConfig == null) {
            enterpriseNemoConfig = enterpriseNemoConfigService.getEntDeviceConfigByType(device.getEnterpriseId(), configName, device.getType());
        }
        return enterpriseNemoConfig != null ? enterpriseNemoConfig.getConfigValue() : null;
    }

    private String getEnterpriseNemoConfig(String deviceSN, String configName, int deviceType) {
        EnterpriseNemoConfig enterpriseNemoConfigPO = enterpriseNemoConfigService.getEnterpriseDeviceConfig(deviceSN, configName, deviceType);
        return enterpriseNemoConfigPO != null ? enterpriseNemoConfigPO.getConfigValue() : null;
    }

    private String getNemoSpecialConfig(String nemoSN, String configName) {
        SpecialConfig specialConfig = specialConfigService.getDeviceSpecialConfig(nemoSN, configName);
        return specialConfig != null ? specialConfig.getConfigValue() : null;
    }

    /**
     * get platform config [libra_default_platform_config]
     *
     * @param device
     * @param allConfigs
     */
    private void addPlatformConfig(UserDevice device, Map<String, Map<String, String>> allConfigs) {
        AppDetailInfo currentClientInfo = RestApiContext.getCurrentClientInfo();
        if (currentClientInfo != null && StringUtils.isNotBlank(currentClientInfo.getPl())) {
            platformConfigService.addPlatformConfig(currentClientInfo.getPl(), device.getType(), allConfigs);
            platformConfigService.addPlatformConfig(currentClientInfo.getPl(), device.getSubType(), allConfigs);
        }
    }
}