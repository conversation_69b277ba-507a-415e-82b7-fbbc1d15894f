package com.xylink.configserver.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xylink.configserver.data.model.UserConfig;

import java.util.Map;

public interface UserConfigService extends IService<UserConfig> {

    Map<String, String> getCombinedConfig(long userProfileID, Integer deviceType);

    String getUserConfig(long userProfileID, String configName);

    Map<String, Map<String, String>> getAllConfigs(long userProfileId, int deviceType);

    void applyUserConfig(long userId, Map<String, String> config, int deviceType);
}
