package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xylink.configserver.data.model.CustomizeFeature;
import com.xylink.configserver.data.model.ThirdAppFeature;
import com.xylink.configserver.mapper.ThirdAppFeatureMapper;
import com.xylink.configserver.service.CustomizeFeatureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CustomizeFeatureServiceImpl implements CustomizeFeatureService {

    private volatile List<ThirdAppFeature> allThirdAppFeatures;

    @Autowired
    ThirdAppFeatureMapper thirdAppFeatureMapper;

    @Override
    public List<ThirdAppFeature> getAllThirdAppFeatures() {
        return allThirdAppFeatures;
    }

    @Override
    public CustomizeFeature getCustomizeFeatureById(String id) {
        return thirdAppFeatureMapper.getCustomizeFeatureById(id);
    }

    @Scheduled(fixedRate=10*60*1000,initialDelay = 100)
    void refreshThirdAppFeatures() {
        List<ThirdAppFeature> thirdAppFeaturePOs = null;
        try {
            thirdAppFeaturePOs = thirdAppFeatureMapper.getAll();
            if(thirdAppFeaturePOs != null) {
                allThirdAppFeatures = thirdAppFeaturePOs;
            }
        } catch (DataAccessException e) {
            log.error("Failed to get all third app features", e);
        }
    }
}
