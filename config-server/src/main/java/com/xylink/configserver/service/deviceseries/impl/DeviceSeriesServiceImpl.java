package com.xylink.configserver.service.deviceseries.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xylink.configserver.data.model.deviceseries.*;
import com.xylink.configserver.mapper.deviceseries.DeviceSeriesMapper;
import com.xylink.configserver.service.deviceseries.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021-01-12 15:36
 */
@Service
public class DeviceSeriesServiceImpl extends ServiceImpl<DeviceSeriesMapper, DeviceSeriesEntity> implements DeviceSeriesService {
    @Resource
    private DeviceSubtypeSeriesConfigDictionaryService deviceSubtypeSeriesConfigDictionaryService;
    @Resource
    private DeviceSubtypeSeriesConfigService deviceSubtypeSeriesConfigService;
    @Resource
    private DeviceSeriesSubTypeService deviceSeriesSubTypeService;

    @Override
    public DeviceSeriesDto getDeviceSeriesDto(long id) {
        DeviceSeriesEntity series = this.getById(id);
        Objects.requireNonNull(series, id + " is not exist.");
        DeviceSeriesDto dto = new DeviceSeriesDto();
        dto.setId(series.getId());
        dto.setSeriesName(series.getSeriesName());
        dto.setSpecial(series.getSpecial());
        // 获取系列包括的设备类型
        List<DeviceSeriesSubtypeEntity> subtypes = deviceSeriesSubTypeService.getBySeriesId(series.getId());
        if (!CollectionUtils.isEmpty(subtypes)) {
            dto.setSubtypes(subtypes.stream().map(DeviceSeriesSubtypeEntity::getSubtype).collect(Collectors.toList()));
        }
        List<DeviceSubtypeSeriesConfigDictionaryEntity> configDictionary = deviceSubtypeSeriesConfigDictionaryService.getSeriesConfigDictionary();
        if (!CollectionUtils.isEmpty(configDictionary)) {
            List<DeviceSubtypeSeriesConfigEntity> selectedConfig = deviceSubtypeSeriesConfigService.getSeriesConfig(dto.getId());
            dto.setConfigs(ConfigDictionaryUtils.parseTree(configDictionary, selectedConfig));
        }
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateSeries(DeviceSeriesDto deviceSeriesDto) {
        DeviceSeriesEntity deviceSeriesEntity = new DeviceSeriesEntity();
        if (deviceSeriesDto.getId() != null) {
            deviceSubtypeSeriesConfigService.deleteSeriesConfigBySeriesId(deviceSeriesDto.getId());
            deviceSeriesEntity.setId(deviceSeriesDto.getId());
        }
        deviceSeriesEntity.setSeriesName(deviceSeriesDto.getSeriesName());
        this.saveOrUpdate(deviceSeriesEntity);
        List<DeviceSubtypeSeriesConfigEntity> deviceSeriesEntities = of(deviceSeriesEntity.getId(), deviceSeriesDto.getConfigs());
        if (!CollectionUtils.isEmpty(deviceSeriesEntities)) {
            deviceSubtypeSeriesConfigService.saveBatch(deviceSeriesEntities);
        }
        return true;
    }

    @Override
    public List<DeviceSeriesDto> getAllSeries() {
        List<DeviceSeriesEntity> series = this.list();
        if (!CollectionUtils.isEmpty(series)) {
            List<DeviceSeriesSubtypeEntity> seriesSubtypeMapping = deviceSeriesSubTypeService.list();
            List<DeviceSeriesDto> data = new ArrayList<>(series.size());
            series.forEach(item ->
                    data.add(buildDeviceSeriesDto(seriesSubtypeMapping, item))
            );
            return data;
        }
        return Collections.emptyList();
    }

    private DeviceSeriesDto buildDeviceSeriesDto(List<DeviceSeriesSubtypeEntity> seriesSubtypeMapping, DeviceSeriesEntity item) {
        DeviceSeriesDto dto = new DeviceSeriesDto();
        dto.setId(item.getId());
        dto.setSeriesName(item.getSeriesName());
        // 查询包含的终端类型
        if (!CollectionUtils.isEmpty(seriesSubtypeMapping)) {
            dto.setSubtypes(seriesSubtypeMapping.stream().filter(mapped -> item.getId().equals(mapped.getSeriesId())).map(DeviceSeriesSubtypeEntity::getSubtype).collect(Collectors.toList()));
        }
        return dto;
    }

    private List<DeviceSubtypeSeriesConfigEntity> of(long seriesId, List<DeviceSeriesSubtypeConfigsDto> deviceSeriesSubtypeConfigsDto) {
        if (CollectionUtils.isEmpty(deviceSeriesSubtypeConfigsDto)) {
            return Collections.emptyList();
        }
        return deviceSeriesSubtypeConfigsDto.stream().map(item -> {
            DeviceSubtypeSeriesConfigEntity deviceSubtypeSeriesConfigEntity = new DeviceSubtypeSeriesConfigEntity();
            deviceSubtypeSeriesConfigEntity.setSeriesId(seriesId);
            deviceSubtypeSeriesConfigEntity.setConfigId(item.getConfigId());
            deviceSubtypeSeriesConfigEntity.setPlatform(PlatformEnum.BUFFET_MANAGER.name());
            deviceSubtypeSeriesConfigEntity.setType(DeviceGroupEnum.SERIES.name());
            return deviceSubtypeSeriesConfigEntity;
        }).collect(Collectors.toList());
    }
}
