package com.xylink.configserver.service.impl;

import com.xylink.configserver.data.model.DeviceInfo;
import com.xylink.configserver.data.model.FeatureProvision;
import com.xylink.configserver.data.model.FeatureRequirement;
import com.xylink.configserver.data.model.FeatureRequirementList;
import com.xylink.configserver.service.FeatureListStore;
import com.xylink.configserver.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
public class FeatureListStoreImpl implements FeatureListStore {

    private volatile AtomicReference<FeatureRequirementList> featureList = new AtomicReference<>();
    private volatile AtomicReference<List<FeatureRequirement>> appFeatureList = new AtomicReference<> ();
    private volatile AtomicReference<List<FeatureRequirement>> pcFeatureList = new AtomicReference<> ();

    private static final String FEATURE_TYPE_ALL = "all";
    private static final String FEATURE_TYPE_APP = "app";
    private static final String FEATURE_TYPE_PC = "pc";



    @PostConstruct
    public void initialize() {
        new Thread(new FeatureListParser()).start();
    }

    @Override
    public List<FeatureProvision> getDeviceFeatureProvision(DeviceInfo device) {
        FeatureRequirementList features = featureList.get();
        List<FeatureProvision> featuresProvision = new ArrayList<FeatureProvision>();
        if(features != null && features.getFeatures() != null) {
            for(FeatureRequirement feature : features.getFeatures()) {
                if(FEATURE_TYPE_APP.equals(feature.getType()) || FEATURE_TYPE_PC.equals(feature.getType())) {
                    continue;
                }
                FeatureProvision provision = new FeatureProvision(feature.getFeatureName(), true, feature.isReboot());
                featuresProvision.add(provision);
                boolean enable = true;
                if(feature.getAllowedSNs() != null && feature.getAllowedSNs().size() > 0) {
                    String deviceSN = device.getSn() != null ? device.getSn().trim().toUpperCase() : null;
                    enable = feature.getAllowedSNs().contains(deviceSN);
                }
                if(!enable) {
                    provision.setEnable(false);
                    continue;
                }
                //compare softversion
                enable = compareVersion(device.getSoftVersion(), feature.getSoftVersion()) >= 0;
                if(!enable) {
                    provision.setEnable(false);
                    continue;
                }
                //compare hard version
                enable = compareVersion(device.getHardVersion(), feature.getHardVersion()) >= 0;
                if(!enable) {
                    provision.setEnable(false);
                    continue;
                }
                //compare OS
                enable = compareVersion(device.getOS(), feature.getOS()) >= 0;
                if(!enable) {
                    provision.setEnable(false);
                    continue;
                }
            }
        }
        return featuresProvision;
    }

    private List<FeatureProvision> getFeaturesByManuAndModel(String manu, String model, List<FeatureRequirement> frs) {
        List<FeatureProvision> features = new ArrayList<> ();
        if(frs != null) {
            for(FeatureRequirement fr : frs) {
                FeatureProvision feature = new FeatureProvision(fr.getFeatureName(), true, fr.isReboot());
                if(fr.getValue() != null) {
                    feature.setValue(fr.getValue());
                } else {
                    feature.setValue("true");
                }
                boolean add = false;
                if(fr.getModel() == null || fr.getModel().trim().isEmpty()) {
                    add = true;
                } else {
                    String[] mms = fr.getModel().split(";");
                    for(String mm : mms) {
                        String[] manuandmodel = mm.split(":");
                        String manur = null;
                        String modelr = null;
                        if(manuandmodel.length > 1) {
                            manur = manuandmodel[0].trim();
                            modelr = manuandmodel[1].trim();
                        } else {
                            modelr = manuandmodel[0];
                        }

                        if(manu != null && model != null) {
                            if(manu.equalsIgnoreCase(manur) && model.toLowerCase().startsWith(modelr.toLowerCase())){
                                add = true;
                                break;
                            }
                        } else if(model != null && model.toLowerCase().startsWith(modelr.toLowerCase())) {
                            add = true;
                            break;
                        }
                    }
                }

                if(add) {
                    features.add(feature);
                }
            }
        }
        return features;
    }

    @Override
    public List<FeatureProvision> getPCFeatureProvision(String manufacture, String model) {
        List<FeatureRequirement> frs = pcFeatureList.get();
        return getFeaturesByManuAndModel(manufacture, model, frs);
    }

    @Override
    public List<FeatureProvision> getAppFeatureProvision(String manu, String model) {
        List<FeatureRequirement> frs = appFeatureList.get();
        return getFeaturesByManuAndModel(manu, model, frs);
    }

    private class FeatureListParser implements Runnable {

        @Override
        public void run() {
            InputStream featuresConfigFile = this.getClass().getClassLoader().getResourceAsStream("featureList.json");
            FeatureRequirementList info = FileUtil.parseJsonFile(featuresConfigFile, FeatureRequirementList.class);


            if(info != null) {
                featureList.set(info);
                List<FeatureRequirement> features = new ArrayList<FeatureRequirement> ();
                List<FeatureRequirement> pcfeatures = new ArrayList<FeatureRequirement> ();
                for(FeatureRequirement fr : info.getFeatures()) {
                    if(FEATURE_TYPE_ALL.equalsIgnoreCase(fr.getType()) || FEATURE_TYPE_APP.equalsIgnoreCase(fr.getType())) {
                        features.add(fr);
                    }
                    if(FEATURE_TYPE_ALL.equalsIgnoreCase(fr.getType()) || FEATURE_TYPE_PC.equalsIgnoreCase(fr.getType())) {
                        pcfeatures.add(fr);
                    }
                }
                appFeatureList.set(features);
                pcFeatureList.set(pcfeatures);
            }
        }

    }

    //soft/os version format: 1.1.1-1234
    //hard version format: 1.1.1
    private int compareVersion(String version1, String version2) {
        if(StringUtils.isEmpty(version1)) {
            return -1;
        }
        if(StringUtils.isEmpty(version2)) {
            return 1;
        }
        String[] versionBuild1 = version1.split("-");
        String[] versionBuild2 = version2.split("-");
        if(versionBuild1.length < 1) {
            return -1;
        }
        if(versionBuild2.length < 1) {
            return 1;
        }
        String[] versions1 = versionBuild1[0].split("\\.");
        String[] versions2 = versionBuild2[0].split("\\.");
        if(versions1.length < 2) {
            log.warn("Version is not in correct format, maybe debug: " + version1);
            return 1;
        }
        int result = 0;
        int i = 0;
        while(i < versions1.length && i < versions2.length){
            try {
                result = Integer.parseInt(versions1[i]) - Integer.parseInt(versions2[i]);
                if(result != 0) {
                    break;
                }
            } catch(Exception e) {
                log.error("Failed t parse integer, version1: " + version1 + " version2: " + version2);
            }

            i ++;
        }
        if(result != 0) {
            return result;
        }

        if(i < versions1.length) {
            return 1;
        }
        if(i < versions2.length) {
            return -1;
        }
        if(result == 0) {
            if(versionBuild1.length == 2 && versionBuild2.length == 2) {
                try {
                    result = Integer.parseInt(versionBuild1[1]) - Integer.parseInt(versionBuild2[1]);
                } catch(Exception e) {
                    log.error("Failed t parse integer, version1: " + version1 + " version2: " + version2);
                }

            }
        }
        return result;
    }
}
