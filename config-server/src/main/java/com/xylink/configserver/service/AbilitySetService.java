package com.xylink.configserver.service;

import com.xylink.configserver.data.dto.abilityset.AbilityResultDTO;
import com.xylink.configserver.data.dto.abilityset.DeviceAbilitySetDto;
import com.xylink.configserver.data.dto.abilityset.ServerAbilitySetDto;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/11/18 17:27
 */
public interface AbilitySetService {
    /**
     * 查询服务端所具备能力表
     *
     * @return 服务端能力集合
     */
    List<Long> getServerAbility();

    /**
     * 下发终端与服务端聚合能力集
     *
     * @param deviceAbilitySetDto 终端能力集
     * @return 聚合后能力集
     */
    List<Long> getCombinedAbility(DeviceAbilitySetDto deviceAbilitySetDto);

    AbilityResultDTO getCombinedAbilityStr(DeviceAbilitySetDto deviceAbilitySetDto);

    /**
     * 清除能力表缓存
     */
    void removeAbilityCache();

    /**
     * 清除服务端能力集缓存
     */
    void removeServerAbility();

    void changeServerAbility(List<ServerAbilitySetDto> serverAbilitySetDtoList);
}