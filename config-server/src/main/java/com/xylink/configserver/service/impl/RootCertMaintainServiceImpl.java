package com.xylink.configserver.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xylink.configserver.data.entity.BasicCertInfo;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.data.vo.RootCertListResponse;
import com.xylink.configserver.data.vo.RootCertResponse;
import com.xylink.configserver.exception.RestException;
import com.xylink.configserver.service.BasicCertInfoService;
import com.xylink.configserver.service.RootCertMaintainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * ClassName:RootCertMaintainServiceImpl
 * Package:com.xylink.configserver.service.impl
 * Description:
 *
 * <AUTHOR>
 * @Date 2024/11/26-10:45
 * @Version: v1.0
 */
@Slf4j
@Service
public class RootCertMaintainServiceImpl implements RootCertMaintainService {

    @Autowired
    private BasicCertInfoService basicCertInfoService;

    @Override
    public RootCertListResponse getRootCertList(String sn) {
        List<String> certIds = new ArrayList<>();
        //打印查询处理的证书id
        basicCertInfoService.list(new LambdaQueryWrapper<BasicCertInfo>().eq(BasicCertInfo::getCertState, 0)).forEach(basicCertInfo -> {
            certIds.add(basicCertInfo.getId());
        });
        log.info("RP-RootCertMaintainServiceImpl-getRootCertList:certIds:{}", certIds);
        return new RootCertListResponse(certIds);
    }

    @Override
    public RootCertResponse getRootCert(String certId) {
        BasicCertInfo basicCertInfo = basicCertInfoService.getOne(new LambdaQueryWrapper<BasicCertInfo>().eq(BasicCertInfo::getId, certId));
        RootCertResponse rootCertResponse = new RootCertResponse(basicCertInfo.getId(), basicCertInfo.getCertContent(), basicCertInfo.getCertSign());
        log.info("RP-RootCertMaintainServiceImpl-getRootCert:rootCertResponse:{}", rootCertResponse);
        if (rootCertResponse == null) {
            log.error("RP-RootCertMaintainServiceImpl-getRootCert:rootCertResponse is null");
            throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, "rootCert is null");
        }
        return rootCertResponse;
    }

    @Override
    public List<BasicCertInfo> getRootCertListByPage() {
        List<BasicCertInfo> list = basicCertInfoService.list();
        log.info("RP-RootCertMaintainServiceImpl-getRootCertListByPage:list:{}", list);
        return list;
    }

    @Override
    public BasicCertInfo getRootCertDetail(String certId) {
        BasicCertInfo basicCertInfo = basicCertInfoService.getOne(new LambdaQueryWrapper<BasicCertInfo>().eq(BasicCertInfo::getId, certId));
        return basicCertInfo;
    }

    @Override
    public void addOrUpdateRootCert(BasicCertInfo basicCertInfo) {
        //此处需要检查下如果证书不存在，是保存操作，证书总数是否已经超过5个，超过5个抛出异常
        if (basicCertInfo.getId() == null) {
            List<BasicCertInfo> list = basicCertInfoService.list();
            if (list.size() >= 5) {
                throw new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, "rootCert count is more than 5");
            }
        }

        basicCertInfoService.saveOrUpdate(basicCertInfo);
    }

    @Override
    public void deleteRootCert(String certId) {
        basicCertInfoService.remove(new LambdaQueryWrapper<BasicCertInfo>().eq(BasicCertInfo::getId, certId));
    }
}
