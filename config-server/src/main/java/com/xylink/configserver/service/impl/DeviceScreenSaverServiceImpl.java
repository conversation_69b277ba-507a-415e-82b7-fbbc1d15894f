package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xylink.configserver.data.model.DeviceInfo;
import com.xylink.configserver.data.model.DeviceScreenSaver;
import com.xylink.configserver.data.model.UserDevice;
import com.xylink.configserver.enums.Configs;
import com.xylink.configserver.service.DeviceConfigService;
import com.xylink.configserver.service.DeviceScreenSaverService;
import com.xylink.configserver.service.DeviceService;
import com.xylink.configserver.util.I18nNemoConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class DeviceScreenSaverServiceImpl implements DeviceScreenSaverService {

    @Autowired
    DeviceService deviceService;

    @Autowired
    DeviceConfigService deviceConfigService;

    @Override
    public DeviceScreenSaver getScreenSaver(DeviceInfo deviceInfo) {
        try {
            List<UserDevice> userDevicePO = deviceService.getUserDeviceByDevSn(deviceInfo.getSn());
            if(userDevicePO.size() > 0) {
                UserDevice devicePO = userDevicePO.get(0);
                UserDevice userDevice =  new UserDevice(devicePO.getId(), devicePO.getDeviceSN(), devicePO.getType(),
                        devicePO.getSubType(), devicePO.getUserProfileID());
                userDevice.setEnterpriseId(devicePO.getEnterpriseId());
                String configValue = deviceConfigService.getDeviceConfig(userDevice, Configs.NemoConfig.CAROUSEL_IMAGES);

                return new DeviceScreenSaver(I18nNemoConfig.getLocalizedValue(configValue, deviceInfo.getLocale()));
            }
        } catch (DataAccessException e) {
            log.error("Failed to get device by sn: " + deviceInfo.getSn(), e);
        }
        return new DeviceScreenSaver();
    }
}
