package com.xylink.configserver.service.impl;

import com.xylink.configserver.data.model.*;
import com.xylink.configserver.mapper.LibraServerEndpointConfigMapper;
import com.xylink.configserver.mapper.UserProfileMapper;
import com.xylink.configserver.service.DeviceService;
import com.xylink.configserver.service.LibraServerEndpoinConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @FilePath:com.xylink.configserver.service.impl.LibraServerEndpoinConfigService
 * @Description: TODO
 */
@Slf4j
@Service
public class LibraServerEndpoinConfigServiceImpl implements LibraServerEndpoinConfigService {

    @Autowired
    private LibraServerEndpointConfigMapper libraServerEndpointConfigMapper;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private UserProfileMapper userProfileMapper;

    /**
     * 实际情况是单个添加或更新，接口支持批量，采用遍历方式，不应存在写入压力。
     * @param serverEndpointConfigReq
     */
    @Override
    public void addOrUpdateServerEndpoinConfig(ServerEndpointConfigReq serverEndpointConfigReq) {
        List<Long> deviceIds = serverEndpointConfigReq.getDeviceIds();
        List<Long> userIds = serverEndpointConfigReq.getUserIds();
        if (Objects.nonNull(deviceIds) && CollectionUtils.isNotEmpty(deviceIds)) {
            deviceIds.forEach(deviceId -> {
                LibraServerEndpointConfigEntity endpointConfig = libraServerEndpointConfigMapper.findServerEndpointConfig(0l, deviceId, serverEndpointConfigReq.getConfigName());
                if (Objects.nonNull(endpointConfig)) {
                    endpointConfig.setConfigValue(serverEndpointConfigReq.getConfigValue());
                    libraServerEndpointConfigMapper.updateById(endpointConfig);
                } else {
                    endpointConfig = new LibraServerEndpointConfigEntity();
                    endpointConfig.setConfigName(serverEndpointConfigReq.getConfigName());
                    endpointConfig.setConfigValue(serverEndpointConfigReq.getConfigValue());
                    endpointConfig.setDeviceId(deviceId);
                    libraServerEndpointConfigMapper.insert(endpointConfig);
                }
            });
        }
        if (Objects.nonNull(userIds) && CollectionUtils.isNotEmpty(userIds)) {
            userIds.forEach(userId -> {
                LibraServerEndpointConfigEntity endpointConfig = libraServerEndpointConfigMapper.findServerEndpointConfig(userId, 0l, serverEndpointConfigReq.getConfigName());
                if (Objects.nonNull(endpointConfig)) {
                    endpointConfig.setConfigValue(serverEndpointConfigReq.getConfigValue());
                    libraServerEndpointConfigMapper.updateById(endpointConfig);
                } else {
                    endpointConfig = new LibraServerEndpointConfigEntity();
                    endpointConfig.setConfigName(serverEndpointConfigReq.getConfigName());
                    endpointConfig.setConfigValue(serverEndpointConfigReq.getConfigValue());
                    endpointConfig.setUserProfileId(userId);
                    libraServerEndpointConfigMapper.insert(endpointConfig);
                }
            });
        }
    }

    /**
     * 与相关服务约定，最大暂支持查询100个
     * @param serverEndpointConfigReq
     * @return
     * 如果configValue为-10000，则表示改账户或者终端未配置。
     * {
     *     "userIdConfigs": [
     *         {
     *             "userId": 111,
     *             "configName": "",
     *             "configValue": ""
     *         }
     *     ],
     *     "deviceNumberConfigs": [
     *         {
     *             "deviceNumber": "",
     *             "configName": "",
     *             "configValue": ""
     *         }
     *     ],
     *     "deviceIdConfigs": [
     *         {
     *             "deviceId": 111,
     *             "configName": "",
     *             "configValue": ""
     *         }
     *     ],
     *     "telephoneConfigs": [
     *         {
     *             "telphone": "",
     *             "configName": "",
     *             "configValue": ""
     *         }
     *     ]
     * }
     *
     */
    @Override
    public Map findServerEndpoinConfigs(ServerEndpointConfigReq serverEndpointConfigReq) {
        Map endpoinConfigsRs = new HashMap();
        List<Map> userIdConfigsData = new ArrayList<>();
        List<Map> deviceIdConfigsData = new ArrayList<>();
        List<Map> telephoneConfigsData = new ArrayList<>();
        List<Map> deviceNumberConfigsData = new ArrayList<>();
        List<Long> deviceIds = serverEndpointConfigReq.getDeviceIds();
        List<Long> userIds = serverEndpointConfigReq.getUserIds();
        List<String> telephones = serverEndpointConfigReq.getTelephones();
        List<String> deviceNumbers = serverEndpointConfigReq.getDeviceNumbers();
        if (Objects.nonNull(deviceIds) && CollectionUtils.isNotEmpty(deviceIds)){
            String deviceIdsStr = StringUtils.join(deviceIds,",");
            deviceIdConfigsData = getConfigsData("''",deviceIdsStr,null, deviceIds,null,null,null,null,serverEndpointConfigReq);
        }
        if (Objects.nonNull(userIds) && CollectionUtils.isNotEmpty(userIds)){
            String userIdsStr = StringUtils.join(userIds,",");
            userIdConfigsData = getConfigsData(userIdsStr,"''",userIds, null,null,null,null,null,serverEndpointConfigReq);
        }
        if (Objects.nonNull(telephones) && CollectionUtils.isNotEmpty(telephones)){
            List<UserProfile> userProfileList = deviceService.getUserProfileIds(telephones);
            if (Objects.nonNull(userProfileList) && CollectionUtils.isNotEmpty(userProfileList)){
                List<Long> uIds = userProfileList.stream().map(u -> u.getId()).collect(Collectors.toList());
                Map<Long, String> idPhoneMaps = userProfileList.stream().collect(Collectors.toMap(UserProfile::getId, UserProfile::getCellPhone, (key1, key2) -> key2));
                String userIdsStr = StringUtils.join(uIds,",");
                telephoneConfigsData = getConfigsData(userIdsStr,"''",uIds, null,null,null,telephones,idPhoneMaps,serverEndpointConfigReq);
            }else {
                telephoneConfigsData = telephones.stream().map(telephone -> {
                    Map obj = new HashMap();
                    obj.put("configName",serverEndpointConfigReq.getConfigName());
                    // fix: 手机号查用户，用户不存在，默认返回-20000
                    // 此条件分支仅处理所有手机号均不存在场景，仍需在getConfigsData方法中处理部分存在情况。
                    obj.put("configValue","-20000");
                    obj.put("telephone",telephone);
                    return obj;
                }).collect(Collectors.toList());
            }
        }
        if (Objects.nonNull(deviceNumbers) && CollectionUtils.isNotEmpty(deviceNumbers)){
            List<UserDevice> userDeviceList = deviceService.getDeviceIds(deviceNumbers);
            if (Objects.nonNull(userDeviceList) && CollectionUtils.isNotEmpty(userDeviceList)){
                List<Long> dIds = userDeviceList.stream().map(d -> d.getId()).collect(Collectors.toList());
                Map<Long, String> idNumberMaps = userDeviceList.stream().collect(Collectors.toMap(UserDevice::getId, UserDevice::getNemoNumber, (key1, key2) -> key2));
                String deviceIdsStr = StringUtils.join(dIds,",");
                deviceNumberConfigsData = getConfigsData("''",deviceIdsStr, null, dIds,idNumberMaps,deviceNumbers,null,null,serverEndpointConfigReq);
            }else {
                deviceNumberConfigsData = deviceNumbers.stream().map(deviceNumber -> {
                    Map obj = new HashMap();
                    obj.put("configName",serverEndpointConfigReq.getConfigName());
                    obj.put("configValue","-10000");
                    obj.put("deviceNumber",deviceNumber);
                    return obj;
                }).collect(Collectors.toList());
            }
        }
        endpoinConfigsRs.put("userIdConfigs", userIdConfigsData);
        endpoinConfigsRs.put("deviceIdConfigs", deviceIdConfigsData);
        endpoinConfigsRs.put("telephoneConfigs", telephoneConfigsData);
        endpoinConfigsRs.put("deviceNumberConfigs", deviceNumberConfigsData);
        return endpoinConfigsRs;
    }

    private List<Map> getConfigsData(String userIdsStr, String deviceIdsStr, List<Long> userIds, List<Long> deviceIds, Map<Long, String> idNumberMaps, List<String> deviceNumbers, List<String> telephones, Map<Long, String> idPhoneMaps,ServerEndpointConfigReq serverEndpointConfigReq){

        List<Long> userIdList = new ArrayList<>();
        List<Long> deviceIdList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            userIdList = new ArrayList<>(userIds);
        }
        if (CollectionUtils.isNotEmpty(deviceIds)) {
            deviceIdList = new ArrayList<>(deviceIds);
        }

        List<Map> configsData = configsData = new ArrayList<>();
        try {

            // 20240524 fix：检查用户或手机号是否存在，如果不存在，直接返回-20000
            // 不存在的用户加入结果集
            if (CollectionUtils.isNotEmpty(userIds)) {
                List<UserProfileModel> existedUsers = userProfileMapper.selectBatchIds(userIds);
                List<Long> existedUserIds = existedUsers.stream()
                                                        .map(UserProfileModel::getId)
                                                        .collect(Collectors.toList());
                List<Long> notExistedUserIds = userIds.stream()
                                                      .filter(e -> !existedUserIds.contains(e))
                                                      .collect(Collectors.toList());
                for (Long notExistedUserId : notExistedUserIds) {
                    Map<String, Object> obj = new HashMap<>();
                    obj.put("configName", serverEndpointConfigReq.getConfigName());
                    obj.put("configValue", "-20000");
                    obj.put("userId", notExistedUserId);
                    configsData.add(obj);
                    // 移除不存在用户，后续处理默认值
                    userIds.remove(notExistedUserId);
                }
            }

            // 不存在的手机号加入结果集
            if (CollectionUtils.isNotEmpty(telephones)) {
                List<UserProfileModel> existedPhoneModels = userProfileMapper.getUserProfileByUserPhones(telephones);
                List<String> existedPhones = existedPhoneModels.stream().map(UserProfileModel::getUserPhone)
                                                               .collect(Collectors.toList());
                List<String> notExistedPhones = telephones.stream()
                                                          .filter(e -> !existedPhones.contains(e))
                                                          .collect(Collectors.toList());
                for (String notExistedPhone : notExistedPhones) {
                    Map<String, Object> obj = new HashMap<>();
                    obj.put("configName", serverEndpointConfigReq.getConfigName());
                    obj.put("configValue", "-20000");
                    obj.put("telephone", notExistedPhone);
                    configsData.add(obj);
                    // 移除不存在手机号，后续处理默认值
                    telephones.remove(notExistedPhone);
                }
            }

            List<LibraServerEndpointConfigEntity> libraServerEndpointConfigEntities = libraServerEndpointConfigMapper.findServerEndpointConfigBatch(userIdList,deviceIdList,serverEndpointConfigReq.getConfigName());
            List<Map> result1 = libraServerEndpointConfigEntities.stream().map(entity -> {
                Map obj = new HashMap();
                obj.put("configName",entity.getConfigName());
                obj.put("configValue",entity.getConfigValue());
                if (Objects.nonNull(deviceIds)  && Objects.isNull(telephones) && Objects.isNull(deviceNumbers)){
                    obj.put("deviceId",entity.getDeviceId());
                    deviceIds.remove(entity.getDeviceId());
                }
                if (Objects.nonNull(userIds) && Objects.isNull(telephones) && Objects.isNull(deviceNumbers)){
                    obj.put("userId",entity.getUserProfileId());
                    userIds.remove(entity.getUserProfileId());
                }
                if (Objects.nonNull(deviceNumbers)){
                    obj.put("deviceNumber",idNumberMaps.get(entity.getDeviceId()));
                    deviceNumbers.remove(idNumberMaps.get(entity.getDeviceId()));
                }
                if (Objects.nonNull(telephones)){
                    obj.put("telephone",idPhoneMaps.get(entity.getUserProfileId()));
                    telephones.remove(idPhoneMaps.get(entity.getUserProfileId()));
                }
                return obj;
            }).collect(Collectors.toList());
            configsData.addAll(result1);
            if (Objects.isNull(deviceNumbers) && Objects.isNull(telephones) && Objects.nonNull(deviceIds) && CollectionUtils.isNotEmpty(deviceIds)){
                List<Map> result2 = deviceIds.stream().map(deviceId -> {
                    Map obj = new HashMap();
                    obj.put("configName",serverEndpointConfigReq.getConfigName());
                    obj.put("configValue","-10000");
                    obj.put("deviceId",deviceId);
                    return obj;
                }).collect(Collectors.toList());
                configsData.addAll(result2);
            }
            if (Objects.isNull(deviceNumbers) && Objects.isNull(telephones) && Objects.nonNull(userIds) && CollectionUtils.isNotEmpty(userIds)){
                List<Map> result2 = userIds.stream().map(userId -> {
                    Map obj = new HashMap();
                    obj.put("configName",serverEndpointConfigReq.getConfigName());
                    obj.put("configValue","-10000");
                    obj.put("userId",userId);
                    return obj;
                }).collect(Collectors.toList());
                configsData.addAll(result2);
            }
            if (Objects.nonNull(deviceNumbers) && CollectionUtils.isNotEmpty(deviceNumbers)){
                List<Map> result2 = deviceNumbers.stream().map(deviceNumber -> {
                    Map obj = new HashMap();
                    obj.put("configName",serverEndpointConfigReq.getConfigName());
                    obj.put("configValue","-10000");
                    obj.put("deviceNumber",deviceNumber);
                    return obj;
                }).collect(Collectors.toList());
                configsData.addAll(result2);
            }
            if (Objects.nonNull(telephones) && CollectionUtils.isNotEmpty(telephones)){
                List<Map> result2 = telephones.stream().map(telephone -> {
                    Map obj = new HashMap();
                    obj.put("configName",serverEndpointConfigReq.getConfigName());
                    obj.put("configValue","-10000");
                    obj.put("telephone",telephone);
                    return obj;
                }).collect(Collectors.toList());
                configsData.addAll(result2);
            }
        } catch (Exception e) {
            log.error("getConfigsData error ",e);
        }
        return configsData;
    }

}
