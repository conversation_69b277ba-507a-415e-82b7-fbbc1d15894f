package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.mapper.SpecialNemoFeatureMapper;
import com.xylink.configserver.proxy.InternalApiProxy;
import com.xylink.configserver.service.DeviceService;
import com.xylink.configserver.service.NotificationService;
import com.xylink.configserver.service.SpecialConfigService;
import com.xylink.configserver.service.SpecialNemoFeatureService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.xylink.configserver.util.Constants.EN_NEMOS_WITHOUT_ADMIN;

@Slf4j
@Service
public class SpecialNemoFeatureServiceImpl implements SpecialNemoFeatureService {

    @Autowired
    SpecialNemoFeatureMapper specialNemoFeatureMapper;

    @Autowired
    DeviceService deviceService;

    @Autowired
    SpecialConfigService specialConfigService;

    @Autowired
    NotificationService notificationService;

    @Autowired
    InternalApiProxy internalApiProxy;

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public String addSpecialFeatureNemo(String featureId, String nemoSN,boolean isLogin) {
        log.debug("Add special feature " + featureId + " for nemo: " + nemoSN);
        if(nemoSN == null || nemoSN.trim().isEmpty()) {
            log.error("NemoSn cannot be empty.");
            throw new ServiceException("NemoSNCannotbeEmpty", ErrorStatus.INVALID_PARAMETER);
        }
        SpecialNemoFeature feature = specialNemoFeatureMapper.getFeatureById(featureId);
        if (StringUtils.isNotBlank(featureId) && !featureId.equals(EN_NEMOS_WITHOUT_ADMIN)){
            List<SpecialConfig> specialConfigPOS = specialConfigService.getSpecialConfig(nemoSN);

            Set<SpecialConfig> nemoSpecialFeatures = specialConfigPOS.stream().
                    filter((SpecialConfig specialConfig) -> !specialConfig.getSpecialFeatureId().equals(EN_NEMOS_WITHOUT_ADMIN)).collect((Collectors.toSet()));
            if (nemoSpecialFeatures!= null&&nemoSpecialFeatures.size()>0){
                for (SpecialConfig specialConfigPO : specialConfigPOS){
                    if (specialConfigPO.getSpecialFeatureId().equals(featureId)){
                        throw new ServiceException("current nemo has been added this special feature",ErrorStatus.MP_TOKEN_STATUS_ADD_DEVICE_REPEAT);
                    }
                }
                throw new ServiceException("current nemo has been added  another special feature",ErrorStatus.OPENAPI_INVALID_ENTERPRISE_DEVICESN);
            }
        }


        if(feature != null) {
            String id = addNemoSpecialFeature(featureId, nemoSN);
            UserDevice device = deviceService.getInUseUserHardDeviceByDevSn(nemoSN);
            List<SpecialConfig> configs = specialConfigService.getSpecialConfigsByFeatureById(featureId);
            if (configs!=null && configs.size()>0 && !EN_NEMOS_WITHOUT_ADMIN.equals(featureId)
                    && !isLogin && device != null){
                Map<String, Map<String, String>> allConfigs = new HashMap<>();

                for (SpecialConfig specialConfigPO: configs){
                    specialConfigPO.addToConfigs(allConfigs);
                }
                notificationService.notifySpecialDeviceAddConfigChange(nemoSN,allConfigs);
            }
            List<SpecialNemoCustomizeFeature> cusFeatures = specialNemoFeatureMapper.getBySpecialFeatureId(featureId);
            for(SpecialNemoCustomizeFeature po : cusFeatures) {
                addOrUpdateNemoFeature(nemoSN, po.getCustomizeFeatureId(),
                        po.getFeatureStatus(), po.getTargetNumber(), po.isTrial(), true, po.getHasMainShortCut());
            }
            return id;
        }
        return null;
    }

    private void addOrUpdateNemoFeature(String nemoSN, String featureId, int status, String targetNumber, boolean trial, boolean notify, Boolean mainShortCut) {

        log.info("Update nemo feature " + featureId + " for " + nemoSN + " status is: " + status);
        try {
            NemoFeature nemoFeature = specialNemoFeatureMapper.getNemoFeature(nemoSN, featureId);
            if(nemoFeature == null) {
                nemoFeature = new NemoFeature(nemoSN, featureId);
            }
            nemoFeature.setTrial(trial);
            nemoFeature.setFeatureStatus(status);
            nemoFeature.setTargetNumber(targetNumber);
            nemoFeature.setHasMainShortCut(mainShortCut);

            if (StringUtils.isNotBlank(nemoFeature.getId())){
                specialNemoFeatureMapper.updateNemoFeature(nemoFeature);
            }else {
                specialNemoFeatureMapper.addNemoFeature(nemoFeature);
            }

            if(notify) {
                notifyNemoFeatureChange(nemoSN);
            }

        } catch (DataAccessException e) {
            log.error("Failed to add nemo feature.", e);
            throw new ServiceException("Failed to save nemo feature", ErrorStatus.INTERNAL_DATABASE_ERROR);
        }
    }

    private void notifyNemoFeatureChange(String nemoSN) {
        UserDevice device = deviceService.getInUseUserHardDeviceByDevSn(nemoSN);
        if (device != null) {
            List<CustomizeFeature> features = internalApiProxy.getCustomizeFeatures(device,"");
            notificationService.notifyNemoFeatureChange(features,device);
        }
    }

    private String addNemoSpecialFeature(String featureId, String nemoSN) {
        SpecialFeatureNemo nemo = specialNemoFeatureMapper.getSpecialNemo(featureId, nemoSN, false);
        if(nemo == null) {
            nemo = new SpecialFeatureNemo();
            nemo.setNemoSN(nemoSN);
            nemo.setFeatureId(featureId);
        }
        nemo.setStatus(1);
        return specialNemoFeatureMapper.addSpecialFeatureNemo(nemo);
    }

    @Override
    @DS("master")
    @Transactional(rollbackFor = Exception.class)
    public void disableSpecialFeatureNemo(String featureId, String nemoSN,boolean isLogin) {
        log.debug("disable special feature: " + featureId + " from nemo: " + nemoSN);
        SpecialNemoFeature feature = specialNemoFeatureMapper.getFeatureById(featureId);
        if(feature != null) {
            SpecialFeatureNemo nemo = specialNemoFeatureMapper.getSpecialNemo(featureId, nemoSN, false);
            if(nemo != null) {
                List<SpecialConfig> configs = specialConfigService.getSpecialConfigsByFeatureById(featureId);
                if (configs!=null && configs.size()>0 && !EN_NEMOS_WITHOUT_ADMIN.equals(featureId)
                        && !isLogin){
                    Map<String, Map<String, String>> allConfigs = new HashMap<>();

                    for (SpecialConfig specialConfigPO: configs){
                        specialConfigPO.addToConfigs(allConfigs);
                    }
                    notificationService.notifySpecialDeviceAddConfigChange(nemoSN,allConfigs);
                }
                specialNemoFeatureMapper.deleteNemoSpecialFeature(nemo);
            }

            List<SpecialNemoCustomizeFeature> cusFeatures = this.specialNemoFeatureMapper.getBySpecialFeatureId(featureId);
            for(SpecialNemoCustomizeFeature po : cusFeatures) {
                internalApiProxy.removeNemoFeature(nemoSN, po.getCustomizeFeatureId());
            }
        }
    }
}
