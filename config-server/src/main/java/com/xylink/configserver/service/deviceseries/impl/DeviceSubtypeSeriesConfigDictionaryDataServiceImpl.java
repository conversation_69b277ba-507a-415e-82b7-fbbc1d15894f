package com.xylink.configserver.service.deviceseries.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigDictionaryDataEntity;
import com.xylink.configserver.mapper.deviceseries.DeviceSubtypeSeriesConfigDictionaryDataMapper;
import com.xylink.configserver.service.deviceseries.DeviceSubtypeSeriesConfigDictionaryDataService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-02-05 16:24
 */
@Service
public class DeviceSubtypeSeriesConfigDictionaryDataServiceImpl extends ServiceImpl<DeviceSubtypeSeriesConfigDictionaryDataMapper, DeviceSubtypeSeriesConfigDictionaryDataEntity> implements DeviceSubtypeSeriesConfigDictionaryDataService {

    @Override
    public List<DeviceSubtypeSeriesConfigDictionaryDataEntity> getByConfigIdAndSpecial(long configId, int special) {
        return this.baseMapper.getByConfigIdAndSpecial(configId, special);
    }

    @Override
    public List<DeviceSubtypeSeriesConfigDictionaryDataEntity> getByConfigIdAndSpecial(long seriesId) {
        return this.baseMapper.getBySeriesId(seriesId);
    }
}
