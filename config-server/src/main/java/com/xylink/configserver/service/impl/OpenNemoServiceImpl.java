package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.configserver.data.model.OpenNemo;
import com.xylink.configserver.data.model.OpenNemoConfig;
import com.xylink.configserver.enums.Configs;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.mapper.OpenNemoMapper;
import com.xylink.configserver.service.DeviceConfigService;
import com.xylink.configserver.service.OpenNemoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class OpenNemoServiceImpl implements OpenNemoService {

    private ObjectMapper mapper = new ObjectMapper();

    @Autowired
    DeviceConfigService deviceConfigService;

    @Autowired
    OpenNemoMapper openNemoMapper;

    @Override
    public String getOpenNemoConfig(long nemoId) throws ServiceException {
        OpenNemo po = openNemoMapper.getByNemoId(nemoId);
        if (po != null) {
            return po.getConfig();
        } else if (isOpennemoByConfig(nemoId)) {
            try {
                return mapper.writeValueAsString(new OpenNemoConfig());
            } catch (Exception e) {
                throw new ServiceException("FailedSaveOpenNemo", ErrorStatus.INTERNAL_ERROR);
            }
        }
        return null;
    }

    private boolean isOpennemoByConfig(long nemoId) {
        String type = deviceConfigService.getNemoConfig(nemoId, Configs.NemoConfig.NEMO_TYPE);
        if(type != null) {
            return Configs.NemoType.isOpenNemo(type);
        }
        return false;
    }
}
