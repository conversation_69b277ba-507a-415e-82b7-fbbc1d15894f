package com.xylink.configserver.service.impl;

import com.xylink.configserver.data.model.DeviceInfo;
import com.xylink.configserver.data.model.FeatureProvision;
import com.xylink.configserver.data.model.RestNemoConfig;
import com.xylink.configserver.process.NemoConfigProcessor;
import com.xylink.configserver.process.NemoFeatureProcessor;
import com.xylink.configserver.service.NemoConfigHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class NemoConfigHelperImpl implements NemoConfigHelper {

    private Map<String, NemoConfigProcessor> configProcessors = new HashMap<>();

    private Map<String, NemoFeatureProcessor> featureProcessors = new HashMap<> ();

    @Autowired
    private NemoConfigProcessor carouselImagesProcessor;

    @Autowired
    private NemoConfigProcessor specialContactProcessor;

    @Autowired
    private NemoConfigProcessor defaultNemoConfigProcessor;

    @Autowired
    private NemoFeatureProcessor changeUseModeProcessor;

    @Autowired
    private NemoFeatureProcessor defaultNemoFeatureProcessor;

    @Override
    public RestNemoConfig process(String configName, String configValue,DeviceInfo device) {
        return this.getConfigProcessor(configName).processNemoConfig(configName, configValue, device);
    }

    @Override
    public RestNemoConfig process(FeatureProvision feature, DeviceInfo device) {
        return this.getFeatureProcessor(feature.getFeatureName()).processNemoFeatureProvision(feature, device);
    }

    private NemoConfigProcessor getConfigProcessor(String configName) {
        NemoConfigProcessor processor = configProcessors.get(configName);
        if(processor == null) {
            processor = defaultNemoConfigProcessor;
        }
        return processor;
    }

    private NemoFeatureProcessor getFeatureProcessor(String featureName) {
        NemoFeatureProcessor processor = featureProcessors.get(featureName);
        if(processor == null ) {
            processor = defaultNemoFeatureProcessor;
        }
        return processor;
    }

    @PostConstruct
    void init(){
        configProcessors.put("carouselImages",carouselImagesProcessor);
        configProcessors.put("specialContacts",specialContactProcessor);
        featureProcessors.put("changeUseModeProcessor",changeUseModeProcessor);

    }
}
