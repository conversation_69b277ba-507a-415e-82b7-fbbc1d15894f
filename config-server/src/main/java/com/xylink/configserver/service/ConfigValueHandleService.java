package com.xylink.configserver.service;

import com.xylink.configserver.data.model.BaseConfig;
import com.xylink.configserver.data.model.ConfigTypeValueLimitModel;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 处理配置值类型及取值范围的校验和按类型解析服务
 *
 * <AUTHOR>
 * @since 2021/1/9 10:41 上午
 */
@Service
public interface ConfigValueHandleService {

    /**
     * 校验
     */
    <T extends BaseConfig> void verifyConfig(List<T> unsavedConfigList);

    /**
     * 解析
     * @return
     */
    Map<String, Object> parseConfigs(Map<String, String> configs, String clientConfig);

    Object parseOneConfig(ConfigTypeValueLimitModel model, String configValue, String clientConfigName);

    String transferToJson(Map<String, Object> pendedConfigMap);

    void removeErrorConfigs(Map<String, Map<String, String>> allConfigs);
}
