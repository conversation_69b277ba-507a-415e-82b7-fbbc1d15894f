package com.xylink.configserver.service.deviceseries;

import com.xylink.configserver.data.model.deviceseries.EnterpriseDeviceSeriesConfigDto;
import com.xylink.configserver.data.model.deviceseries.EnterpriseDeviceSeriesConfigUpdateDto;

/**
 * <AUTHOR>
 * @since 2021-02-02 15:43
 */
public interface EnterpriseDeviceSeriesService {
    /**
     * 获取企业系列配置
     *
     * @param enterpriseId
     * @param seriesId
     * @return
     */
    EnterpriseDeviceSeriesConfigDto queryEnterpriseDeviceSeriesConfigs(String enterpriseId, Long seriesId);

    /**
     * 更新企业的系列配置
     *
     * @param enterpriseDeviceSeriesConfigUpdateDto
     * @return
     */
    boolean updateEnterpriseDeviceSeriesConfigs(EnterpriseDeviceSeriesConfigUpdateDto enterpriseDeviceSeriesConfigUpdateDto);

}
