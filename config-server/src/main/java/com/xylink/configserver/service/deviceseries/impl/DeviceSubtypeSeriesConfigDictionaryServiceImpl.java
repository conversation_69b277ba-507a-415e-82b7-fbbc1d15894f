package com.xylink.configserver.service.deviceseries.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xylink.configserver.data.model.deviceseries.DeviceGroupEnum;
import com.xylink.configserver.data.model.deviceseries.DeviceSeriesSubtypeConfigsDto;
import com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigDictionaryEntity;
import com.xylink.configserver.mapper.deviceseries.DeviceSubtypeSeriesConfigDictionaryMapper;
import com.xylink.configserver.service.deviceseries.ConfigDictionaryUtils;
import com.xylink.configserver.service.deviceseries.DeviceSubtypeSeriesConfigDictionaryService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-18 21:24
 */
@Service
public class DeviceSubtypeSeriesConfigDictionaryServiceImpl extends ServiceImpl<DeviceSubtypeSeriesConfigDictionaryMapper, DeviceSubtypeSeriesConfigDictionaryEntity> implements DeviceSubtypeSeriesConfigDictionaryService {
    @Override
    public List<DeviceSubtypeSeriesConfigDictionaryEntity> getSeriesConfigDictionary() {
        return this.baseMapper.getConfigDictionaryByType(DeviceGroupEnum.SERIES.name());

    }

    @Override
    public List<DeviceSubtypeSeriesConfigDictionaryEntity> getSubtypeConfigDictionary() {
        return this.baseMapper.getConfigDictionaryByType(DeviceGroupEnum.SUBTYPE.name());
    }

    @Override
    public List<DeviceSeriesSubtypeConfigsDto> getTreeSeriesConfigDictionary() {
        List<DeviceSubtypeSeriesConfigDictionaryEntity> dictionary = getSeriesConfigDictionary();
        return ConfigDictionaryUtils.parseTree(dictionary, null);
    }
}
