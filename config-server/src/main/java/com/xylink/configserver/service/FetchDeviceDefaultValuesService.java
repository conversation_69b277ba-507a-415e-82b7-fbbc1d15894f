package com.xylink.configserver.service;

import com.xylink.configserver.data.model.FetchDeviceDefaultModel;

import java.util.List;
import java.util.Map;

/**
 * ClassName:FetchDeviceDefaultValuesService
 * Package:com.xylink.configserver.service
 * Description:
 *
 * <AUTHOR>
 * @Date 2025/2/11-15:21
 * @Version: v1.0
 */
public interface FetchDeviceDefaultValuesService {
    Map<String, Map<String, String>> fetchDeviceDefaultValues(String subtype);

    List<FetchDeviceDefaultModel> fetchAllSubtypeDefault();

}
