package com.xylink.configserver.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xylink.configserver.data.dto.BaseConfigDto;
import com.xylink.configserver.data.model.EnterpriseNemoConfig;
import com.xylink.configserver.mapper.EnterpriseNemoConfigMapper;
import com.xylink.configserver.service.ConfigValueHandleService;
import com.xylink.configserver.service.EnterpriseNemoConfigService;
import com.xylink.configserver.util.ConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


@Slf4j
@Service
public class EnterpriseNemoConfigServiceImpl extends ServiceImpl<EnterpriseNemoConfigMapper, EnterpriseNemoConfig> implements EnterpriseNemoConfigService {

    @Autowired
    EnterpriseNemoConfigMapper enterpriseNemoConfigMapper;

    private final ConfigValueHandleService configValueHandleService;

    public EnterpriseNemoConfigServiceImpl(ConfigValueHandleService configValueHandleService) {
        this.configValueHandleService = configValueHandleService;
    }

    @Override
    public List<EnterpriseNemoConfig> getEnterpriseProfileConfig(String enterpriseId, int type) {
        return enterpriseNemoConfigMapper.getEnterpriseProfileConfig(enterpriseId, type);
    }

    @Override
    public List<EnterpriseNemoConfig> getEnterpriseDeviceConfig(String deviceSN, int type) {
        return enterpriseNemoConfigMapper.getEnterpriseDeviceConfigs(deviceSN, type);
    }

    @Override
    public List<EnterpriseNemoConfig> getEnterpriseDeviceConfigByEnterpriseId(String enterpriseId, int type) {
        return enterpriseNemoConfigMapper.getEnterpriseDeviceConfigByEnterpriseId(enterpriseId, type);
    }

    @Override
    public List<EnterpriseNemoConfig> getEnterpriseDeviceClientConfig(String deviceSN, String clientConfigName) {
        return enterpriseNemoConfigMapper.getEnterpriseDeviceClientConfig(deviceSN, clientConfigName);
    }

    @Override
    public EnterpriseNemoConfig getEnterpriseDeviceConfig(String deviceSN, String configName, int deviceType) {
        return enterpriseNemoConfigMapper.getEnterpriseDeviceConfig(deviceSN, configName, deviceType);
    }

    @Override
    public EnterpriseNemoConfig getEntDeviceConfigBySubType(String enterpriseId, String configName, int subType) {
        List<EnterpriseNemoConfig> configs = enterpriseNemoConfigMapper.getEntDeviceConfigBySubType(enterpriseId, configName, subType);
        return configs != null && configs.size() > 0 ? configs.get(0) : null;
    }

    @Override
    public EnterpriseNemoConfig getEntDeviceConfigByType(String enterpriseId, String configName, int type) {
        List<EnterpriseNemoConfig> configs = enterpriseNemoConfigMapper.getEntDeviceConfigByType(enterpriseId, configName, type);
        return configs != null && configs.size() > 0 ? configs.get(0) : null;
    }

    @Override
    public List<EnterpriseNemoConfig> getProfileConfigs(String profileId) {
        return enterpriseNemoConfigMapper.getProfileConfigsByProfileId(profileId);
    }

    @Override
    public List<EnterpriseNemoConfig> getProfileConfigs(String profileId, String configName) {
        return enterpriseNemoConfigMapper.getProfileConfigsByProfileIdAndConfigName(profileId, configName);
    }

    @Override
    public Set<EnterpriseNemoConfig> getEnterpriseNemoConfigs(String profileId) {
        return enterpriseNemoConfigMapper.getEnterpriseNemoConfigsByProfileId(profileId);
    }

    @Override
    public void saveOrUpdateEnterpriseNemoConfig(EnterpriseNemoConfig enterpriseNemoConfig) {
        // 校验配置
        List<EnterpriseNemoConfig> enterpriseNemoConfigList = new ArrayList<>();
        enterpriseNemoConfigList.add(enterpriseNemoConfig);
        configValueHandleService.verifyConfig(enterpriseNemoConfigList);

        if (enterpriseNemoConfig != null) {
            if (enterpriseNemoConfigMapper.selectById(enterpriseNemoConfig.getId()) != null) {
                //bugfix 使用最新版本登录后，设置 - 关于 单位logo 为空[#334799 ]
                if (null == enterpriseNemoConfig.getConfigValue()){
                    enterpriseNemoConfig.setConfigValue("");
                }
                enterpriseNemoConfigMapper.updateById(enterpriseNemoConfig);
            } else {
                enterpriseNemoConfigMapper.insert(enterpriseNemoConfig);
            }
        }
    }

    @Override
    public List<EnterpriseNemoConfig> getEnterpriseDeviceConfigByEnterpriseIdAndConfigList(
            String enterpriseId,
            Integer deviceType,
            Integer deviceSubtype,
            List<BaseConfigDto> requiredConfigList) {

        if (StringUtils.isBlank(enterpriseId)) {
            return Collections.emptyList();
        }

        int resListCap = 10;
        if (CollectionUtils.isNotEmpty(requiredConfigList)) {

            resListCap = requiredConfigList.size() * 2;
        }

        List<EnterpriseNemoConfig> res = new ArrayList<>(resListCap);
        List<EnterpriseNemoConfig> entTypeConfigList = null;
        List<EnterpriseNemoConfig> entSubtypeConfigList = null;
        if (deviceType > 0) {
            entTypeConfigList = enterpriseNemoConfigMapper.getEnterpriseDeviceConfigByEnterpriseId(enterpriseId, deviceType);
            entTypeConfigList.forEach(enterpriseNemoConfig -> enterpriseNemoConfig.setClientConfigName(
                    ConfigUtils.transferCommonConfig(enterpriseNemoConfig.getClientConfigName())));
        }

        if (deviceSubtype > 0) {
            entSubtypeConfigList = enterpriseNemoConfigMapper.getEnterpriseDeviceConfigByEnterpriseId(enterpriseId, deviceSubtype);
            entSubtypeConfigList.forEach(enterpriseNemoConfig -> enterpriseNemoConfig.setClientConfigName(
                    ConfigUtils.transferCommonConfig(enterpriseNemoConfig.getClientConfigName())));
        }

        if (CollectionUtils.isNotEmpty(requiredConfigList)) {
            Map<String, List<EnterpriseNemoConfig>> subtypeConfigMap = new HashMap<>(8);
            Map<String, List<EnterpriseNemoConfig>> configMap = new HashMap<>(8);

            // 转为map
            if (CollectionUtils.isNotEmpty(entSubtypeConfigList)) {
                for (EnterpriseNemoConfig config : entSubtypeConfigList) {
                    combineConfig(subtypeConfigMap, config.getClientConfigName(), config);
                }
            }

            if (CollectionUtils.isNotEmpty(entTypeConfigList)) {
                for (EnterpriseNemoConfig config : entTypeConfigList) {
                    combineConfig(configMap, config.getClientConfigName(), config);
                }
            }
            for (BaseConfigDto baseConfigDto : requiredConfigList) {
                final String clientConfigName = ConfigUtils.transferCommonConfig(baseConfigDto.getClientConfigName());
                final String configName = baseConfigDto.getConfigName();
                if (configName == null) {
                    continue;
                }

                mergeConfigs(res, configMap, clientConfigName, configName);
                mergeConfigs(res, subtypeConfigMap, clientConfigName, configName);
            }
        } else {
            if (CollectionUtils.isEmpty(entSubtypeConfigList)) {
                // 防止空指针 subtype null && type null
                if (CollectionUtils.isEmpty(entTypeConfigList)) {
                    return res;
                }
                return entTypeConfigList;
            }

            // subtype 非 null type null
            if (CollectionUtils.isEmpty(entTypeConfigList)) {
                return entSubtypeConfigList;
            }

            Set<String> set = new HashSet<>();

            entSubtypeConfigList.forEach(
                    config -> {
                        if (set.add(config.getSignature())) {
                            res.add(config);
                        }
                    }
            );

            entTypeConfigList.forEach(
                    config -> {
                        if (set.add(config.getSignature())) {
                            res.add(config);
                        }
                    }
            );
        }

        return res;
    }

    private void mergeConfigs(List<EnterpriseNemoConfig> res, Map<String, List<EnterpriseNemoConfig>> map, String clientConfigName, String configName) {
        List<EnterpriseNemoConfig> configs = map.get(clientConfigName);

        if (CollectionUtils.isEmpty(configs)) {
            return;
        }

        for (EnterpriseNemoConfig config : configs) {
            if (configName.equals(config.getConfigName())) {
                res.add(config);
                break;
            }
        }
    }

    private void combineConfig(Map<String, List<EnterpriseNemoConfig>> map, String key, EnterpriseNemoConfig value) {
        map.computeIfAbsent(key, oldKey -> {
            List<EnterpriseNemoConfig> list = new ArrayList<>();
            list.add(value);
            return list;
        });

        map.computeIfPresent(key, (oldKey, oldValue) -> {
            oldValue.add(value);
            return oldValue;
        });
    }


    @Override
    public HashMap<String, String> selectConfigsByEnterpriseConfiguration(String enterpriseId, String clientConfigName, int type, int subType, String deviceSN) {
        //首先通过enterpriseid查询基础配置
        HashMap<String, String> hashMap = new HashMap<>(32);
        //1.1第一层查询  enterpriseid--->type查询
        List<EnterpriseNemoConfig> enterpriseDeviceConfigsByEnterpriseId = this.getEnterpriseDeviceConfigByEnterpriseId(enterpriseId, type);
        if (!CollectionUtils.isEmpty(enterpriseDeviceConfigsByEnterpriseId)) {
            for (EnterpriseNemoConfig enterpriseNemoConfig : enterpriseDeviceConfigsByEnterpriseId) {
                hashMap.put(enterpriseNemoConfig.getConfigName(), enterpriseNemoConfig.getConfigValue());
            }
        }
        //1.2第二层查询 enterpriseid--->subeType查询 覆盖
        List<EnterpriseNemoConfig> enterpriseDeviceConfigsSubtype = this.getEnterpriseDeviceConfigByEnterpriseId(enterpriseId, subType);
        if (!CollectionUtils.isEmpty(enterpriseDeviceConfigsSubtype)) {
            for (EnterpriseNemoConfig enterpriseNemoConfig : enterpriseDeviceConfigsSubtype) {
                hashMap.put(enterpriseNemoConfig.getConfigName(), enterpriseNemoConfig.getConfigValue());
            }
        }

        // 1.3第三层查询 sn--->type查询 覆盖
        List<EnterpriseNemoConfig> enterpriseDeviceConfigSnType = this.getEnterpriseDeviceConfig(deviceSN, type);
        if (!CollectionUtils.isEmpty(enterpriseDeviceConfigSnType)) {
            for (EnterpriseNemoConfig enterpriseNemoConfig : enterpriseDeviceConfigSnType) {
                hashMap.put(enterpriseNemoConfig.getConfigName(), enterpriseNemoConfig.getConfigValue());
            }
        }

        // 1.4第四层查询 sn--->subType查询 覆盖
        List<EnterpriseNemoConfig> enterpriseNemoConfigSnSubtype = this.getEnterpriseDeviceConfig(deviceSN, subType);
        if (!CollectionUtils.isEmpty(enterpriseNemoConfigSnSubtype)) {
            for (EnterpriseNemoConfig enterpriseNemoConfig : enterpriseNemoConfigSnSubtype) {
                hashMap.put(enterpriseNemoConfig.getConfigName(), enterpriseNemoConfig.getConfigValue());
            }
        }
        return hashMap;
    }


}