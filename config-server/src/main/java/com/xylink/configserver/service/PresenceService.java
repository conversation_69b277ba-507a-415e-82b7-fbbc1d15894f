package com.xylink.configserver.service;

import com.xylink.configserver.data.model.PresenceInfoDto;
import com.xylink.configserver.data.model.UserDevice;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface PresenceService {

    Map<String, String> getDeviceInfo(long userId, String deviceId, int deviceType, String sn);

    String getAppInfo(long id, String redisKeyModel);

    boolean deviceIsOnline(UserDevice nemo, String number);

    Set<String> getCusomizedUsersByCustomizedKey(String customizedKey);

    PresenceInfoDto batchGetDeviceState(List<UserDevice> deviceList);
}
