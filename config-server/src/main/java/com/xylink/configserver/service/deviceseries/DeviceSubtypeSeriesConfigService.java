package com.xylink.configserver.service.deviceseries;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-14 22:48
 */
public interface DeviceSubtypeSeriesConfigService extends IService<DeviceSubtypeSeriesConfigEntity> {
    /**
     * 获取系列选择管理的配置项
     *
     * @param seriesId
     * @return
     */
    List<DeviceSubtypeSeriesConfigEntity> getSeriesConfig(long seriesId);

    /**
     * 获取终端选择管理的配置项
     *
     * @param subtype
     * @return
     */
    List<DeviceSubtypeSeriesConfigEntity> getSubtypeConfig(int subtype);

    /**
     * 删除系列选择管理的配置项
     *
     * @param seriesId
     * @return
     */
    void deleteSeriesConfigBySeriesId(long seriesId);


    /**
     * 删除系列选择管理的配置项
     *
     * @param subtype
     * @return
     */
    void deleteSubtypeConfigBySubtype(int subtype);

}
