package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xylink.configserver.data.model.DeviceSoftVersion;
import com.xylink.configserver.mapper.DeviceSoftVersionMapper;
import com.xylink.configserver.service.DeviceSoftVersionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DeviceSoftVersionServiceImpl extends ServiceImpl<DeviceSoftVersionMapper, DeviceSoftVersion> implements DeviceSoftVersionService {

    @Autowired
    DeviceSoftVersionMapper deviceSoftVersionMapper;

    @Override
    public DeviceSoftVersion getDeviceVersionBySn(String deviceSN) {
        return deviceSoftVersionMapper.getDeviceVersionBySn(deviceSN);
    }
}
