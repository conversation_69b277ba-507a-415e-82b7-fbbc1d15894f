package com.xylink.configserver.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xylink.configserver.data.model.FetchDeviceDefaultModel;
import com.xylink.configserver.data.model.LibraDeviceSubtypeModel;
import com.xylink.configserver.mapper.LibraDeviceSubtypeModelMapper;
import com.xylink.configserver.service.DefaultConfigService;
import com.xylink.configserver.service.FetchDeviceDefaultValuesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ClassName:FetchDeviceDefaultValuesServiceImpl
 * Package:com.xylink.configserver.service.impl
 * Description:
 *
 * <AUTHOR>
 * @Date 2025/2/11-15:21
 * @Version: v1.0
 */
@Service
public class FetchDeviceDefaultValuesServiceImpl implements FetchDeviceDefaultValuesService {

    @Autowired
    private LibraDeviceSubtypeModelMapper libraDeviceSubtypeModelMapper;

    @Autowired
    private DefaultConfigService defaultConfigService;


    @Override
    public Map<String, Map<String, String>> fetchDeviceDefaultValues(String subtype) {
        LibraDeviceSubtypeModel libraDeviceSubtypeModel = libraDeviceSubtypeModelMapper.selectOne(new LambdaQueryWrapper<LibraDeviceSubtypeModel>().eq(LibraDeviceSubtypeModel::getSubType, subtype));
        if (libraDeviceSubtypeModel != null) {
            Short type = libraDeviceSubtypeModel.getType();

            Map<String, Map<String, String>> defaultConfigByType = defaultConfigService.
                    getDefaultConfigByType(Integer.valueOf(subtype), type);

            return defaultConfigByType;
        }
        return null;
    }

    @Override
    public List<FetchDeviceDefaultModel> fetchAllSubtypeDefault() {
        //查询出所有的subtype
        List<LibraDeviceSubtypeModel> deviceSubtypeModels = libraDeviceSubtypeModelMapper.selectAll();
        //遍历处理
        if (deviceSubtypeModels != null && deviceSubtypeModels.size() > 0) {
            return deviceSubtypeModels.stream().map(libraDeviceSubtypeModel -> {
                FetchDeviceDefaultModel fetchDeviceDefaultModel = new FetchDeviceDefaultModel();
                fetchDeviceDefaultModel.setSubtype(libraDeviceSubtypeModel.getSubType());
                fetchDeviceDefaultModel.setDisplay(libraDeviceSubtypeModel.getCategoryDisplay());
                Map<String, Map<String, String>> defaultConfigByType = defaultConfigService.
                        getDefaultConfigByType(libraDeviceSubtypeModel.getSubType(), libraDeviceSubtypeModel.getType());
                fetchDeviceDefaultModel.setFetchDeviceDefaultValues(defaultConfigByType);
                return fetchDeviceDefaultModel;
            }).collect(Collectors.toList());
        }

        return Collections.emptyList();
    }
}
