package com.xylink.configserver.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xylink.configserver.data.dto.BaseConfigDto;
import com.xylink.configserver.data.model.EnterpriseNemoConfig;

import java.util.HashMap;
import java.util.List;
import java.util.Set;

public interface EnterpriseNemoConfigService extends IService<EnterpriseNemoConfig> {

    List<EnterpriseNemoConfig> getEnterpriseProfileConfig(String enterpriseId, int type);

    List<EnterpriseNemoConfig> getEnterpriseDeviceConfig(String deviceSN, int type);

    List<EnterpriseNemoConfig> getEnterpriseDeviceConfigByEnterpriseId(String enterpriseId, int type);

    List<EnterpriseNemoConfig> getEnterpriseDeviceClientConfig(String deviceSN, String clientConfigName);

    EnterpriseNemoConfig getEnterpriseDeviceConfig(String deviceSN, String configName, int deviceType);

    EnterpriseNemoConfig getEntDeviceConfigBySubType(String enterpriseId, String configName, int subType);

    EnterpriseNemoConfig getEntDeviceConfigByType(String enterpriseId, String configName, int type);

    List<EnterpriseNemoConfig> getProfileConfigs(String profileId);

    List<EnterpriseNemoConfig> getProfileConfigs(String profileId,String configName);

    Set<EnterpriseNemoConfig> getEnterpriseNemoConfigs(String profileId);

    void  saveOrUpdateEnterpriseNemoConfig(EnterpriseNemoConfig enterpriseNemoConfig);

    List<EnterpriseNemoConfig> getEnterpriseDeviceConfigByEnterpriseIdAndConfigList(String enterpriseId,
                                                                                    Integer deviceType,
                                                                                    Integer deviceSubtype,
                                                                                    List<BaseConfigDto> requiredConfigList);
    HashMap<String, String> selectConfigsByEnterpriseConfiguration(String enterpriseId, String clientConfigName, int type, int subType, String deviceSN);

}