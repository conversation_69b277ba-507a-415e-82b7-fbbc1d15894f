package com.xylink.configserver.service.impl;

import com.ainemo.protocol.CallUriType;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.xylink.configserver.data.model.PresenceInfo;
import com.xylink.configserver.data.model.PresenceInfoDto;
import com.xylink.configserver.data.model.UserDevice;
import com.xylink.configserver.enums.DeviceState;
import com.xylink.configserver.enums.DeviceType;
import com.xylink.configserver.proxy.InternalApiProxy;
import com.xylink.configserver.service.PresenceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.xylink.configserver.util.Constants.*;

@Slf4j
@Service
public class PresenceServiceImpl implements PresenceService {

    @Autowired
    @Resource(name = "redisAccessTemplate")
    RedisTemplate redisAccessTemplate;

    @Autowired
    @Resource(name = "redisCraftTemplate")
    RedisTemplate redisCraftTemplate;

    @Autowired
    InternalApiProxy internalApiProxy;

    @Override
    public Map<String, String> getDeviceInfo(long userId, String deviceId, int deviceType, String sn) {
        String key = generatePresenceKey(userId, deviceId, deviceType, sn);
        return redisAccessTemplate.opsForHash().entries(REDIS_KEY_DEVICE_PRESENCE + ":" + key);
    }

    @Override
    public String getAppInfo(long deviceId, String key) {
        String rediskey = APP_INFO + ":" + deviceId;
        return (String) redisAccessTemplate.opsForHash().get(rediskey, key);
    }

    @Override
    public boolean deviceIsOnline(UserDevice userDevicePO,String number){
        String callUri = getUriFromDevice(userDevicePO, number);
        PresenceInfoDto presenceInfoResult = internalApiProxy.getDeviceOnlineState(callUri);
        if(presenceInfoResult == null || CollectionUtils.isEmpty(presenceInfoResult.getPresenceInfoList())){
            log.error("get device online status error and callUri is "+callUri);
            return false;
        }
        PresenceInfo presenceInfo = presenceInfoResult.getPresenceInfoList().get(0);
        if(StringUtils.equals(presenceInfo.getState(), DeviceState.IDLE.toString()) ||
                org.apache.commons.lang3.StringUtils.equals(presenceInfo.getState(), DeviceState.INCALL.toString())){
            return true;
        }
        return false;
    }

    @Override
    public Set<String> getCusomizedUsersByCustomizedKey(String customizedKey) {
        return redisCraftTemplate.opsForSet().members(LOGIN_USER_PASSWORD_EXPIRE_TIME + "_" + customizedKey);
    }

    @Override
    public PresenceInfoDto batchGetDeviceState(List<UserDevice> deviceList) {
        List<String> callUriList = deviceList.stream().map(item -> getUriFromDevice(item, "")).collect(Collectors.toList());
        return internalApiProxy.batchGetDeviceState(callUriList);
    }

    public static String getUriFromDevice(UserDevice device, String number) {
        DeviceType deviceType = DeviceType.valueOf(device.getType());
        String callUri = null;
        switch (deviceType) {
            case SOFT:
                callUri = device.getUserProfileID() + SOFT;
                break;
            case HARD:
                callUri = device.getId() + HARD;
                break;
            case DESKTOP:
                callUri = device.getUserProfileID() + DESK;
                break;
            case BIG_ENDPOINT_DEVICE:
                callUri = number + BRUCE;
                break;
            case TVBOX:
                callUri = device.getId() + TVBOX;
                break;
            default:
                break;
        }

        return callUri;
    }


    protected String generatePresenceKey(long userId, String deviceId, int deviceType, String sn) {

        String key = "";

        if (deviceType == DeviceType.SOFT.getValue()) { // app
            key = "u:" + userId;

        } else if (DeviceType.isHardDevice(deviceType)) {

            if (StringUtils.isNotBlank(deviceId)) {
                try {
                    Long.parseLong(deviceId);
                    deviceId = deviceId.trim();
                } catch (Exception e) {
                    deviceId = null;
                }
            }

            if (StringUtils.isNotBlank(deviceId)) { // bind nemo
                key = "d:" + deviceId;
            } else { // unbind nemo
                key = "s:" + sn;
            }
        } else if (deviceType == DeviceType.DESKTOP.getValue()) {
            key = "pc:" + userId;
        }

        return key;
    }
}
