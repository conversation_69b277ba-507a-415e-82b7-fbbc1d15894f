package com.xylink.configserver.service.deviceseries.impl;


import com.xylink.configserver.data.model.DeviceConfigUpdate;
import com.xylink.configserver.data.model.EnterpriseNemoProfileUpdateReq;
import com.xylink.configserver.data.model.deviceseries.*;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.service.EnterpriseNemoProfileService;
import com.xylink.configserver.service.deviceseries.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021-02-02 16:00
 */
@Slf4j
@Service
public class EnterpriseDeviceSeriesServiceImpl implements EnterpriseDeviceSeriesService {
    @Resource
    private EnterpriseNemoProfileService enterpriseNemoProfileService;
    @Resource
    private DeviceSubtypeSeriesConfigDictionaryDataService deviceSubtypeSeriesConfigDictionaryDataService;
    @Resource
    private DeviceSeriesService deviceSeriesService;
    @Resource
    private DeviceConfigDictDataService deviceConfigDictDataService;
    @Resource
    private DeviceSubtypeService deviceSubtypeService;
    @Resource
    private DeviceSeriesSubTypeService deviceSeriesSubTypeService;

    /**
     * 1、获取配置项 （默认的+默认企业+企业配置）
     * 2、根据系列获取需要展示的配置项
     * 3、获取系列对应的subtype
     * 4、已查询到的配置集合中筛选需要的配置项：获取subtype 配置项，如果没有 或者 各subtype配置值有不同 统一取大类的配置
     *
     * @param enterpriseId
     * @param seriesId
     * @return
     */
    @Override
    public EnterpriseDeviceSeriesConfigDto queryEnterpriseDeviceSeriesConfigs(String enterpriseId, Long seriesId) {
        if (StringUtils.isBlank(enterpriseId) || seriesId == null) {
            return null;
        }
        EnterpriseNemoProfileUpdateReq enterpriseProfile = enterpriseNemoProfileService.getEnterpriseProfile(enterpriseId);
        return of(enterpriseProfile, seriesId);
    }

    @Override
    public boolean updateEnterpriseDeviceSeriesConfigs(EnterpriseDeviceSeriesConfigUpdateDto enterpriseDeviceSeriesConfigUpdateDto) {
        String profileId = null;
        String enterpriseId = enterpriseDeviceSeriesConfigUpdateDto.getEnterpriseId().trim();
        if (enterpriseDeviceSeriesConfigUpdateDto.getProfile() != null) {
            profileId = enterpriseNemoProfileService.updateEnterpriseNemoProfileByEnterpriseId(enterpriseId, enterpriseDeviceSeriesConfigUpdateDto.getProfile());
        }
        List<DeviceConfigUpdate> configs = enterpriseDeviceSeriesConfigUpdateDto.getConfigs();
        if (!CollectionUtils.isEmpty(configs)) {
            try {
                // 处理seriesId 和 subtypes mapping
                long seriesId = enterpriseDeviceSeriesConfigUpdateDto.getSeriesId();
                List<DeviceSeriesSubtypeEntity> subtypes = deviceSeriesSubTypeService.getBySeriesId(seriesId);
                if (!CollectionUtils.isEmpty(subtypes)) {
                    List<DeviceConfigUpdate> subtypeConfigs = new ArrayList<>();
                    configs.forEach(item ->
                            subtypeConfigs.addAll(fission(item, subtypes))
                    );
                    log.info("Enterprise:{} change series:{} fission configs:{}", enterpriseId, seriesId, subtypeConfigs);
                    enterpriseNemoProfileService.applyEnterpriseProfileConfig(subtypeConfigs, profileId);
                } else {
                    log.info("SeriesId:{} has no subtypes.", seriesId);
                }
            } catch (ServiceException e) {
                log.error("Failed to apply enterprise profile config.", e);
                return false;
            }
        }
        return true;
    }

    private EnterpriseDeviceSeriesConfigDto of(EnterpriseNemoProfileUpdateReq enterpriseProfile, Long seriesId) {
        if (enterpriseProfile == null) {
            return null;
        }
        EnterpriseDeviceSeriesConfigDto result = new EnterpriseDeviceSeriesConfigDto();
        result.setEnterpriseId(enterpriseProfile.getEnterpriseId());
        result.setProfile(enterpriseProfile.getProfile());
        result.setSeries(build((seriesId), enterpriseProfile.getConfigs()));
        return result;
    }

    private DeviceSeriesDto build(long seriesId, List<DeviceConfigUpdate> configs) {
        DeviceSeriesDto deviceSeriesDto = deviceSeriesService.getDeviceSeriesDto(seriesId);
        // 系列可配置的配置项
        deviceSeriesDto.setConfigs(ConfigDictionaryUtils.excludeNotSelectedTree(deviceSeriesDto.getConfigs()));
        List<DeviceSeriesSubtypeConfigsDto> seriesConfigs = deviceSeriesDto.getConfigs();
        if (!CollectionUtils.isEmpty(seriesConfigs)) {
            for (DeviceSeriesSubtypeConfigsDto item : seriesConfigs) {
                configsData(deviceSeriesDto, item, configs);
            }
        }
        return deviceSeriesDto;
    }

    private void configsData(DeviceSeriesDto deviceSeriesDto, DeviceSeriesSubtypeConfigsDto deviceSeriesSubtypeConfigsDto, List<DeviceConfigUpdate> configs) {
        //设置关联的配置项、当前值以及取值范围
        List<DeviceSubtypeSeriesConfigDictionaryDataEntity> configDataEntity = deviceSubtypeSeriesConfigDictionaryDataService.getByConfigIdAndSpecial(deviceSeriesSubtypeConfigsDto.getConfigId(), deviceSeriesDto.getSpecial());
        if (!CollectionUtils.isEmpty(configDataEntity)) {
            List<ConfigDataDto> configDataDtoList = new ArrayList<>(configDataEntity.size());
            for (DeviceSubtypeSeriesConfigDictionaryDataEntity dataEntity : configDataEntity) {
                ConfigDataDto configDataDto = new ConfigDataDto();
                configDataDto.setClientConfigName(dataEntity.getClientConfigName());
                configDataDto.setConfigName(dataEntity.getConfigName());
                configDataDto.setConfigValuesType(dataEntity.getConfigValuesType());
                // 设置系列当前值
                configDataDto.setConfigValue(getCurrentConfigValue(configs, dataEntity, deviceSeriesDto.getSubtypes()));
                // 设置取值范围
                List<ConfigDataValuesDto> configDataValuesDto = deviceConfigDictDataService.getDtoByDictType(dataEntity.getConfigDictType());
                if (!CollectionUtils.isEmpty(configDataValuesDto)) {
                    configDataDto.setConfigValues(configDataValuesDto);
                    if (configDataDto.getConfigValue() == null) {
                        configDataDto.setConfigValue(configDataDto.getConfigValues().get(0).getValue());
                    }
                }
                configDataDtoList.add(configDataDto);
            }
            deviceSeriesSubtypeConfigsDto.setConfigItems(configDataDtoList);
        }
        // 设置children关联的配置项、当前值以及取值范围
        List<DeviceSeriesSubtypeConfigsDto> children = deviceSeriesSubtypeConfigsDto.getChildren();
        if (children != null && !children.isEmpty()) {
            children.forEach(item ->
                    configsData(deviceSeriesDto, item, configs)
            );
        }
    }

    private Object getCurrentConfigValue(List<DeviceConfigUpdate> configs, DeviceSubtypeSeriesConfigDictionaryDataEntity dataEntity, List<Integer> subtypes) {
        if (!CollectionUtils.isEmpty(subtypes)) {
            List<DeviceConfigUpdate> configsValue = configs.stream().filter(item -> ConfigDictionaryUtils.equalConfigs(item, dataEntity)).collect(Collectors.toList());
            // 子类型的配置值
            List<DeviceConfigUpdate> prioritySubtypeConfig = configsValue.stream().filter(item -> subtypes.contains(item.getDeviceSubType())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(prioritySubtypeConfig)) {
                return prioritySubtypeConfig.get(0).getConfigValue();
            } else {
                // 大类的配置值
                Integer prioritySubtype = subtypes.get(0);
                DeviceSubtypeDto deviceSubtypeDto = deviceSubtypeService.getBySubtype(prioritySubtype);
                if (deviceSubtypeDto != null) {
                    int deviceType = deviceSubtypeDto.getType();
                    List<DeviceConfigUpdate> priorityDeviceTypeConfig = configsValue.stream().filter(item -> item.getDeviceSubType() == deviceType).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(priorityDeviceTypeConfig)) {
                        return priorityDeviceTypeConfig.get(0).getConfigValue();
                    }
                }
            }
        }
        return null;
    }

    private List<DeviceConfigUpdate> fission(DeviceConfigUpdate source, List<DeviceSeriesSubtypeEntity> subtypes) {
        return subtypes.stream().map(item -> {
            DeviceConfigUpdate deviceConfigUpdate = new DeviceConfigUpdate();
            BeanUtils.copyProperties(source, deviceConfigUpdate);
            deviceConfigUpdate.setDeviceSubType(item.getSubtype());
            return deviceConfigUpdate;
        }).collect(Collectors.toList());
    }

}
