package com.xylink.configserver.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xylink.configserver.data.model.UserDeviceEntity;
import com.xylink.configserver.mapper.NewUserDeviceServiceMapper;
import com.xylink.configserver.service.NewUserDeviceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class NewUserDeviceServiceImpl extends ServiceImpl<NewUserDeviceServiceMapper, UserDeviceEntity> implements NewUserDeviceService {

    @Autowired
    private NewUserDeviceServiceMapper newUserDeviceServiceMapper;

    @Override
    public List<Map<String, Object>> selectCondition(String condition) {
        return newUserDeviceServiceMapper.selectCondition(condition);
    }
}