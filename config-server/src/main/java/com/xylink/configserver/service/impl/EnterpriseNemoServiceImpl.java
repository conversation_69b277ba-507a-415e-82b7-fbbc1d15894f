package com.xylink.configserver.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.xylink.configserver.data.model.DeviceConfig;
import com.xylink.configserver.data.model.EnterpriseNemoConfig;
import com.xylink.configserver.enums.Configs;
import com.xylink.configserver.enums.SmsTemplateKey;
import com.xylink.configserver.mapper.EnterpriseNemoConfigMapper;
import com.xylink.configserver.mapper.UserDeviceMapper;
import com.xylink.configserver.proxy.InternalApiProxy;
import com.xylink.configserver.service.DefaultConfigService;
import com.xylink.configserver.service.EnterpriseNemoService;
import com.xylink.configserver.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class EnterpriseNemoServiceImpl implements EnterpriseNemoService {

    @Autowired
    EnterpriseNemoConfigMapper enterpriseNemoConfigMapper;

    @Autowired
    DefaultConfigService defaultConfigService;

    @Autowired
    UserDeviceMapper userDeviceMapper;

    @Autowired
    InternalApiProxy internalApiProxy;

    @Resource
    MessageSource messagesSource;
    @Override
    public Map<String, String> getEnterpriseUICustomization(String customizedKey, int configType) {
        Map<String, String> configs = new HashMap<>();

        List<EnterpriseNemoConfig> enterpriseNemoConfigs =
                enterpriseNemoConfigMapper.getEnterpriseCustomizedClientConfig(customizedKey, configType, Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION);
        getEnterpriseCustomizationConfig(configs, enterpriseNemoConfigs, configType);

        return configs;
    }

    @Override
    public Map<String, String> getEnterpriseH5Customization(String enterpriseId, String distributorId) {
        Map<String, String> configs = new HashMap<>();
        if(StringUtils.isNotBlank(distributorId)) {
            getEnterpriseH5CustomizationByEnterpriseId(distributorId, configs);
        }

        if(StringUtils.isNotBlank(enterpriseId)) {
            getEnterpriseH5CustomizationByEnterpriseId(enterpriseId, configs);
        }
        return configs;
    }

    @Override
    public Map<String, String> getEnterpriseH5Customization(String deviceSN) {
        Map<String, String> configs = new HashMap<>();
        if(deviceSN == null || deviceSN.trim().isEmpty()) {
            log.error("invalid device sn");
            return configs;
        }

        List<EnterpriseNemoConfig> enterpriseNemoConfigPOS = enterpriseNemoConfigMapper.getEnterpriseDeviceConfigs(deviceSN, Constants.TYPE_H5_CONFIG);
        getEnterpriseCustomizationConfig(configs, enterpriseNemoConfigPOS, Constants.TYPE_H5_CONFIG);

        return configs;
    }

    @Override
    public Map<String, String> getUserEnterpriseH5Customization(long userProfileId) {

        String enterpriseId = userDeviceMapper.getUserProfileEnterpriseByUserId(userProfileId);
        if(StringUtils.isNotBlank(enterpriseId)) {
            return new HashMap<>();
        }
        String distributorId = internalApiProxy.getEnterpriseDistributorId(enterpriseId);
        return getEnterpriseH5Customization(enterpriseId, distributorId);
    }

    @Override
    public Map<String, String> getEnterpriseSmsTemplateCustomization(String enterpriseId, String distributorId, String lang) {
        Map<String, String> configs = this.getEnterpriseH5Customization(enterpriseId, distributorId);
        return getEnterpriseSmsTemplateCustomizationOrDefault(configs,lang,enterpriseId);
    }

    @Override
    public Map<String, String> getUserEnterpriseSmsTemplateCustomization(long userProfileId, String lang) {
        String enterpriseId = userDeviceMapper.getUserProfileEnterpriseByUserId(userProfileId);
        if(StringUtils.isBlank(enterpriseId)) {
           return this.defaultSmsTemplate(lang);
        }
        String distributorId = internalApiProxy.getEnterpriseDistributorId(enterpriseId);
        return this.getEnterpriseSmsTemplateCustomization(enterpriseId,distributorId,lang);
    }

    private void getEnterpriseH5CustomizationByEnterpriseId(String enterpriseId, Map<String, String> configs) {
        List<EnterpriseNemoConfig> enterpriseNemoConfigPOS =  enterpriseNemoConfigMapper.getEnterpriseDeviceConfigByEnterpriseId(enterpriseId,Constants.TYPE_H5_CONFIG);
        addUIDisplayCustomization(configs, enterpriseNemoConfigPOS);
    }

    private void addUIDisplayCustomization(Map<String, String> configs, List<EnterpriseNemoConfig> enterpriseNemoConfigPOS) {
        if (enterpriseNemoConfigPOS != null) {
            Map<String, Map<String, String>> customizedConfigs = new HashMap<>();
            DeviceConfig.configPOs2All(enterpriseNemoConfigPOS, customizedConfigs);
            if (customizedConfigs.containsKey(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION)) {
                configs.putAll(customizedConfigs.get(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION));
            }
        }
    }

    private void getEnterpriseCustomizationConfig(Map<String, String> configs, List<EnterpriseNemoConfig> enterpriseNemoConfigPOS, int configType) {
        Map<String, Map<String, String>> defaultConfigs = defaultConfigService.getEnterpriseConfig(configType, -1);
        if(defaultConfigs != null && defaultConfigs.containsKey(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION)) {
            configs.putAll(defaultConfigs.get(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION));
        }
        addUIDisplayCustomization(configs, enterpriseNemoConfigPOS);
    }

    private Map<String, String> defaultSmsTemplate(String lang) {
        Map<String, String> defaults = new LinkedHashMap<>();
        defaults.putAll(defaultSmsTemplate(SmsTemplateKey.SMS_REPLACE_XIAOYU, lang));
        defaults.putAll(defaultSmsTemplate(SmsTemplateKey.SMS_USER_CREATE, lang));
        defaults.putAll(defaultSmsTemplate(SmsTemplateKey.SMS_USER_INVITED, lang));
        defaults.putAll(defaultSmsTemplate(SmsTemplateKey.SMS_ADMIN_REPLACE_NEW, lang));
        defaults.putAll(defaultSmsTemplate(SmsTemplateKey.SMS_ADMIN_REPLACE_OLD, lang));
        return defaults;
    }

    private Map<String, String> defaultSmsTemplate(String key, String lang) {
        Map<String, String> defaults = new HashMap<>();
        defaults.put(key, messagesSource.getMessage(key, null, new Locale(lang)));
        return defaults;
    }

    private Map<String, String> getEnterpriseSmsTemplateCustomizationOrDefault(Map<String, String> customization, String lang, String enterpriseId) {
        Map<String, String> smsTemplate = new LinkedHashMap<>();
        String signature = null;
        if (StringUtils.isNotBlank(enterpriseId)) {
            signature = internalApiProxy.getCustomSmsSignature(enterpriseId);
        }
        if (signature == null) {
            signature = messagesSource.getMessage(SmsTemplateKey.DEFAULT_SMS_SIGNATURE, null, new Locale(lang));
        }
        if (Objects.isNull(customization) || customization.isEmpty()) {
            smsTemplate = this.defaultSmsTemplate(lang);
        } else {
            smsTemplate.put(SmsTemplateKey.SMS_REPLACE_XIAOYU, "true");
            if (!customization.containsKey(SmsTemplateKey.SMS_REPLACE_XIAOYU) || !Boolean.valueOf(customization.get(SmsTemplateKey.SMS_REPLACE_XIAOYU))) {
                smsTemplate.putAll(defaultSmsTemplate(SmsTemplateKey.SMS_USER_CREATE, lang));
                smsTemplate.putAll(defaultSmsTemplate(SmsTemplateKey.SMS_USER_INVITED, lang));
                smsTemplate.putAll(defaultSmsTemplate(SmsTemplateKey.SMS_ADMIN_REPLACE_NEW, lang));
                smsTemplate.putAll(defaultSmsTemplate(SmsTemplateKey.SMS_ADMIN_REPLACE_OLD, lang));
            } else {
                smsTemplate.put(SmsTemplateKey.SMS_USER_CREATE, customizationOrDefault(customization, SmsTemplateKey.SMS_USER_CREATE, lang));
                smsTemplate.put(SmsTemplateKey.SMS_USER_INVITED, customizationOrDefault(customization, SmsTemplateKey.SMS_USER_INVITED, lang));
                smsTemplate.put(SmsTemplateKey.SMS_ADMIN_REPLACE_NEW, customizationOrDefault(customization, SmsTemplateKey.SMS_ADMIN_REPLACE_NEW, lang));
                smsTemplate.put(SmsTemplateKey.SMS_ADMIN_REPLACE_OLD, customizationOrDefault(customization, SmsTemplateKey.SMS_ADMIN_REPLACE_OLD, lang));
            }
        }
        for (Map.Entry<String, String> entry : smsTemplate.entrySet()) {
            entry.setValue(replaceSignature(entry.getValue(), signature));
        }
        return smsTemplate;
    }

    private String customizationOrDefault(Map<String,String> customization, String key, String lang){
        return customization.containsKey(key)?customization.get(key):defaultSmsTemplate(key,lang).get(key);
    }

    private static String replaceSignature(String old, String signature) {
        return old.replace(SmsTemplateKey.REPLACE_SIGNATURE, signature);
    }
}
