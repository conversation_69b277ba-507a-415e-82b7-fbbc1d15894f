package com.xylink.configserver.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xylink.configserver.data.entity.BaseCloudControllerConfigEntity;
import com.xylink.configserver.data.entity.EntCloudControllerConfigEntity;
import com.xylink.configserver.data.response.R;

import java.util.Map;
import java.util.Set;
public interface EntCloudControllerConfigService extends IService<EntCloudControllerConfigEntity> {

    Map<String, Map<String,String>> getCloudConfigLoginBeforeOrAfter(Integer configType, String customizedKey);

    Set<String> getCloudConfigCKs();

    Set<String> getCloudConfigClientConfigNames(Integer type);

    Set<String> getCloudConfigConfigNames(Integer type,String clientConfigName);

    String getCloudConfigValue(Integer type, String clientConfigName, String configName, String customizedKey);

    R<Void> addCloudConfigValue(BaseCloudControllerConfigEntity configEntity);

    R<Void> updateCloudConfigValue(EntCloudControllerConfigEntity configEntity);
}
