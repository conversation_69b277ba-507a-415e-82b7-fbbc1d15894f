package com.xylink.configserver.service;

import com.xylink.configserver.data.model.NemoDefaultBrandConfig;

import java.util.List;
import java.util.Map;

public interface BrandConfigService {

    List<NemoDefaultBrandConfig> getDefaultBrandConfigBytypeAndBrand(String brand,int deviceTypeOrSubtype,String brandModel);

    void clearBrandConfigCache(String brand, int configTypeOrSubype, String brandModel);

    Map<String, String> getDefaultBrandConfigAll();

    void saveOrUpdateDefaultBrandConfig(NemoDefaultBrandConfig defaultBrandConfig);

    void removeDefaultBrandConfig(String configBrand, String brandModel, int deviceTypeOrSubtype,String configName,String clientConfigName);
}
