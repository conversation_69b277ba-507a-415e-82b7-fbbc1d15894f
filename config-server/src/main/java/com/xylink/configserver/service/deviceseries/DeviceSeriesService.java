package com.xylink.configserver.service.deviceseries;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xylink.configserver.data.model.deviceseries.DeviceSeriesDto;
import com.xylink.configserver.data.model.deviceseries.DeviceSeriesEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-12 15:35
 */
public interface DeviceSeriesService extends IService<DeviceSeriesEntity> {
    /**
     * 获取系列详情
     *
     * @param id
     * @return
     */
    DeviceSeriesDto getDeviceSeriesDto(long id);

    /**
     * 新增或者修改系列相关
     *
     * @param deviceSeriesDto
     * @return
     */
    boolean saveOrUpdateSeries(DeviceSeriesDto deviceSeriesDto);

    /**
     * 获取系列列表
     *
     * @return
     */
    List<DeviceSeriesDto> getAllSeries();
}

