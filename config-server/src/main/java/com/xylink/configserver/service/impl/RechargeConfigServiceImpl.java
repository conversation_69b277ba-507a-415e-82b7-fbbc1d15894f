package com.xylink.configserver.service.impl;

import com.xylink.configserver.data.model.DeviceResolutionInfo;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.data.model.RechargeConfig;
import com.xylink.configserver.data.model.UserDevice;
import com.xylink.configserver.enums.Configs;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.RestException;
import com.xylink.configserver.mapper.RechargeConfigMapper;
import com.xylink.configserver.proxy.InternalApiProxy;
import com.xylink.configserver.service.ConfigValueHandleService;
import com.xylink.configserver.service.DeviceSubtypeModelCacheService;
import com.xylink.configserver.service.RechargeConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * Created by niulong on 2020/6/22 6:51 PM
 */
@Slf4j
@Service
public class RechargeConfigServiceImpl implements RechargeConfigService {

    @Autowired
    RechargeConfigMapper rechargeConfigMapper;

    @Autowired
    InternalApiProxy internalApiProxy;

    private final ConfigValueHandleService configValueHandleService;

    private final DeviceSubtypeModelCacheService deviceSubtypeModelCacheService;

    public RechargeConfigServiceImpl(ConfigValueHandleService configValueHandleService, DeviceSubtypeModelCacheService deviceSubtypeModelCacheService) {
        this.configValueHandleService = configValueHandleService;
        this.deviceSubtypeModelCacheService = deviceSubtypeModelCacheService;
    }

    @Override
    public boolean applyDevice4kConfig(UserDevice devicePO, Map<String, String> device4kConfigs) {
        boolean result = false;
        if (StringUtils.isBlank(devicePO.getDeviceSN())) {
            ErrorStatus errorStatus = ErrorStatus.LOGIN_INVALID_DEVICE_NOT_ACTIVITED;
            throw new RestException(HttpStatus.CLIENT_ERROR_FORBIDDEN, errorStatus.getErrorCode(),
                                    errorStatus.getResId());
        }
        if (!canOperate4K(devicePO.getSubType())) {
            ErrorStatus errorStatus = ErrorStatus.DEVICE_CAN_NOT_OPERATE_4K;
            throw new RestException(HttpStatus.CLIENT_ERROR_FORBIDDEN, errorStatus.getErrorCode(),
                    errorStatus.getResId());
        }
        String show4kResolution = device4kConfigs.get(Configs.SHOW_4K_RESOLUTION);
        String enable4kResolution = device4kConfigs.get(Configs.ENABLE_4K_RESOLUTION);
        List<RechargeConfig> rechargeConfigs = getRechargeConfigs(devicePO.getDeviceSN());
        List<RechargeConfig> needUpdateOrInsertConfigs = buildNeedUpdateOrInsertConfigs(devicePO, show4kResolution, enable4kResolution, rechargeConfigs);

        // 校验配置
        configValueHandleService.verifyConfig(needUpdateOrInsertConfigs);

        if (!CollectionUtils.isEmpty(needUpdateOrInsertConfigs)) {
            rechargeConfigMapper.bulkInsertOrUpdate(needUpdateOrInsertConfigs);
            result = true;
        }
        return result;
    }

    public boolean canOperate4K(int subType) {
        if (subType < 0) {
            return false;
        }

        return deviceSubtypeModelCacheService.getCanK4BySubtype(subType);
    }

    @Override
    public List<RechargeConfig> getRechargeConfigs(String deviceSn) {
        Map<String, Object> columnMap = new HashMap<>();
        if (StringUtils.isBlank(deviceSn)) {
            return Collections.emptyList();
        }
        columnMap.put("device_sn", deviceSn);
        return rechargeConfigMapper.selectByMap(columnMap);
    }

    private List<RechargeConfig> buildNeedUpdateOrInsertConfigs(UserDevice devicePO, String show4kResolution, String enable4kResolution, List<RechargeConfig> rechargeConfigs) {
        List<RechargeConfig> needUpdateOrInsertConfigs = new ArrayList<>();
        Date now = new Date();
        DeviceResolutionInfo deviceResolutionInfo = null;
        if (Boolean.FALSE.toString().equals(show4kResolution)) {
            //如果是关闭，原来没有数据就直接忽略。有数据则进行更新
            RechargeConfig config = rechargeConfigs == null ? null : rechargeConfigs.stream().filter(o -> Configs.SHOW_4K_RESOLUTION.equals(o.getConfigName())).findFirst().orElse(null);
            if (config != null) {
                config.setDeviceSn(devicePO.getDeviceSN());
                config.setConfigName(Configs.SHOW_4K_RESOLUTION);
                config.setConfigValue(show4kResolution);
                config.setConfigType(devicePO.getType());
                config.setClientConfigName(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION);
                config.setUpdateTime(now);
                needUpdateOrInsertConfigs.add(config);
            }
        } else if (Boolean.TRUE.toString().equals(show4kResolution)) {
            //调用bill接口查询是否充值4k，找到记录更新或者插入。如果没有充值，则报错
            deviceResolutionInfo = internalApiProxy.getDeviceResolutionInfoFromBill(devicePO.getDeviceSN());
            if (deviceResolutionInfo != null && deviceResolutionInfo.support4k()) {
                RechargeConfig config = rechargeConfigs == null ? null : rechargeConfigs.stream().filter(o -> Configs.SHOW_4K_RESOLUTION.equals(o.getConfigName())).findFirst().orElse(null);
                if (config == null) {
                    config = new RechargeConfig();
                    config.setCreateTime(now);
                }
                config.setDeviceSn(devicePO.getDeviceSN());
                config.setConfigName(Configs.SHOW_4K_RESOLUTION);
                config.setConfigValue(show4kResolution);
                config.setConfigType(devicePO.getType());
                config.setClientConfigName(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION);
                config.setConfigExpireTime(deviceResolutionInfo.getExpireTime());
                config.setUpdateTime(now);
                needUpdateOrInsertConfigs.add(config);
            } else {
                log.warn("buildNeedUpdateOrInsertConfigs show4kResolution, device not recharged 4k: " + devicePO.getId());
                throw new RestException(HttpStatus.CLIENT_ERROR_FORBIDDEN, ErrorStatus.DEVICE_NOT_RECHARGED_4K.getErrorCode(),
                        ErrorStatus.DEVICE_NOT_RECHARGED_4K.getResId());
            }
        }
        if (Boolean.FALSE.toString().equals(enable4kResolution)) {
            //如果是关闭，原来没有数据就直接忽略。有数据则进行更新
            RechargeConfig config = rechargeConfigs == null ? null : rechargeConfigs.stream().filter(o -> Configs.ENABLE_4K_RESOLUTION.equals(o.getConfigName())).findFirst().orElse(null);
            if (config != null) {
                config.setDeviceSn(devicePO.getDeviceSN());
                config.setConfigName(Configs.ENABLE_4K_RESOLUTION);
                config.setConfigValue(enable4kResolution);
                config.setConfigType(devicePO.getType());
                config.setClientConfigName(Configs.COMMON_CONFIG_KEY);
                config.setUpdateTime(now);
                needUpdateOrInsertConfigs.add(config);
            }
        } else if (Boolean.TRUE.toString().equals(enable4kResolution)) {
            if (deviceResolutionInfo == null) {
                deviceResolutionInfo = internalApiProxy.getDeviceResolutionInfoFromBill(devicePO.getDeviceSN());
            }
            if (deviceResolutionInfo != null && deviceResolutionInfo.support4k()) {
                RechargeConfig config = rechargeConfigs == null ? null : rechargeConfigs.stream().filter(o -> Configs.ENABLE_4K_RESOLUTION.equals(o.getConfigName())).findFirst().orElse(null);
                if (config == null) {
                    config = new RechargeConfig();
                    config.setCreateTime(now);
                }
                config.setDeviceSn(devicePO.getDeviceSN());
                config.setConfigName(Configs.ENABLE_4K_RESOLUTION);
                config.setConfigValue(enable4kResolution);
                config.setConfigType(devicePO.getType());
                config.setClientConfigName(Configs.COMMON_CONFIG_KEY);
                config.setConfigExpireTime(deviceResolutionInfo.getExpireTime());
                config.setUpdateTime(now);
                needUpdateOrInsertConfigs.add(config);
            } else {
                log.warn("buildNeedUpdateOrInsertConfigs enable4kResolution, device not recharged 4k: " + devicePO.getId());
                throw new RestException(HttpStatus.CLIENT_ERROR_FORBIDDEN, ErrorStatus.DEVICE_NOT_RECHARGED_4K.getErrorCode(),
                        ErrorStatus.DEVICE_NOT_RECHARGED_4K.getResId());
            }
        }
        return needUpdateOrInsertConfigs;
    }

}
