package com.xylink.configserver.service;

import com.ainemo.protocol.Device;
import com.xylink.configserver.data.model.*;

import java.util.List;
import java.util.Map;

public interface NotificationService {

    void notifyChangeNemoConfig(RestNemoConfig[] configs, long nemoId);

//    void notifyChangeNemoConfig(long nemoId, RestNemoConfig[] configs, boolean notifyNemo);

    void notifyConfigChange(Map<String, String> computedConfigs, UserDevice userDevicePO, boolean isHardDeviceExceptBigEndpoint);

    void notifyNemoConfigChange(UserDevice userDevicePO, RestNemoConfig[] toArray);

    void notifyChangeDeviceConfig(Map<String, String> computedConfigs, UserDevice userDevicePO);

    void notifyScreenSaverChanged(long id, int type);

    void norifyProfileRemoveNemoConfig(EnterpriseNemoProfileUpdateReq enterpriseNemoProfileUpdateReq);

    void notifyUserPasswordExpireTimeChanged(long userId, int deviceType, long deltTime);

    void norifyProfileNemoConfigChange(EnterpriseNemoConfig enterpriseNemoConfigPO);

    void notifyNemoFeatureChange(List<CustomizeFeature> customizeFeatures, UserDevice device);

    void notifySpecialDeviceAddConfigChange(String nemoSN, Map<String, Map<String, String>> allConfigs);

    void sendMsgToHardDevice(String content, List<Device> msgDevices);

    void sendMsgToSoftDevice(String content, List<Device> msgDevices, boolean persistent);

    void batchNotifyDeviceConfigChange(List<UserDevice> deviceList, DeviceConfigUpdate updatedConfig);

    void notifyAllDeviceConfigChange(List<UserDevice> userDeviceList, List<DeviceConfigUpdate> source);

    Map<String, String> restNemoConfigToMap(List<RestNemoConfig> data);
}
