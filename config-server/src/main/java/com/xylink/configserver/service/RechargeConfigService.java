package com.xylink.configserver.service;

import com.xylink.configserver.data.model.RechargeConfig;
import com.xylink.configserver.data.model.UserDevice;

import java.util.List;
import java.util.Map;

/**
 * Created by niulong on 2020/6/22 6:50 PM
 */
public interface RechargeConfigService {
    boolean applyDevice4kConfig(UserDevice devicePO, Map<String, String> device4kConfigs);

    List<RechargeConfig> getRechargeConfigs(String deviceSn) ;
}
