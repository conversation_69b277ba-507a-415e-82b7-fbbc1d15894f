package com.xylink.configserver.service;

import com.xylink.configserver.data.dto.BaseConfigDto;
import com.xylink.configserver.data.model.NemoDefaultConfig;
import com.xylink.configserver.data.model.NemoDefaultVersionConfig;
import com.xylink.configserver.data.vo.config.ConfigInfoVo;

import java.util.List;
import java.util.Map;

public interface DefaultConfigService {

    Map<String,Map<String, String>> getDefaultNemoConfig(String name, int subType);

    List<NemoDefaultConfig> getDefaultNemoConfig(Integer configType, String clientConfigName, String configName);

    Map<String,Map<String, String>> getDefaultConfigByType(int subType, int type);

    Map<String, Map<String, String>> getDefaultConfigByTypeAndConfigName(int subType, int type,
                                                                         List<BaseConfigDto> configDtoList);

    Map<String, Map<String, String>> getEnterpriseConfig(int subType, int type);

    List<NemoDefaultVersionConfig> getNemoDefaultConfigBytype(int type);

    List<NemoDefaultConfig> getAllDefaultConfig();

    /**
     * 获取指定设备的版本配置
     *
     * @param type
     * @param version
     * @return
     */
    List<NemoDefaultVersionConfig> getDefaultVersionConfigByTypeAndVersion(int type, int version);

    List<ConfigInfoVo> getAllConfigNameList();

    void saveOrUpdateDefaultNemoConfig(List<NemoDefaultConfig> nemoDefaultConfigs);

    void refreshDefaultConfigCache();

}
