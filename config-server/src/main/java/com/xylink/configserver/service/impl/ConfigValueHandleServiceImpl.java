package com.xylink.configserver.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.xylink.configserver.data.model.BaseConfig;
import com.xylink.configserver.data.model.ConfigTypeValueLimitModel;
import com.xylink.configserver.enums.ConfigValueType;
import com.xylink.configserver.enums.Configs;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.mapper.LibraConfigTypeValueLimitMapper;
import com.xylink.configserver.service.ConfigValueHandleService;
import com.xylink.configserver.util.Jackson;
import com.xylink.configserver.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2021/1/9 10:50 上午
 */
@Slf4j
@Service
public class ConfigValueHandleServiceImpl implements ConfigValueHandleService {

    private final LibraConfigTypeValueLimitMapper libraConfigTypeValueLimitMapper;

    private final Map<String, ConfigTypeValueLimitModel> configCacheMap = new HashMap<>(512);
    private final JsonMapper jsonMapper = new JsonMapper();

    public ConfigValueHandleServiceImpl(LibraConfigTypeValueLimitMapper libraConfigTypeValueLimitMapper) {
        this.libraConfigTypeValueLimitMapper = libraConfigTypeValueLimitMapper;
    }

    @Override
    public <T extends BaseConfig> void verifyConfig(List<T> unsavedConfigList) {
        if (unsavedConfigList == null || unsavedConfigList.size() == 0) {
            return;
        }

        for (T config : unsavedConfigList) {
            // 请使用迭代器进行删除，目前发现有些场景是需要改配置项的值为null (后期应该提供删除配置项的接口代替这个null的操作)
//            if (config.getConfigValue() == null) {
//                unsavedConfigList.remove(config);
//            }

            String clientConfigName = config.getClientConfigName();
            if (StringUtils.isBlank(clientConfigName)) {
                clientConfigName = Configs.COMMON_CONFIG_KEY;
            }
            String configName = config.getConfigName();
            ConfigTypeValueLimitModel configModel = configCacheMap.get(cacheKey(clientConfigName, configName));

            //配置不存在
            if (configModel == null) {
                continue;
            }

            //依照类型校验：array与json无需校验值范围
            if (ConfigValueType.ARRAY.getValue().equals(configModel.getValueType()) ||
                    ConfigValueType.JSON.getValue().equals(configModel.getValueType())) {
                judgeArrayOrJson(config.getConfigValue());
            } else {
                // 其余类型检查值范围
                String scope = configModel.getValueScope();
                if (!StringUtils.isBlank(scope)) { // 有范围规约(非空串)受检查
                    List<String> scopeList = null;
                    try {
                        scopeList = Jackson.getObjectMapper().readValue(scope, new TypeReference<List<String>>() {});
                    } catch (JsonProcessingException e) {
                        e.printStackTrace();
                    }

                    if (scopeList != null && !scopeList.contains(config.getConfigValue())) {
                        log.error("scope type is formatted with an error: " + config.getConfigValue());
                        ErrorStatus err = ErrorStatus.INVALID_PARAMETER;
                        StringBuilder sb = new StringBuilder();
                        err.setResId(sb.append("config value is not in scope：")
                                .append(scopeList).append(",value：").append(config.getConfigValue())
                                .append(",config_name: ").append(clientConfigName).append("#")
                                .append(configName).toString());
                        throw new ServiceException("配置校验错误", err);
                    }
                }
            }
        }
    }

    @Override
    public Map<String, Object> parseConfigs(Map<String, String> pendingConfigMap, String clientConfigName) {
        if (pendingConfigMap == null || pendingConfigMap.size() == 0) {
            return new HashMap<>(0);
        }

        // 处理每一项配置
        Map<String, Object> pendedConfigMap = new HashMap<>(64);
        Set<Map.Entry<String, String>> pendingConfigMapEntries = pendingConfigMap.entrySet();
        for (Map.Entry<String, String> pendingConfigEntry : pendingConfigMapEntries) {
            ConfigTypeValueLimitModel cacheConfig =
                    configCacheMap.get(cacheKey(clientConfigName, pendingConfigEntry.getKey()));
            Object pendedValue = parseOneConfig(cacheConfig, pendingConfigEntry.getValue(), clientConfigName);
            if (pendedValue == null) {
                continue;
            }
            pendedConfigMap.put(pendingConfigEntry.getKey(), pendedValue);
        }
        return pendedConfigMap;
    }


    /**
     * 配置项解析规则
     * 1、cacheConfig 配置项检查规则不存在  走原来的解析逻辑
     *
     * 2、cacheConfig 配置项检查规则存在
     * 2.1 configValue 为空，返回null，上层调用据此移除该项配置
     * 2.2 是否能根据valueType进行转换
     *
     * @param cacheConfig      配置项检查规则
     * @param configValue      当前配置项值
     * @param clientConfigName 当前配置项大类
     */
    @Override
    public Object parseOneConfig(ConfigTypeValueLimitModel cacheConfig, String configValue, String clientConfigName) {
        // 该配置的检验规则不存在，走原来的解析逻辑
        if (cacheConfig == null) {
            return oldParseProcess(configValue);
        }

        // 配置项的校验规则
        if (StringUtils.isBlank(configValue)){
            return null;
        }

        String type = cacheConfig.getValueType();

        // String和json不处理
        Object pendedValue = configValue;
        try {
            if (ConfigValueType.BOOLEAN.getValue().equals(type)) {
                pendedValue = String.valueOf(Boolean.parseBoolean(configValue));
            } else if (ConfigValueType.INTEGER.getValue().equals(type)) {
                pendedValue = jsonMapper.readValue(configValue, Integer.class);
            } else if (ConfigValueType.LONG.getValue().equals(type)) {
                pendedValue = jsonMapper.readValue(configValue, Long.class);
            } else if (ConfigValueType.DOUBLE.getValue().equals(type)) {
                pendedValue = jsonMapper.readValue(configValue, Double.class);
            } else if (ConfigValueType.ARRAY.getValue().equals(type)) {
                pendedValue = jsonMapper.readValue(configValue, Object[].class);
            }
        } catch (Exception e) {
            log.error("配置项类型解析失败！client_config_name:{},config_name:{}, type:{}, config_value:{}",
                    cacheConfig.getClientConfigName(), cacheConfig.getConfigName(), type, configValue, e);
            return null;
        }
        return pendedValue;
    }

    public Object oldParseProcess(String configValue) {
        if (configValue == null) {
            return "";
        }
        if ("TRUE".equalsIgnoreCase(configValue) || "FALSE".equalsIgnoreCase(configValue)) {
            return String.valueOf(Boolean.parseBoolean(configValue));
        } else if (NumberUtil.isInteger(configValue)) {
            try {
                return jsonMapper.readValue(configValue, Long.class);
            } catch (IOException ignored) {
            }
            try {
                return jsonMapper.readValue(configValue, Integer.class);
            } catch (IOException ignored) {
            }
        } else {
            try {
                return jsonMapper.readValue(configValue, Double.class);
            } catch (IOException ignored) {
            }
            try {
                return jsonMapper.readValue(configValue, Object[].class);
            } catch (IOException ignored) {
            }
        }

        return configValue;
    }

    @Override
    public String transferToJson(Map<String, Object> pendedConfigMap) {
        try {
            return jsonMapper.writeValueAsString(pendedConfigMap);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        return "";
    }

    @Override
    public void removeErrorConfigs(Map<String, Map<String, String>> allConfigs) {
        if (allConfigs == null || allConfigs.size() == 0) {
            return;
        }

        if (configCacheMap.isEmpty()) {
            return;
        }

        for (Map.Entry<String, Map<String, String>> allConfigEntries : allConfigs.entrySet()) {
            Iterator<Map.Entry<String, String>> configEntriesIterator = allConfigEntries.getValue().entrySet().iterator();
            String clientConfigName = allConfigEntries.getKey();
            while (configEntriesIterator.hasNext()) {
                Map.Entry<String, String> configEntry = configEntriesIterator.next();
                ConfigTypeValueLimitModel model = configCacheMap.get(cacheKey(clientConfigName, configEntry.getKey()));
                if (parseOneConfig(model, configEntry.getValue(), clientConfigName) == null) {
                    configEntriesIterator.remove();
                }
            }
        }
    }

    private static void judgeArrayOrJson(String value) {
        try {
            Jackson.getObjectMapper().readValue(value, Object.class);
        } catch (JsonProcessingException e) {
            log.error("json or array format is illegal");
            ErrorStatus err = ErrorStatus.INVALID_PARAMETER;
            StringBuilder sb = new StringBuilder();
            err.setResId(sb.append("json or array format is illegal：").append(value).toString());
            throw new ServiceException("配置校验错误", err);
        }
    }

    /**
     * 每天更新一次配置
     */
    @Scheduled(fixedDelay = 1000 * 60 * 60 * 24)
    private void timeFixedUpdate() {
        configCacheMap.clear();
        List<ConfigTypeValueLimitModel> cacheList = libraConfigTypeValueLimitMapper.selectAll();
        for (ConfigTypeValueLimitModel cache : cacheList) {
            configCacheMap.put(cacheKey(cache.getClientConfigName(), cache.getConfigName()), cache);
        }
        log.info("配置项缓存已更新！共更新{}项", configCacheMap.size());
    }

    private String cacheKey(String clientConfigName, String configName) {
        return clientConfigName + "#" + configName;
    }

}
