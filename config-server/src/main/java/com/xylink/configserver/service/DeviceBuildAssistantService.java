package com.xylink.configserver.service;

import com.xylink.configserver.data.dto.SubtypeExportImportDto;
import com.xylink.configserver.data.model.NewDeviceBuildInfoRequest;
import com.xylink.configserver.data.response.R;

/**
 * ClassName:DeviceBuildAssistantImpl
 * Package:com.xylink.configserver.service
 * Description:
 *
 * <AUTHOR>
 * @Date 2024/10/17-15:42
 * @Version: v1.0
 */
public interface DeviceBuildAssistantService {
     String newDeviceBuildReturn(NewDeviceBuildInfoRequest request, Integer subtype);

    SubtypeExportImportDto subtypeBaseExport(Integer subtype);

    R<String> subtypeBaseImport(SubtypeExportImportDto subtypeExportImportDto);
}
