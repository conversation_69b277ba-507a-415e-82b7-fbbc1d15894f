package com.xylink.configserver.service.deviceseries;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xylink.configserver.data.model.deviceseries.DeviceSeriesSubtypeEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-14 22:58
 */
public interface DeviceSeriesSubTypeService extends IService<DeviceSeriesSubtypeEntity> {
    /**
     * 系列包含的终端集合
     *
     * @param seriesId
     * @return
     */
    List<DeviceSeriesSubtypeEntity> getBySeriesId(long seriesId);

    /**
     * 终端所属系列
     *
     * @param subtype
     * @return
     */
    DeviceSeriesSubtypeEntity getSeries(int subtype);

    /**
     * 更新终端对应的系列
     *
     * @param seriesId
     * @param subtype
     */
    void saveOrUpdateSeriesSubtypeMapping(long seriesId, int subtype);

    /**
     * 根据终端删除对应记录
     *
     * @param subtype
     */
    void removeBySubtype(int subtype);

}
