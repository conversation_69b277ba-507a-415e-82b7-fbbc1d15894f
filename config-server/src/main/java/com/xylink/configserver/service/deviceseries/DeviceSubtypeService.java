package com.xylink.configserver.service.deviceseries;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xylink.configserver.data.model.DeviceSubtypeModelV2;
import com.xylink.configserver.data.model.deviceseries.*;

/**
 * <AUTHOR>
 * @since 2021-01-15 11:18
 */
public interface DeviceSubtypeService extends IService<DeviceSubtypeModelV2> {
    /**
     * 终端分页数据 支持模糊搜索 category   categoryDisplay
     *
     * @param page
     * @param key
     * @return
     */
    Page<DeviceSubtypeListDto> page(Page<DeviceSubtypeListDto> page, String key);

    /**
     * 终端配置详情
     *
     * @param subtype
     * @return
     */
    DeviceSubtypeDetailDto detail(int subtype);

    /**
     * 修改管理平台显示型号名称
     *
     * @param categoryDisplay
     * @param subtype
     * @return
     */
    boolean updateSubtypeCategoryDisplayName(String categoryDisplay, int subtype);

    /**
     * 新增或者修改终端配置
     *
     * @param deviceSubtypeDetailUpdateDto
     * @return
     */
    boolean saveOrUpdateDeviceSubtype(DeviceSubtypeDetailUpdateDto deviceSubtypeDetailUpdateDto);

    /**
     * 终端基础信息查询
     *
     * @param subtype
     * @return
     */
    DeviceSubtypeDto getBySubtype(int subtype);

    /**
     * 获取终端的配置
     *
     * @param deviceId
     * @param platformEnum
     * @return
     */
    DeviceSubtypeDto getByDeviceId(long deviceId, PlatformEnum platformEnum);

}
