package com.xylink.configserver.service;

import java.util.Map;

public interface EnterpriseNemoService {

    Map<String, String> getEnterpriseUICustomization(String customizedkey, int parseInt);

    Map<String, String> getEnterpriseH5Customization(String enterpriseId, String distributorId);

    Map<String,String>  getEnterpriseH5Customization(String deviceSN);

    Map<String, String> getUserEnterpriseH5Customization(long userProfileId);

    /**
     * 获取企业短信模板
     *
     * @param enterpriseId
     * @param distributorId
     * @return
     */
    Map<String, String> getEnterpriseSmsTemplateCustomization(String enterpriseId, String distributorId , String lang);

    /**
     * 获取企业短信模板
     * @param userProfileId
     * @return
     */
    Map<String, String> getUserEnterpriseSmsTemplateCustomization(long userProfileId , String lang);

}
