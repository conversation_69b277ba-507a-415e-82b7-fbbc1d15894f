package com.xylink.configserver.service.impl;

import com.github.seaframework.core.util.CollectionUtil;
import com.github.seaframework.monitor.SeaMonitor;
import com.github.seaframework.monitor.dto.MetricDTO;
import com.xylink.configserver.configuration.CommonConfiguration;
import com.xylink.configserver.service.SeaMonitorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;


@Slf4j
@Service
@RequiredArgsConstructor
public class SeaMonitorServiceImpl implements SeaMonitorService {

    @Autowired
    private CommonConfiguration commonConfiguration;

    @Override
    public void reportSignalAlarm(String metric, Map<String, String> extraMap) {
        if (!commonConfiguration.isSeaMonitorEnable()) {
            return;
        }

        try {
            MetricDTO metricDTO = new MetricDTO();
            metricDTO.setMetric(metric);
            metricDTO.setValue(1);
            metricDTO.setErrorFlag(true);
            metricDTO.setTraceIdFlag(true);
            if (CollectionUtil.isNotEmpty(extraMap)) {
                extraMap.put("traceid", "0");
                if (extraMap.containsKey("number")) {
                    Map<String, String> tagMap = new HashMap<>();
                    tagMap.put("number", extraMap.get("number"));
                    metricDTO.setTags(tagMap);
                }
                metricDTO.setExtraMap(extraMap);
            }
            SeaMonitor.logMetric(metricDTO);
        } catch (Exception e) {
            log.error("reportAlarmEvent, exception", e);
        }
    }
}