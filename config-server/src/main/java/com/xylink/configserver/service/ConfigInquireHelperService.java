package com.xylink.configserver.service;

import com.xylink.configserver.data.model.ContainerConfigTableModel;
import com.xylink.configserver.data.vo.InquireHelperVO;

import java.util.List;
import java.util.Map;

public interface ConfigInquireHelperService {
    ContainerConfigTableModel inquireConfig(InquireHelperVO inquireHelperVO);

    List<Map<String, Object>> selectCondition(String condition);
}