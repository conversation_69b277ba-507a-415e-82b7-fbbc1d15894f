package com.xylink.configserver.service.impl;

import com.xylink.configserver.data.dto.DeptChangeDTO;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.mapper.DeptConfigMapper;
import com.xylink.configserver.service.DeptConfigService;
import com.xylink.configserver.service.EnterpriseNemoProfileService;
import com.xylink.configserver.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class DeptConfigServiceImpl implements DeptConfigService {

    @Autowired
    private DeptConfigMapper deptConfigMapper;

    @Autowired
    private EnterpriseNemoProfileService enterpriseNemoProfileService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Override
    @Transactional
    public void changeConfig(DeptChangeDTO changeDTO) {

        //修改本地配置
        //配置量大大情况下这里有性能问题, 但是目前配置量级少, 后续可以考虑优化
        for (DeviceConfigUpdate updateItem:changeDTO.getConfigs()){
            //如果configValue=="", 就直接删掉这条配置防止配置覆盖出现问题(部门重置配置会传configValue=="")
            if (StringUtils.isBlank(updateItem.getConfigValue())){
                deptConfigMapper.deleteByDeptId(changeDTO.getEnterpriseId(), changeDTO.getDeptId(), updateItem.getConfigName());
                continue;
            }
            DeptConfigPO deptConfigPO = new DeptConfigPO();
            deptConfigPO.setEnterpriseId(changeDTO.getEnterpriseId());
            deptConfigPO.setDeptId(changeDTO.getDeptId());
            deptConfigPO.setClientConfigName(updateItem.getClientConfigName());
            deptConfigPO.setConfigName(updateItem.getConfigName());
            deptConfigPO.setConfigValue(updateItem.getConfigValue());
            deptConfigPO.setConfigType(updateItem.getDeviceSubType());
            deptConfigMapper.saveOrUpdate(deptConfigPO);
        }

        //发送配置变更消息
        applicationEventPublisher.publishEvent(new DeptConfigChangedEvent(changeDTO.getConfigs(), changeDTO.getEnterpriseId(), changeDTO.getDeptId()));

    }


    @Override
    public EnterpriseNemoProfileUpdateReq getOverrideDeptConfig(String enterpriseId, String deptId, int type) {
        //企业维度配置, 已经做过配置default->enterprise的覆盖
        EnterpriseNemoProfileUpdateReq enterpriseConfigModel = enterpriseNemoProfileService.getEnterpriseProfile(enterpriseId);
        //部门维度配置
        List<DeptConfigPO> deptConfigList = deptConfigMapper.getDeptConfig(enterpriseId, deptId);

        //配置覆盖
        Map<String, List<DeviceConfigUpdate>> collectMap = new HashMap<>();
        for (DeviceConfigUpdate deviceConfigUpdate : enterpriseConfigModel.getConfigs()) {
            String mapKey = "";
            if (StringUtils.isBlank(deviceConfigUpdate.getClientConfigName())){
                mapKey = "common-"+deviceConfigUpdate.getConfigName();
            } else {
                mapKey = deviceConfigUpdate.getClientConfigName() + "-" + deviceConfigUpdate.getConfigName();
            }
            collectMap.merge(mapKey, CollectionUtil.newArrayList(deviceConfigUpdate), (oldVal, currVal) -> {
                if (CollectionUtil.isNotEmpty(currVal)) {
                    oldVal.add(currVal.get(0));
                }
                return oldVal;
            });
        }
        if (CollectionUtil.isNotEmpty(deptConfigList)){
            for (DeptConfigPO item:deptConfigList){
                List<DeviceConfigUpdate> updateVauleList = collectMap.get(item.getClientConfigName() + "-" + item.getConfigName());
                if (CollectionUtil.isNotEmpty(updateVauleList)){
                    updateVauleList.stream().forEach(configItem -> {
                        configItem.setConfigValue(item.getConfigValue());
                    });
                } else {
                    collectMap.put(item.getClientConfigName()+"-"+item.getConfigName(),
                            CollectionUtil.newArrayList(new DeviceConfigUpdate(item.getConfigName(), item.getConfigValue(), item.getClientConfigName())));
                }
            }
        }
        List<DeviceConfigUpdate> resultList = collectMap.values().stream().flatMap(item -> item.stream()).collect(Collectors.toList());
        enterpriseConfigModel.setConfigs(resultList);
        return enterpriseConfigModel;
    }

    @Override
    public List<DeptConfigPO> getOverrideMergeDeptConfigList(String enterpriseId, String deptId) {
        EnterpriseNemoProfileUpdateReq overrideDeptConfig = getOverrideDeptConfig(enterpriseId, deptId, -1);
        List<DeptConfigPO> deptConfigList = overrideDeptConfig.getConfigs().stream().map(DeptConfigPO::build).collect(Collectors.toList());
        return deptConfigList;
    }

    @Override
    public List<DeptConfigPO> getSourceDeptConfig(String enterpriseId, String deptId) {
        return deptConfigMapper.getDeptConfig(enterpriseId, deptId);
    }


    @Override
    @Transactional
    public void deleteByDeptId(String enterpriseId, String deptId, String configName) {
        deptConfigMapper.deleteByDeptId(enterpriseId, deptId, configName);
        DeviceConfigUpdate deviceConfigUpdate = new DeviceConfigUpdate(configName, null, "UIDisplayCustomization");
        //发送配置变更消息
        applicationEventPublisher.publishEvent(new DeptConfigChangedEvent(Collections.singletonList(deviceConfigUpdate), enterpriseId, deptId));
    }

    @Override
    public List<DeptConfigPO> findConfigListByDeptIds(String enterpriseId, List<String> deptIdList) {
        return deptConfigMapper.findConfigListByDeptIdList(enterpriseId, deptIdList, "");
    }
}
