package com.xylink.configserver.proxy;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixProperty;
import com.xylink.configserver.data.bo.department.MainDeptFetchBO;
import com.xylink.configserver.data.bo.department.UserDeviceDeptCascadeMainDeptBO;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.enums.RemoteCallUriEnum;
import com.xylink.configserver.exception.ServiceException;
import com.xylink.configserver.proxy.response.OmsShortMessageResponse;
import com.xylink.configserver.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.util.*;

import static com.xylink.configserver.util.Constants.*;

@Slf4j
@Component
public class InternalApiProxy {

    @Value("${common.restApiInternalPrefix}")
    String restApiInternalPrefix;

    @Autowired
    private RestTemplate restTemplate;


    @HystrixCommand(commandKey = "remoteCallContact", fallbackMethod = "getMainDeptFallback",
            commandProperties = {
                    @HystrixProperty(name = "execution.isolation.thread.timeoutInMilliseconds", value = "1000")
            })
    public UserDeviceDeptCascadeMainDeptBO getMainDept(long uid, long did, String enterpriseId) {
        MainDeptFetchBO mainDeptFetchBO = new MainDeptFetchBO();
        mainDeptFetchBO.setEnterpriseId(enterpriseId);
        if (uid != -1) {
            mainDeptFetchBO.setUserIds(Lists.newArrayList(uid));
        }
        if (did != -1) {
            mainDeptFetchBO.setDeviceIds(Lists.newArrayList(did));
        }
        HttpEntity<MainDeptFetchBO> httpEntity = new HttpEntity<>(mainDeptFetchBO);
        log.info("RC-Req-Uri:{}, HttpEntity:{}", RemoteCallUriEnum.CONTACT_FETCH.getUri(restApiInternalPrefix),
                httpEntity);
        ResponseEntity<UserDeviceDeptCascadeMainDeptBO> response;
        try {
            response = restTemplate.postForEntity(
                    RemoteCallUriEnum.CONTACT_FETCH.getUri(restApiInternalPrefix),
                    httpEntity,
                    UserDeviceDeptCascadeMainDeptBO.class);
        } catch (HttpStatusCodeException e) {
            log.error("remote call to contact error,url:{}", RemoteCallUriEnum.CONTACT_FETCH.getUri(), e);
            return null;
        }
        UserDeviceDeptCascadeMainDeptBO res = response.getBody();
        log.info("RC-getMainDept:{}", res);
        return res;
    }

    /**
     * Called by method named getMainDept, so don`t remove it even through looks unuseful.
     */
    public UserDeviceDeptCascadeMainDeptBO getMainDeptFallback(long uid, long did, String enterpriseId) {
        log.error("hystrix fallback: contact timeout");
        return null;
    }

    public List<String> getIauthRoleResource(long uid) {
        String url = restApiInternalPrefix + String.format(IAUTH_ROLE_RESOURCE_URL, uid);
        String body = getForEntity(url, String.class, uid);
        log.info("getIauthRoleResource body:{} ,using url:{}", body, url);
        if (StringUtils.isBlank(body)) {
            return Collections.emptyList();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            List<String> result = (List<String>) objectMapper.readValue(body, List.class);
            return result;
        } catch (IOException e) {
            log.error("getIauthRoleResource error", e);
            return Collections.emptyList();
        }
    }

    private <T> T getForEntity(String uri, Class<T> entityClass, Object... uriVariables) {
        ResponseEntity<T> responseEntity;
        try {
            responseEntity = restTemplate.getForEntity(uri, entityClass, uriVariables);
            if (responseEntity.getStatusCode() == HttpStatus.OK) {
                return responseEntity.getBody();
            } else {
                log.error("get {}  returns {}", uri, responseEntity.getStatusCode());
            }
        } catch (Throwable t) {
            log.error("Failed to get {}", uri, t);
        }
        return null;
    }

    public PresenceInfoDto getDeviceOnlineState(String callUri) {
        if (StringUtils.isBlank(callUri)) {
            return null;
        }
        String url = restApiInternalPrefix + DEVICE_PRESENCE_REQUEST;
        try {
            List<String> newUriArray = Arrays.asList(callUri);
            Map<String, List> map = new HashMap<>();
            map.put("uri", newUriArray);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Accept", "application/json");
            HttpEntity httpEntity = new HttpEntity<>(map, headers);
            ResponseEntity<PresenceInfoDto> responseEntity = restTemplate.postForEntity(url, httpEntity, PresenceInfoDto.class);
            log.info("getDeviceOnlineState url:{},responseEntity:{}", url, responseEntity);
            if (HttpStatus.OK == responseEntity.getStatusCode()) {
                return responseEntity.getBody();
            } else {
                log.error("getDeviceOnlineState fail and url:{}", callUri);
            }

        } catch (Exception e) {
            log.error("getDeviceOnlineState fail and url:{},error:{}", callUri, e);
            return null;
        }
        return null;
    }


    public PresenceInfoDto batchGetDeviceState(List<String> callUriList) {
        if (CollectionUtil.isEmpty(callUriList)) {
            return null;
        }
        String url = restApiInternalPrefix + DEVICE_PRESENCE_REQUEST;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Accept", "application/json");
            Map<String, List<String>> bodyMap = CollectionUtil.newMap(CollectionUtil.kv("uri", CollectionUtil.newArrayList(callUriList.toArray(new String[1]))));
            HttpEntity httpEntity = new HttpEntity<>(bodyMap, headers);

            ResponseEntity<PresenceInfoDto> responseEntity = restTemplate.postForEntity(url, httpEntity, PresenceInfoDto.class);
            log.info("getDeviceOnlineState url:{},responseEntity:{}", url, responseEntity);
            if (HttpStatus.OK == responseEntity.getStatusCode()) {
                return responseEntity.getBody();
            }
        } catch (Exception e) {
            log.error("batch get device state error:{}", callUriList, e);
            return null;
        }
        return null;
    }


    public Map<String, String> getEnterpriseConfig(String enterpriseId) {
        String url = restApiInternalPrefix + BUFFET_ENTERPRISE_CONFIG_URL + enterpriseId;
        return getForEntity(url, Map.class);
    }

    public List<EnterpriseNemoFeature> getEnterpriseNemoFeatures(String profileId) {
        String getEnterpriseNemoFeatureFromVcsUrl = restApiInternalPrefix + String.format(VCS_CUSTOMIZED_FEATURES, profileId);
        ResponseEntity<ArrayList> enterpriseFeatures = restTemplate.getForEntity(getEnterpriseNemoFeatureFromVcsUrl, ArrayList.class);
        log.info("getEnterpriseNemoFeatures url:{},response:{}", getEnterpriseNemoFeatureFromVcsUrl, enterpriseFeatures);
        return enterpriseFeatures.getBody();
    }

    public String getEnterpriseDistributorId(String enterpriseId) {
        Map enterprisePO = getEnterpriseConfig(enterpriseId);
        if (enterprisePO != null) {
            return (String) enterprisePO.get("distributor");
        }
        return null;
    }

    public Optional<ConnectionTestDto[]> getEnterpriseConnectionTestUri(String enterpriseId) {
        enterpriseId = StringUtils.trim(enterpriseId);
        if (StringUtils.isBlank(enterpriseId)) {
            return Optional.empty();
        }
        return Optional.ofNullable(getForEntity(restApiInternalPrefix + String.format(CONNECTION_TEST_URI, enterpriseId), ConnectionTestDto[].class, enterpriseId));
    }

    public List<CustomizeFeature> getCustomizeFeatures(UserDevice device, String softVersion) {
        try {
            StringBuffer url = new StringBuffer();
            url.append(restApiInternalPrefix).append(String.format(CUSTOMIZED_FEATURES, device.getId()));
            if (StringUtils.isNotBlank(softVersion)) {
                url.append("?softVersion=").append(softVersion);
            }
            CustomizeFeature[] features = restTemplate.getForObject(url.toString(), CustomizeFeature[].class);
            log.info("getCustomizeFeatures url:{},response:{}", url, features);
            return features == null ? new ArrayList<>() : Arrays.asList(features);
        } catch (Exception e) {
            log.error("Fail to get CustomizeFeatures!", e);
        }
        return null;
    }


    public void removeNemoFeature(String nemoSN, String customizeFeatureId) {
        String url = restApiInternalPrefix + String.format(VCS_DELETE_CLIENT_FEATURE, customizeFeatureId, nemoSN);
        try {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);
            log.info("removeNemoFeature url:{}", url);
            restTemplate.exchange(url, HttpMethod.POST, new HttpEntity<>(httpHeaders), Object.class);
        } catch (Exception e) {
            log.error("removeNemoFeature url:{},error:{}", url, e);
            throw new ServiceException("Failed to remove nemo feature.", ErrorStatus.INTERNAL_ERROR);
        }
    }

    public DeviceResolutionInfo getDeviceResolutionInfoFromBill(String deviceSn) {
        List<DeviceResolutionInfo> list = getDeviceResolutionInfosFromBill(Arrays.asList(deviceSn));
        DeviceResolutionInfo result = null;
        if (!CollectionUtils.isEmpty(list)) {
            result = list.stream()
                    .filter((o) -> o != null && deviceSn.equals(o.getDeviceSn()))
                    .findFirst()
                    .orElse(null);
        }
        log.info("getDeviceResolutionInfoFromBill deviceSn:{},result:{}", deviceSn, result);
        return result;
    }

    public List<DeviceResolutionInfo> getDeviceResolutionInfosFromBill(List<String> deviceSns) {
        if (CollectionUtils.isEmpty(deviceSns)) {
            return Collections.emptyList();
        }
        String url = restApiInternalPrefix + BILL_RESOLUTION_URL;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Accept", "application/json");
            HttpEntity httpEntity = new HttpEntity<>(deviceSns, headers);
            ResponseEntity<DeviceResolutionInfo[]> responseEntity = restTemplate.postForEntity(url, httpEntity, DeviceResolutionInfo[].class);
            log.info("getDeviceOnlineState url:{},responseEntity:{}", url, responseEntity);
            if (HttpStatus.OK == responseEntity.getStatusCode()) {
                DeviceResolutionInfo[] result = responseEntity.getBody();
                return result.length > 0 ? Arrays.asList(result) : Collections.emptyList();
            } else {
                log.error("get4KInfoFromBill fail and url:{}", Arrays.toString(deviceSns.toArray()));
            }

        } catch (Exception e) {
            log.error("get4KInfoFromBill fail and url:{},error:{}", Arrays.toString(deviceSns.toArray()), e);
            return Collections.emptyList();
        }
        return Collections.emptyList();
    }

    public String getCustomSmsSignature(String enterpriseId) {
        try {
            String url = restApiInternalPrefix + OMS_SHORT_MESSAGE_URL + enterpriseId;
            ResponseEntity<OmsShortMessageResponse> data = restTemplate.getForEntity(url, OmsShortMessageResponse.class);
            OmsShortMessageResponse result = data.getBody();
            return result != null ? result.getCustomEnterpriseName() : null;
        } catch (Exception e) {
            log.error("getCustomSmsSignature error.", e);
            return null;
        }
    }


    // 游标查询如果返回数据有结果说明查询没有结束
    public ContactScrollModel scrollGetCurrentDeptDeviceFormContact(String enterpriseId, String deptId, String scrollId,List<String> excludeDeptIdList) {
        try {
            String url = restApiInternalPrefix + CONTACT_SCROLL_DEVICE_BY_DEPT_URL;
            Map<String, Object> requestBodyMap = CollectionUtil.newMap(
                    CollectionUtil.kv("resourceType", 1),
                    CollectionUtil.kv("depIds", CollectionUtil.newArrayList(deptId)),
                    CollectionUtil.kv("enterpriseId", enterpriseId),
                    CollectionUtil.kv("onlyMainDep", true),     //仅返回主部门的数据
                    CollectionUtil.kv("limit", 200)    //每次查询200个
            );
            //如果游标id不为空则需要传入游标id作为查询条件
            if (StringUtils.isNotBlank(scrollId)) {
                requestBodyMap.put("scrollId", scrollId);
            }

            // excludeDeptIdList中的部门及其子部门下的数据不返回
            if (CollectionUtil.isNotEmpty(excludeDeptIdList)){
                requestBodyMap.put("excludeDep", CollectionUtil.newMap(
                        CollectionUtil.kv("depIds", excludeDeptIdList),
                        CollectionUtil.kv("includeSubDep", true)
                ));
            }

            log.info("scroll get contact, url:{}, body:{}", url, requestBodyMap);
            ResponseEntity<String> respEntity = restTemplate.postForEntity(url, requestBodyMap, String.class, "");
            log.info("scroll get contact, result:{}", respEntity);
            if (respEntity.getStatusCode().is2xxSuccessful()) {
                ContactScrollModel contactScrollModel = new ObjectMapper().readValue(respEntity.getBody(), new TypeReference<ContactScrollModel>() {
                });
                return contactScrollModel;
            }
        } catch (Exception e) {
            log.error("post contact parse result error", e);
        }
        return null;
    }


    public ContactDeptModel getDeptInfoFromContact(String enterpriseId, long id, String selectType) {
        try {
            String url = restApiInternalPrefix + CONTACT_ONE_LEVEL_DEPT_BY_DEVICE_URL;
            Map<String, Object> requestBodyMap = CollectionUtil.newMap(
                    CollectionUtil.kv("enterpriseId", enterpriseId),
                    CollectionUtil.kv("maxDepartmentLevel", 1)      //只查询一级部门下的设备
            );
            if (StringUtils.equalsIgnoreCase("user", selectType)) {
                //当作用户来查
                requestBodyMap.put("userIds", CollectionUtil.newArrayList(id));
            } else {
                //当作设备来查
                requestBodyMap.put("deviceIds", CollectionUtil.newArrayList(id));
            }
            ResponseEntity<String> respEntity = restTemplate.postForEntity(url, requestBodyMap, String.class, "");
            if (respEntity.getStatusCode().is2xxSuccessful()) {
                ContactDeptModel contactRespModel = new ObjectMapper().readValue(respEntity.getBody(), new TypeReference<ContactDeptModel>() {
                });
                return contactRespModel;
            }
        } catch (Exception e) {
            log.error("post contact parse result error", e);
        }
        return null;
    }


    public String getOneLevelDeptByDeviceIdFromContact(String enterpriseId, long deviceId) {
        try {
            ContactDeptModel deptInfo = getDeptInfoFromContact(enterpriseId, deviceId, "device");
            if (deptInfo == null || deptInfo.getDevice() == null) {
                return "-1";
            }

            return deptInfo.getDevice().stream()
                    .filter(deviceItem -> Long.parseLong(deviceItem.getDeviceId()) == deviceId)
                    .findFirst()
                    .map(deviceItem -> deviceItem.getDepartmentAndLevels().stream()
                            .filter(levelItem -> levelItem.getLevel() == 1)
                            .findFirst()
                            .map(item -> item.getDeptId())
                            .orElse("-1"))
                    .orElse("-1");
        } catch (Exception e) {
            log.error("handler dept error", e);
            return "-1";
        }
    }
    public String getOneLevelDeptByUserProfileIdFromContact(String enterpriseId, long userProfileId) {
        try {
            return Optional.ofNullable(getDeptInfoFromContact(enterpriseId, userProfileId, "user"))
                    .map(item -> item.getUser())
                    .map(item -> item.stream().filter(deviceItem -> Long.parseLong(deviceItem.getUserId()) == userProfileId).findFirst().get())
                    .map(item -> item.getDepartmentAndLevels())
                    .map(item -> item.stream().filter(levelItem -> levelItem.getLevel() == 1).findFirst().get())
                    .map(item -> item.getDeptId())
                    .orElse("-1");
        } catch (Exception e) {
            log.error("handler dept error", e);
            return "-1";
        }
    }

    public boolean fetchDeptSwitch() {
        String url = restApiInternalPrefix + CONTACT_DEPT_SWITCH_URL;
        try {
            String body = restTemplate.exchange(
                    UriComponentsBuilder.fromHttpUrl(url)
                            .queryParam("enterpriseId", "default_enterprise")
                            .queryParam("configName", "ENABLED_DEPARTMENT_CUSTOM")
                            .build()
                            .toUri(),
                    HttpMethod.GET,
                    null,
                    String.class
            ).getBody();
            return "true".equalsIgnoreCase(body);
        } catch (HttpClientErrorException e) {
            log.error("请求参数错误 [URL:{}] [Status:{}] [Response:{}]",
                    url, e.getStatusCode(), e.getResponseBodyAsString());
            return false;
        } catch (ResourceAccessException e) {
            log.error("网络连接失败 [URL:{}] [Error:{}]", url, e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("未知错误 [URL:{}]", url, e);
            return false;
        }
    }

    public String getDeptRootId(String enterpriseId) {
        log.info("queryRootDepartmentByEnterpriseId parameter--->{}",enterpriseId);
        try {
            String url = restApiInternalPrefix + CONTACT_QUERY_ROOT_DEPARTMENT_BY_ENTERPRISE_ID;
            ResponseEntity<JsonNode> respEntity = restTemplate.getForEntity(url, JsonNode.class, enterpriseId);
            if (respEntity.getStatusCode().is2xxSuccessful()) {
                log.info("queryRootDepartmentByEnterpriseId body--->{}",respEntity.getBody());
                String rootDeptId = Optional.of(respEntity.getBody())
                        .map(body -> body.get("id").asText())
                        .orElse(null);
                return rootDeptId;
            }
        } catch (Exception e) {
            log.error("rest contact parse result error", e);
        }
        return null;
    }


    public List<String> findChildDeptBySourceDeptId(String enterpriseId, String deptId) {
        try {
            String url = restApiInternalPrefix + CONTACT_CHILD_DEPT_BY_DEPT_ID;
            url += "?enterpriseId="+enterpriseId+"&departmentId="+deptId;

            ResponseEntity<String> respEntity = restTemplate.getForEntity(url, String.class, "");
            if (respEntity.getStatusCode().is2xxSuccessful()){
                List<String> childDeptIdList = new ObjectMapper().readValue(respEntity.getBody(), new TypeReference<List<String>>() {});
                return childDeptIdList;
            }
        } catch (Exception e) {
            log.error("rest contact parse result error", e);
        }
        return null;
    }
}
