package com.xylink.configserver.proxy;

import com.xylink.configserver.data.model.BillGateWayResourceRequest;
import com.xylink.configserver.data.model.BillGateWayResourceResponse;
import com.xylink.configserver.data.model.BillResponse;
import com.xylink.configserver.util.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Slf4j
@Component
public class GatewayProxy {

    @Value("${common.restApiInternalPrefix}")
    String restApiInternalPrefix;

    @Autowired
    private RestTemplate restTemplate;

    public BillResponse<BillGateWayResourceResponse> getResource(BillGateWayResourceRequest request) throws RestClientException {
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromHttpUrl(getBillGateWay())
                .queryParam("enterpriseId", request.getEnterpriseId())
                .queryParam("type", request.getType());
        ResponseEntity<BillResponse<BillGateWayResourceResponse>> response = restTemplate.exchange(uriComponentsBuilder.build().encode().toUri(),
                HttpMethod.GET,
                null,
                new ParameterizedTypeReference<BillResponse<BillGateWayResourceResponse>>() {
                }
        );
        return response.getBody();
    }

    private String getBillGateWay() {
        return restApiInternalPrefix + Constants.BILL_GATEWAY_RESOURCE_URL;
    }
}
