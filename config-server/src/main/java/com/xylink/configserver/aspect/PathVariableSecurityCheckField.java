package com.xylink.configserver.aspect;

import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @since 2020-05-13 18:28
 */
@Target({ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface PathVariableSecurityCheckField {

    @AliasFor("field")
    String value() default "";

    @AliasFor("value")
    String field() default "";
}
