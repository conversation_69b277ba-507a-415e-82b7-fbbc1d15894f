package com.xylink.configserver.aspect;

import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.data.model.UserDevice;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.RestException;
import com.xylink.configserver.util.RestApiContext;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2020-05-13 18:10
 */
@Aspect
@Component
public class PathVariableSecurityAspect {

    @Pointcut("@annotation(com.xylink.configserver.aspect.PathVariableSecurityCheck)")
    private void pathVariableSecurityAspect() {

    }

    @Before("pathVariableSecurityAspect()")
    public void security(JoinPoint joinPoint) {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Object[] args = joinPoint.getArgs();
        Annotation[][] parameterAnnotations = method.getParameterAnnotations();
        String field = null;
        Object value = null;
        for (int i = 0; i < parameterAnnotations.length; i++) {
            for (Annotation annotation : parameterAnnotations[i]) {
                PathVariableSecurityCheckField pathVariableSecurityCheckField = AnnotationUtils.getAnnotation(annotation, PathVariableSecurityCheckField.class);
                if (Objects.nonNull(pathVariableSecurityCheckField)) {
                    value = args[i];
                    field = pathVariableSecurityCheckField.value();
                    break;
                }
            }
        }

        UserDevice device = RestApiContext.getCurrentDevice();
        if (Objects.nonNull(device) && StringUtils.isNotBlank(field)) {
            if (value == null) {
                throw new RestException(HttpStatus.CLIENT_ERROR_FORBIDDEN, ErrorStatus.INVALID_PARAMETER.getErrorCode(), ErrorStatus.INVALID_PARAMETER.getResId());
            }

            // == 优先比较对象
            Long id = (Long) value;
            if ("userId".equals(field)) {
                if (!id.equals(device.getUserProfileID())) {
                    throw new RestException(HttpStatus.CLIENT_ERROR_FORBIDDEN, ErrorStatus.INVALID_PARAMETER.getErrorCode(), ErrorStatus.INVALID_PARAMETER.getResId());
                }
            } else {
                if (!id.equals(device.getId())) {
                    throw new RestException(HttpStatus.CLIENT_ERROR_FORBIDDEN, ErrorStatus.INVALID_PARAMETER.getErrorCode(), ErrorStatus.INVALID_PARAMETER.getResId());
                }
            }
        }
    }


}
