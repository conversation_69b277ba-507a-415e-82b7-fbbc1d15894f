package com.xylink.configserver.process.impl;

import com.xylink.configserver.data.model.DeviceInfo;
import com.xylink.configserver.data.model.FeatureProvision;
import com.xylink.configserver.data.model.RestNemoConfig;
import com.xylink.configserver.process.NemoFeatureProcessor;
import org.springframework.stereotype.Component;

@Component
public class DefaultNemoFeatureProcessor implements NemoFeatureProcessor {

    @Override
    public RestNemoConfig processNemoFeatureProvision(FeatureProvision feature, DeviceInfo device) {
        String value = String.valueOf(feature.isEnable());
        if (feature.getValue() != null && !feature.getValue().trim().isEmpty()) {
            value = feature.getValue();
        }
        return new RestNemoConfig(feature.getFeatureName(), value, feature.isReboot());
    }
}
