package com.xylink.configserver.process;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.configserver.data.model.OceanData;
import com.xylink.configserver.data.model.OceanDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class OceanConfigChangeProcessor {

    public static final ObjectMapper MAPPER = new ObjectMapper();

    public static  <T extends OceanData> String toJsonStr(OceanDto<T> obj) {
        try {
            return MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            log.error("to json string error.", e);
            return null;
        }
    }
}
