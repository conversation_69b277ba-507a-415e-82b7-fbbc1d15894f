package com.xylink.configserver.process;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.configserver.data.model.DeviceInfo;
import com.xylink.configserver.data.model.RestNemoConfig;
import com.xylink.configserver.data.model.SpecialContact;
import com.xylink.configserver.enums.ContactUsage;
import com.xylink.configserver.service.SpecialContactService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

@Slf4j
@Component
public class SpecialContactProcessor implements NemoConfigProcessor {

    private static final ObjectMapper mapper = new ObjectMapper();

    @Autowired
    private SpecialContactService specialContactService;

    @Override
    public RestNemoConfig processNemoConfig(String configName, String configValue, DeviceInfo device) {

        List<SpecialContact> contacts = specialContactService.getNemoSpecialContacts(device.getSn(), ContactUsage.Voice);
        try {
            return new RestNemoConfig(configName, mapper.writeValueAsString(contacts), false);
        } catch (IOException e) {
            log.error("Failed to write value.", e);
            return new RestNemoConfig(configName, "", false);
        }

    }
}
