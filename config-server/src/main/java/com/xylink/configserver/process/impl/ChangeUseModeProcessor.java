package com.xylink.configserver.process.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.configserver.data.model.ChangeUseModeConfig;
import com.xylink.configserver.data.model.DeviceInfo;
import com.xylink.configserver.data.model.FeatureProvision;
import com.xylink.configserver.data.model.RestNemoConfig;
import com.xylink.configserver.process.NemoFeatureProcessor;
import com.xylink.configserver.service.DeviceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
public class ChangeUseModeProcessor implements NemoFeatureProcessor {

    private final ObjectMapper mapper = new ObjectMapper();

    @Autowired
    DeviceService deviceService;

    @Override
    public RestNemoConfig processNemoFeatureProvision(FeatureProvision feature, DeviceInfo device) {

        ChangeUseModeConfig c = new ChangeUseModeConfig();
        if(deviceService.isEnterpriseNemoWithFixedModel(device.getSn())) {
            c.setEnable(false);
        } else {
            c.setEnable(feature.isEnable());
        }
        try {
            return new RestNemoConfig(feature.getFeatureName(), mapper.writeValueAsString(c), true);
        } catch (IOException e) {
            log.error("Failed to add changeusemode config", e);
            return null;
        }
    }
}
