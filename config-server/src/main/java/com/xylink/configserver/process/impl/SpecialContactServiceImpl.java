package com.xylink.configserver.process.impl;

import com.xylink.configserver.data.model.EnterpriseNemo;
import com.xylink.configserver.data.model.SpecialContact;
import com.xylink.configserver.data.model.SpecialNemoFeature;
import com.xylink.configserver.enums.ContactStatus;
import com.xylink.configserver.enums.ContactTarget;
import com.xylink.configserver.enums.ContactUsage;
import com.xylink.configserver.enums.SpecialFeatureType;
import com.xylink.configserver.mapper.EnterpriseNemoMapper;
import com.xylink.configserver.mapper.SpecialContactMapper;
import com.xylink.configserver.mapper.SpecialNemoFeatureMapper;
import com.xylink.configserver.service.DeviceService;
import com.xylink.configserver.service.SpecialContactService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Component
public class SpecialContactServiceImpl implements SpecialContactService {

    @Autowired
    DeviceService deviceService;

    @Autowired
    EnterpriseNemoMapper enterpriseNemoMapper;

    @Autowired
    SpecialContactMapper specialContactMapper;

    @Autowired
    SpecialNemoFeatureMapper specialNemoFeatureMapper;

    @Override
    public List<SpecialContact> getNemoSpecialContacts(String nemoSN, ContactUsage contactUsage) {
        Set<String> addedIds = new HashSet<>();
        List<SpecialContact> contacts = new ArrayList<>();
        boolean enterpriseNemo = false;
        Set<String> disabledContactIds = new HashSet<>();
        //3. enterprise config
        if(deviceService.isEnterpriseNemo(nemoSN)) {
            EnterpriseNemo eNemo = enterpriseNemoMapper.getEnterpriseNemo(nemoSN);
            if(eNemo != null && eNemo.getEnterpriseProfileId() != null) {
                List<SpecialContact> profileContacts = specialContactMapper.getEnterpriseSpecialContacts(eNemo.getEnterpriseProfileId(),contactUsage.getUsage());
                addContacts(profileContacts.toArray(new SpecialContact[0]), contacts, addedIds, disabledContactIds);
            }
            enterpriseNemo = true;
        }
        //4. special config
        List<SpecialNemoFeature> features = specialNemoFeatureMapper.getNemoFeatures(nemoSN, 0, SpecialFeatureType.CONFIG.getValue());
        for(SpecialNemoFeature feature : features) {
            List<SpecialContact> featureContacts = specialContactMapper.getNemoSpecialContacts(feature.getId(), contactUsage.getUsage());
            addContacts(featureContacts.toArray(new SpecialContact[0]), contacts, addedIds, disabledContactIds);
        }

        //2. common config
        List<SpecialContact> toAll = specialContactMapper.getSpecialContactsByTarget(ContactTarget.All.getValue(),
                contactUsage.getUsage());
        addContacts(toAll.toArray(new SpecialContact[0]), contacts, addedIds, disabledContactIds);
        if(enterpriseNemo) {
            List<SpecialContact> enAll = specialContactMapper.getSpecialContactsByTarget(ContactTarget.All_Enterprise.getValue(),
                    contactUsage.getUsage());
            addContacts(enAll.toArray(new SpecialContact[0]), contacts, addedIds, disabledContactIds);
        }

        return contacts;
    }

    private void addContacts(SpecialContact[] pos, List<SpecialContact> contacts, Set<String> addedIds, Set<String> disabledIds) {
        for(SpecialContact po : pos) {
            if(disabledIds.contains(po.getId())){
                continue;
            }
            if(po.getContactStatus() == ContactStatus.DISABLED.getValue()) {
                disabledIds.add(po.getId());
                continue;
            }
            if(!addedIds.contains(po.getId())) {
                contacts.add(po);
                addedIds.add(po.getId());
            }
        }
    }
}
