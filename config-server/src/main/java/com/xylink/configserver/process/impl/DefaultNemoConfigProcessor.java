package com.xylink.configserver.process.impl;

import com.xylink.configserver.data.model.DeviceInfo;
import com.xylink.configserver.data.model.RestNemoConfig;
import com.xylink.configserver.process.NemoConfigProcessor;
import org.springframework.stereotype.Component;

@Component
public class DefaultNemoConfigProcessor implements NemoConfigProcessor {

    @Override
    public RestNemoConfig processNemoConfig(String configName,
                                            String configValue, DeviceInfo device) {
        return new RestNemoConfig(configName, configValue, false);
    }

}
