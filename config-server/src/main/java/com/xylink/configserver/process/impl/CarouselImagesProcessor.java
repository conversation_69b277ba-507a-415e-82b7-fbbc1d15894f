package com.xylink.configserver.process.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.configserver.data.model.DeviceInfo;
import com.xylink.configserver.data.model.RestNemoConfig;
import com.xylink.configserver.process.NemoConfigProcessor;
import com.xylink.configserver.util.I18nNemoConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Slf4j
@Component
public class CarouselImagesProcessor implements NemoConfigProcessor {

    private static final ObjectMapper mapper = new ObjectMapper();

    @Override
    public RestNemoConfig processNemoConfig(String configName,
                                            String configValue, DeviceInfo device) {
        if(configValue != null && !configValue.trim().isEmpty()) {
            String[] urls = I18nNemoConfig.getLocalizedValue(configValue, device.getLocale());
            try {
                return new RestNemoConfig(configName, mapper.writeValueAsString(urls), false);
            } catch (IOException e) {
                log.error("Failed to serialized urls.", e);
            }
        }

        return new RestNemoConfig(configName, "", false);
    }
}
