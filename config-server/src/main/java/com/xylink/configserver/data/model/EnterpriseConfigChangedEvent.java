package com.xylink.configserver.data.model;

import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2021/5/26 9:43 上午
 */
public class EnterpriseConfigChangedEvent extends ApplicationEvent {

    private final String enterpriseId;

    public EnterpriseConfigChangedEvent(EnterpriseNemoConfig source, String enterpriseId) {
        super(source);
        this.enterpriseId = enterpriseId;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }
}
