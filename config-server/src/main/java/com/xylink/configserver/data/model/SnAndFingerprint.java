package com.xylink.configserver.data.model;

import org.apache.commons.lang3.StringUtils;

public class SnAndFingerprint {

    public static final String DEVICE_WITHOUT_FINGERPRINT = "2D1446"; //14年46周生产

    private final String sn;
    private final String fingerprint;

    public SnAndFingerprint(String sn, String fingerprint) {
        this.sn = sn;
        this.fingerprint = fingerprint;
    }

    public String getSn() {
        return sn;
    }

    public String getFingerprint() {
        return fingerprint;
    }

    public boolean needCheckFingerprint() {
        return !sn.startsWith(DEVICE_WITHOUT_FINGERPRINT);
    }

    public boolean needUnbindNemo(UserDevice device){
        return device != null &&
                !StringUtils.equals(device.getFingerprint(), getFingerprint()) &&
                needCheckFingerprint() &&
                StringUtils.equals(device.getDeviceSN(), sn);
    }

    @Override
    public String toString() {
        return "sn: " + sn + ", fingerprint: " + fingerprint;
    }

}
