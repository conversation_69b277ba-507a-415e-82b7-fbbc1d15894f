package com.xylink.configserver.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 *ClassName:DeviceSubtypeEntity
 *Package:com.xylink.configserver.data.entity
 *Description:
 *<AUTHOR>
 *@Date 2024/10/17-16:27
 *@Version: v1.0
 *
 */
@Data
@TableName(value = "libra_device_subtype")
public class DeviceSubtypeEntity implements Serializable {
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    @TableField(value = "sub_type")
    private Short subType;

    @TableField(value = "model")
    private String model;

    @TableField(value = "package_name")
    private String packageName;

    @TableField(value = "display_model")
    private String displayModel;

    @TableField(value = "reference_model")
    private Short referenceModel;

    private static final long serialVersionUID = 1L;
}