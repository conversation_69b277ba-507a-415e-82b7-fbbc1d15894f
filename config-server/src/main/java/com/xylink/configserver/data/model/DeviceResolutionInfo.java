package com.xylink.configserver.data.model;

import com.xylink.configserver.data.enums.Resolution;
import lombok.Data;

/**
 * Created by niulong on 2020/6/18 4:47 PM
 */
@Data
public class DeviceResolutionInfo {
    private String deviceSn;
    private Long expireTime;
    private String resolutionRatio;

    public boolean support4k(){
        return Resolution.K4.name().equals(this.getResolutionRatio()) && this.getExpireTime() > System.currentTimeMillis();
    }
}
