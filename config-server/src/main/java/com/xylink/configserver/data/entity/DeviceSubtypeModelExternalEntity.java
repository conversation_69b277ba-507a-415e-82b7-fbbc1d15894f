package com.xylink.configserver.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;

/**
 *ClassName:DeviceSubtypeModelExternalEntity
 *Package:com.xylink.configserver.data.entity
 *Description:
 *<AUTHOR>
 *@Date 2024/11/13-10:54
 *@Version: v1.0
 *
 */
/**
 * 设备基础属性扩展表
 */
@Data
@TableName(value = "libra_device_subtype_model_external")
public class DeviceSubtypeModelExternalEntity implements Serializable {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 设备子类型
     */
    @TableField(value = "subtype")
    private Integer subtype;

    /**
     * avc多画面能力
     */
    @TableField(value = "multi_image_avc")
    private String multiImageAvc;

    private static final long serialVersionUID = 1L;
}