package com.xylink.configserver.data.model.specialfeature;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("libra_special_feature_nemo")
public class SpecialFeatureNemoEntity {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    private String featureId;

    private Integer status;

    private String nemoSn;

    private Integer dataType;

    private String deptName;

    private Date updateTime;

}
