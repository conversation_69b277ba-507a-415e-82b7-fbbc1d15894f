package com.xylink.configserver.data.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class RestNemoConfig implements Serializable {

    private String name;
    private String value;
    private boolean reboot;

    public RestNemoConfig() {
    }

    public RestNemoConfig(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public RestNemoConfig(String name, String value, boolean reboot) {
        this.name = name;
        this.value = value;
        this.reboot = reboot;
    }

    public static RestNemoConfig build(DeviceConfigUpdate update) {
        RestNemoConfig restNemoConfig = new RestNemoConfig();
        restNemoConfig.setName(update.getConfigName());
        restNemoConfig.setValue(update.getConfigValue());
        restNemoConfig.setReboot(false);
        return restNemoConfig;
    }
}
