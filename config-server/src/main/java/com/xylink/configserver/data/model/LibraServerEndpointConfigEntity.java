package com.xylink.configserver.data.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("libra_server_endpoint_config")
public class LibraServerEndpointConfigEntity  implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String configName;

    private String configValue;

    private Long userProfileId;

    private Long deviceId;
}
