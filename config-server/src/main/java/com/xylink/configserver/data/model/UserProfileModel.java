package com.xylink.configserver.data.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/11/3 22:43
 */
@TableName("libra_user_profile")
@Data
public class UserProfileModel {

    @TableId(type = IdType.AUTO)
    private Long id;
    private String userDisplayName;
    private String userPhone;
    private String userPicture;

    private Integer debug;

    private Long userCreateTime;

    private String phoneCountryCode;
    private String identityKey;

    private String userId;

    private Integer type;
    private String enterpriseId;
    private String email;
    private String callNum;
    private String userFlag;

    private Date updateTime;
    private Integer secretLevel;

}
