package com.xylink.configserver.data.dto;

import com.xylink.configserver.data.bo.TempSeriesConfigBO;
import com.xylink.configserver.data.entity.BoxEnterpriseEntity;
import com.xylink.configserver.data.entity.DeviceSubtypeEntity;
import com.xylink.configserver.data.entity.DeviceSubtypeModelExternalEntity;
import com.xylink.configserver.data.model.CommonGroupConfig;
import com.xylink.configserver.data.model.LibraDeviceSubtypeModel;
import com.xylink.configserver.data.model.NemoBaseDefaultConfig;
import com.xylink.configserver.data.model.deviceseries.DeviceSeriesEntity;
import com.xylink.configserver.data.model.deviceseries.DeviceSeriesSubtypeEntity;
import lombok.Data;

import java.util.List;

/**
 * ClassName:SubtypeExportDto
 * Package:com.xylink.configserver.data.dto
 * Description:
 *
 * <AUTHOR>
 * @Date 2025/4/28-16:14
 * @Version: v1.0
 */
@Data
public class SubtypeExportImportDto {

    private LibraDeviceSubtypeModel libraDeviceSubtypeModel;

    private List<DeviceSubtypeEntity> deviceSubtypeEntities;

    private DeviceSubtypeModelExternalEntity deviceSubtypeModelExternalEntity;

    private List<NemoBaseDefaultConfig> nemoBaseDefaultConfigs;

    private List<CommonGroupConfig> commonGroupConfigs;

    private DeviceSeriesSubtypeEntity deviceSeriesSubtypeEntity;

    private DeviceSeriesEntity deviceSeriesEntity;

    private List<TempSeriesConfigBO> tempSeriesConfigs;

    private List<TempSeriesConfigBO> tempSubtypeConfigs;

    private List<BoxEnterpriseEntity> boxEnterpriseEntities;

}
