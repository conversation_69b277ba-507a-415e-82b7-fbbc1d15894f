package com.xylink.configserver.data.model;

public class BillResponse<T> {

    private int code;
    private String msg;
    private T data;

    public boolean isOk() {
        return this.code == 0;
    }

    public String getErrorMsg() {
        return isOk() ? "" : "{code=" + code + ", msg='" + msg + "\'}";
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    @Override
    public String toString() {
        return "BillResponse{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", data=" + data +
                '}';
    }
}
