package com.xylink.configserver.data.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * ClassName:NewDeviceBuildInfoRequest
 * Package:com.xylink.configserver.data.model
 * Description:
 *
 * <AUTHOR>
 * @Date 2024/10/17-15:13
 * @Version: v1.0
 */
@Data
@ToString
@EqualsAndHashCode
public class NewDeviceBuildInfoRequest implements Serializable {

    // 设备型号
    private Integer subtype;
    // 设备名称
    private String deviceName;
    private String typeCategory;
    private String model;
    private String newSeries;
    private List<NewDeviceBuildParam> params;
}
