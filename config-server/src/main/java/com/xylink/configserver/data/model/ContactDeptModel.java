package com.xylink.configserver.data.model;

import lombok.Data;

import java.util.List;

@Data
public class ContactDeptModel {

    private List<UserModel> user;

    private List<DeviceModel> device;

    private List<DeptModel> department;


    @Data
    public static class UserModel{
        private String userId;
        private List<LevelModel> departmentAndLevels;
    }

    @Data
    public static class DeviceModel{
        private String deviceId;
        private List<LevelModel> departmentAndLevels;
    }

    @Data
    public static class DeptModel{
        private String departmentId;
        private List<LevelModel> departmentAndLevels;
    }

    @Data
    public static class LevelModel{
        private String deptId;
        private int level;
    }
}