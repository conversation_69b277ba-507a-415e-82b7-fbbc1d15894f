package com.xylink.configserver.data.model;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

public class DeviceConfigUpdate implements Serializable {
    private String configName;
    private String configValue;
    private String clientConfigName;
    private int deviceSubType;

    public DeviceConfigUpdate() {
    }

    public DeviceConfigUpdate(String configName, String configValue, String clientConfigName) {
        this.configName = configName;
        this.configValue = configValue;
        this.clientConfigName = clientConfigName;
    }

    public String getConfigName() {
        return this.configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getConfigValue() {
        return this.configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public String getClientConfigName() {
        return this.clientConfigName;
    }

    public void setClientConfigName(String clientConfigName) {
        this.clientConfigName = clientConfigName;
    }

    public int getDeviceSubType() {
        return this.deviceSubType;
    }

    public void setDeviceSubType(int deviceSubType) {
        this.deviceSubType = deviceSubType;
    }

    public String getKey() {
        return this.configName + "-" + this.deviceSubType + "-" + (StringUtils.isBlank(this.clientConfigName) ? "common" : this.clientConfigName);
    }

    public String getNoTypeKey() {
        return this.configName + "-" + (StringUtils.isBlank(this.clientConfigName) ? "common" : this.clientConfigName);
    }

    public String toString() {
        return "DeviceConfigUpdate{configName='" + this.configName + '\'' + ", configValue='" + this.configValue + '\'' + ", clientConfigName='" + this.clientConfigName + '\'' + ", deviceSubType=" + this.deviceSubType + '}';
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            DeviceConfigUpdate that = (DeviceConfigUpdate)o;
            if (this.deviceSubType != that.deviceSubType) {
                return false;
            } else {
                label44: {
                    if (this.configName != null) {
                        if (this.configName.equals(that.configName)) {
                            break label44;
                        }
                    } else if (that.configName == null) {
                        break label44;
                    }

                    return false;
                }

                if (this.configValue != null) {
                    if (!this.configValue.equals(that.configValue)) {
                        return false;
                    }
                } else if (that.configValue != null) {
                    return false;
                }

                return this.clientConfigName != null ? this.clientConfigName.equals(that.clientConfigName) : that.clientConfigName == null;
            }
        } else {
            return false;
        }
    }

    public int hashCode() {
        int result = this.configName != null ? this.configName.hashCode() : 0;
        result = 31 * result + (this.configValue != null ? this.configValue.hashCode() : 0);
        result = 31 * result + (this.clientConfigName != null ? this.clientConfigName.hashCode() : 0);
        result = 31 * result + this.deviceSubType;
        return result;
    }
}
