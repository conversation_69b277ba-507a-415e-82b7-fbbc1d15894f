package com.xylink.configserver.data.model;

import lombok.Data;

import java.util.HashSet;
import java.util.Set;

@Data
public class EnterpriseNemoProfile {

    private String id;
    private String displayName;
    private String enterpriseId;
    private boolean specialUI;
    private boolean specialNemoBinding;
    private boolean needAuthentication;
    private boolean enhancePassword;
    private long passwordExpireInterval = 7776000000L;
    private String customizedKey;
    private boolean joinEnterprise;
    private boolean specialClientLogin;


    private Set<EnterpriseNemoConfig> configs = new HashSet<>();

    public EnterpriseNemoProfile() {
    }

    public EnterpriseNemoProfile(String displayName) {
        this.displayName = displayName;
    }
}
