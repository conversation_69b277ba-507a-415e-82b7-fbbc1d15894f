package com.xylink.configserver.data.model.specialfeature;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

@Data
@TableName("libra_special_feature")
public class SpecialFeatureEntity extends Model<SpecialFeatureEntity> {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    private String description;

    private String config;

    private Integer status;

    private Integer appversion;

    private Integer featureType;

    private Boolean specialUiDisplay;

    private String enterpriseId;

}
