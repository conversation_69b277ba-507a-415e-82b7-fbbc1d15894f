package com.xylink.configserver.data.model;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022/7/20 16:04
 */
@Data
@TableName("libra_device_ability")
public class DeviceAbilityModel {

    @TableId
    private Long deviceId;

    private String deviceAbility;

    @TableField("is_enabled")
    private Boolean enabled;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    public DeviceAbilityModel(Long deviceId, String deviceAbility, Boolean enable) {
        this.deviceId = deviceId;
        this.deviceAbility = deviceAbility;
        this.enabled = enable;
    }

}