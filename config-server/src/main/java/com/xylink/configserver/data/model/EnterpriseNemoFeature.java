package com.xylink.configserver.data.model;

import lombok.Data;

@Data
public class EnterpriseNemoFeature {

    private String id;

    private String featureId;

    private int featureStatus;

    private Boolean hasMainShortCut;

    private String profileId;

    private String displayName;

    public EnterpriseNemoFeature() {

    }

    public EnterpriseNemoFeature(String id, int status) {
        this.featureId = id;
        this.featureStatus = status;
    }

    public EnterpriseNemoFeature(String featureId, int status, String profileId) {
        this.featureId = featureId;
        this.featureStatus = status;
        this.profileId = profileId;
    }

}
