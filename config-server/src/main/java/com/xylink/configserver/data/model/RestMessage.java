package com.xylink.configserver.data.model;

public class RestMessage {
    private static final char SEPERATOR = ';';
    private String developerMessage;
    private String userMessage;
    private int errorCode;
    private String moreInfo;

    public RestMessage() {
    }

    public String getDeveloperMessage() {
        return this.developerMessage;
    }

    public void setDeveloperMessage(String developerMessage) {
        this.developerMessage = developerMessage;
    }

    public String getUserMessage() {
        return this.userMessage;
    }

    public void setUserMessage(String userMessage) {
        this.userMessage = userMessage;
    }

    public int getErrorCode() {
        return this.errorCode;
    }

    public void setErrorCode(int errorCode) {
        this.errorCode = errorCode;
    }

    public String getMoreInfo() {
        return this.moreInfo;
    }

    public void setMoreInfo(String moreInfo) {
        this.moreInfo = moreInfo;
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append('{');
        sb.append("errorCode: ").append(this.errorCode).append(';');
        sb.append("userMessage: ").append(this.userMessage).append(';');
        sb.append("developerMessage: ").append(this.developerMessage).append(';');
        sb.append("moreInfo: ").append(this.moreInfo);
        sb.append('}');
        return sb.toString();
    }
}
