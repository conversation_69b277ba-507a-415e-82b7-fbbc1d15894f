package com.xylink.configserver.data.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class BaseConfig extends Model<BaseConfig> {

    public static final String COMMON_CONFIG_KEY = "common";
    @TableId(type = IdType.AUTO)
    protected Long id;
    protected String configName;
    protected String configValue;
    protected String clientConfigName;

    public BaseConfig(String configName, String configValue, String clientConfigName) {
        this.configName = configName;
        this.configValue = configValue;
        this.clientConfigName = clientConfigName;
    }

    public BaseConfig() {
    }

    public void addToConfigs(Map<String, Map<String, String>> theDeviceConfigs) {

        String clientConfigName = getClientConfigName();
        if(StringUtils.isBlank(clientConfigName)) {
            clientConfigName = COMMON_CONFIG_KEY;
        }
        Map<String, String> typedConfigs = theDeviceConfigs.get(clientConfigName);
        if(typedConfigs == null) {
            typedConfigs = new HashMap<>();
            theDeviceConfigs.put(clientConfigName, typedConfigs);
        }
        typedConfigs.put(getConfigName(), getConfigValue());
    }

    public static <T extends BaseConfig> void configPOs2All(List<T> configList, Map<String, Map<String, String>> allConfigs) {
        if(configList == null){
            return;
        }
        for(BaseConfig baseConfig : configList) {
            baseConfig.addToConfigs(allConfigs);
        }
    }

}
