package com.xylink.configserver.data.model;

import lombok.Data;

@Data
public class FeatureProvision {

    private boolean enable = true;
    private boolean reboot = false;
    private String featureName = null;
    private String value = "";

    public FeatureProvision(String featureName, boolean enable, boolean reboot) {
        this.enable = enable;
        this.reboot = reboot;
        this.featureName = featureName;
    }
}
