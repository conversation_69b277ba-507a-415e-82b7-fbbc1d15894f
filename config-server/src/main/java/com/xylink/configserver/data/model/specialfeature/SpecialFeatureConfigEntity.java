package com.xylink.configserver.data.model.specialfeature;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xylink.configserver.data.model.BaseConfig;
import lombok.Data;

@Data
@TableName("libra_special_feature_config")
public class SpecialFeatureConfigEntity extends BaseConfig {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String specialFeatureId;

    private String clientConfigName;

    private String configName;

    private String configValue;

}
