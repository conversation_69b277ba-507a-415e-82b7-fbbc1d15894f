package com.xylink.configserver.data.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * ClassName:BoxEnterpriseEntity
 * Package:com.xylink.configserver.data.entity
 * Description:
 *
 * <AUTHOR>
 * @Date 2024/10/17-17:05
 * @Version: v1.0
 */
@Data
@TableName(value = "libra_box_enterprise")
public class BoxEnterpriseEntity implements Serializable {
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    @TableField(value = "name")
    private String name;

    @TableField(value = "package_name")
    private String packageName;

    @TableField(value = "model_list")
    private String modelList;

    @TableField(value = "dummy_admin_account")
    private String dummyAdminAccount;

    @TableField(value = "app_vendor")
    private String appVendor;

    @TableField(value = "invitation_sms")
    private String invitationSms;

    @TableField(value = "sub_type")
    private Integer subType;

    private static final long serialVersionUID = 1L;
}