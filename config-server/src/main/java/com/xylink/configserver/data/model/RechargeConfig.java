package com.xylink.configserver.data.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * Created by niulong on 2020/6/17 2:20 PM
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("libra_recharge_config")
public class RechargeConfig extends BaseConfig {
    private int configType;
    private String deviceSn;
    private Long configExpireTime;
    private Date createTime;
    private Date updateTime;
}
