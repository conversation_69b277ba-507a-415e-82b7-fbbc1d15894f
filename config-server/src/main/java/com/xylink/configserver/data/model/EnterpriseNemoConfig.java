package com.xylink.configserver.data.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Objects;

@Data
@TableName("libra_enterprise_nemo_config")
public class EnterpriseNemoConfig  extends TypedBaseConfig {

    private String enterpriseProfileId;

    public EnterpriseNemoConfig() {
    }

    public EnterpriseNemoConfig(String name, String value, String clientConfigName, int configType) {
        this.configName = name;
        this.configValue = value;
        this.configType = configType;
        this.clientConfigName = clientConfigName;
    }

    public EnterpriseNemoConfig(String name, String value, String enterpriseProfileId) {
        this(name, value, null, 2, enterpriseProfileId);
    }

    public EnterpriseNemoConfig(String name, String value, String clientConfigName, int configType, String enterpriseProfileId) {
        this.configName = name;
        this.configValue = value;
        this.enterpriseProfileId = enterpriseProfileId;
        this.configType = configType;
        this.clientConfigName = clientConfigName;
    }


    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), configName, configValue, clientConfigName);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
        {return true;}
        if (o == null || getClass() != o.getClass()) {return false;}
        if (!super.equals(o)) {return false;}
        EnterpriseNemoConfig that = (EnterpriseNemoConfig) o;
        return configName.equals(that.configName) &&
                configValue.equals(that.configValue) && ((null== clientConfigName && null==that.clientConfigName) ||
                clientConfigName.equals(that.clientConfigName));
    }

    // 慎用 使用前需要保证clientConfigName非null
    public String getSignature(){
        if (this.getClientConfigName() == null) {
            return this.getConfigName();
        }
        return this.getClientConfigName() + "-" + this.getConfigName();
    }
}