package com.xylink.configserver.data.model;

import lombok.Data;

@Data
public class NemoDefaultBrandConfig extends BaseConfig{

    public static final String COMMON_CONFIG_KEY = "common";
    public static final String DEFAULT_BRAND_MODEL_KEY = "default";

    private String configBrand;
    protected int configType;
    protected int baseConfigType;
    protected String brandModel = DEFAULT_BRAND_MODEL_KEY;

    public NemoDefaultBrandConfig() {
    }

    public NemoDefaultBrandConfig(String configName, String configValue, String clientConfigName, String configBrand, int configType, int baseConfigType, String brandModel) {
        this.configValue = configValue;
        this.configName = configName;
        this.clientConfigName = clientConfigName;
        this.configBrand = configBrand;
        this.configType = configType;
        this.baseConfigType = baseConfigType;
        this.brandModel = brandModel;
    }

    public NemoDefaultBrandConfig(String configName,String clientConfigName,String configBrand, String brandModel, int configType) {
        this.configName = configName;
        this.clientConfigName = clientConfigName;
        this.configBrand = configBrand;
        this.configType = configType;
        this.brandModel = brandModel;
    }
}
