package com.xylink.configserver.data.model.deviceseries;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-18 22:43
 */
@Data
public class DeviceSubtypeDto implements Serializable {
    private int type;
    private String typeName;
    @NotNull(message = "Required int parameter 'subtype' is not present")
    private Integer subtype;
    private String displayName;
    @NotNull(message = "Required String parameter 'categoryDisplayName' is not present")
    private String categoryDisplayName;
    /**
     * 设备ID 在终端获取配置才会有值 终端类型先关的改值为null
     */
    private Long deviceId;
    List<DeviceSeriesSubtypeConfigsDto> configs;
}
