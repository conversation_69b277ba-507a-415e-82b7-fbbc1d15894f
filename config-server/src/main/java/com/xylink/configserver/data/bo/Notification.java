//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON>rn<PERSON>lower decompiler)
//

package com.xylink.configserver.data.bo;

import java.io.Serializable;

public class Notification<T> implements Serializable {
    private static final long serialVersionUID = 1L;
    private int subType;
    private T content;

    public Notification(int subType, T content) {
        this.subType = subType;
        this.content = content;
    }

    public int getSubType() {
        return this.subType;
    }

    public void setSubType(int subType) {
        this.subType = subType;
    }

    public T getContent() {
        return this.content;
    }

    public void setContent(T content) {
        this.content = content;
    }

    public String toString() {
        return "Notification:[subType: " + this.subType + ", content: " + this.content + "]";
    }

    public static enum NotificationType {
        CALL(1),
        UPLOAD_LOG(2),
        REBOOT(4),
        RECOVERY(8),
        PUSHMESSAGE(10),
        NETTEST(12),
        CHEC<PERSON><PERSON>EATURE(14),
        PROMOTIONACTIVITY(15),
        NEMO_CONFIG_CHANGE(16),
        UPLOAD_FULL_LOG(18),
        OPERATION_ACTIVITY(19),
        UPGRADE_NEMO(20),
        INVITE_CALL(22),
        NEMO_COMMAND(24),
        MEETING_REMINER(26),
        START_RECORD(28),
        STOP_RECORD(30),
        START_CONTENT(32),
        STOP_CONTENT(34),
        SUB_TITLE(35),
        CANCEL_HANGUP(36),
        LEAVE_ENTERPRISE(37),
        USER_MSG_NOTIFICATION(38),
        HANGUP(39),
        STATUS_WX_DEVIDE(40),
        STATUS_AUTH_DEVIDE(41),
        CAMERA_CHANGE(42),
        SERVER_ABILITY_CHANGED(50);

        private final int type;

        private NotificationType(int type) {
            this.type = type;
        }

        public int getType() {
            return this.type;
        }
    }
}
