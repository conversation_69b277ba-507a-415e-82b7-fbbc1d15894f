package com.xylink.configserver.data.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * ClassName:BaseCloudControllerEntity
 * Package:com.xylink.configserver.data.entity
 * Description:
 *
 * <AUTHOR>
 * @Date 2025/5/8-16:26
 * @Version: v1.0
 */
@Data
@TableName("base_cloud_controller_config")
public class BaseCloudControllerConfigEntity {

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    private String configName;

    private String configValue;

    private String clientConfigName;

    private Integer configType;

    private String enterpriseId= "default_enterprise";

    private String customizedKey ;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;



}
