package com.xylink.configserver.data.model;

import lombok.Data;

@Data
public class DeviceConfig extends BaseConfig {

    private long nemoId;
    private long configExpireTime = Long.MAX_VALUE;

    public DeviceConfig(long id,long deviceId, String configName, String configValue, String clientConfigName,long configExpireTime) {
        this.configName = configName;
        this.configValue = configValue;
        this.clientConfigName = clientConfigName;
        this.nemoId = deviceId;
        this.configExpireTime = configExpireTime;
        this.id = id;

    }

    public DeviceConfig(long deviceId, String configName, String configValue, String clientConfigName) {
        this.configName = configName;
        this.configValue = configValue;
        this.clientConfigName = clientConfigName;
        this.nemoId = deviceId;
    }

    public DeviceConfig(long nemoId, String configName, String clientConfigName) {
        this.nemoId = nemoId;
        this.configName = configName;
        this.clientConfigName = clientConfigName;
    }

    public DeviceConfig() {}
}
