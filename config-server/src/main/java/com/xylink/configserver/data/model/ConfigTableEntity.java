package com.xylink.configserver.data.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConfigTableEntity implements Serializable {

    private String configName;

    private String configValue;
    /*
    *
    *生效表名
    * */
    private String effectiveTable;


    private Map<String,String> validConfiguration;

}