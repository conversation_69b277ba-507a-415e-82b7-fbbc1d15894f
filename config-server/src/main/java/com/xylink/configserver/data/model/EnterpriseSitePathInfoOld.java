package com.xylink.configserver.data.model;

import lombok.Data;

@Data
public class EnterpriseSitePathInfoOld {

    private String id;
    private String enterpriseOfSitecode;
    private String locationOfSitecode;
    private String netToolServer;
    private String displayName;
    private String enterpriseId;
    private String detail;

    public EnterpriseSitePathInfoOld() {
    }

    public EnterpriseSitePathInfoOld(String id, String localPart, String enterprisePart, String netToolServer, String name, String enterpriseId, String detail) {
        this.id = id;
        this.locationOfSitecode = localPart;
        this.enterpriseOfSitecode = enterprisePart;
        this.netToolServer = netToolServer;
        this.displayName = name;
        this.enterpriseId = enterpriseId;
        this.detail = detail;
    }
}
