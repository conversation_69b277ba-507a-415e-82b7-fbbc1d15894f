package com.xylink.configserver.data.model;

import lombok.Data;

@Data
public class UserConfig extends BaseConfig {

    private long userProfileId;
    private long configExpireTime;
    private int deviceType;

    public UserConfig() {

    }

    public UserConfig(String configName, String configValue, String clientConfigName, int configType, long userProfileId) {
        this.configValue = configValue;
        this.configName = configName;
        this.clientConfigName = clientConfigName;
        this.deviceType = configType;
        this.configExpireTime = Long.MAX_VALUE;
        this.userProfileId = userProfileId;
    }
}
