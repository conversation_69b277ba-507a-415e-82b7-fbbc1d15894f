package com.xylink.configserver.data.model;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Objects;

@Data
@AllArgsConstructor
public class EventsStrContent {

    private int count;
    private List<Event> events;

    @Data
    public static class Event {
        public String type;
        public String content;
        public boolean persistent = true;
        private long timestamp;
        private long expireTime = -1L;
        private String dedupKey;

        public static Event of(com.ainemo.protocol.event.Event event) {
            Objects.requireNonNull(event, "event must not be null");
            com.xylink.configserver.enums.Event.Type eventType = com.xylink.configserver.enums.Event.Type.fromValue(event.getType());
            Objects.requireNonNull(eventType, "eventType must not be null");
            String type = eventType.name();
            Event newEvent = new Event();
            newEvent.setType(type);
            newEvent.setContent(event.getContent());
            newEvent.setPersistent(event.persistent);
            newEvent.setDedupKey(event.getDedupKey());
            newEvent.setExpireTime(event.getExpireTime());
            newEvent.setTimestamp(event.getTimestamp());
            return newEvent;
        }
    }


}