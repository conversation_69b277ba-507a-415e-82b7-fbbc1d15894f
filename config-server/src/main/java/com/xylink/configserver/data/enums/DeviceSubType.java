package com.xylink.configserver.data.enums;

public enum DeviceSubType {
    UNKNOWN(-1),
    NEMO(20),
    NE60(21),
    <PERSON><PERSON>(22),
    <PERSON>50(25),
    <PERSON><PERSON><PERSON>(26),
    AE2060(23),
    <PERSON><PERSON>(70),
    <PERSON><PERSON>(71),
    SOFT_ME90(72),
    <PERSON><PERSON><PERSON><PERSON>(80),
    <PERSON><PERSON><PERSON>(81),
    <PERSON><PERSON><PERSON>(82),
    <PERSON><PERSON><PERSON>(83),
    <PERSON><PERSON><PERSON><PERSON>(84),
    NP40(85),
    ME72(73),
    NP4000(89),
    NP5000(8901),
    NP5000_SLAVE(8902);

    private final int value;

    public static DeviceSubType valueOf(int value) {
        DeviceSubType[] var1 = values();
        for (DeviceSubType type : var1) {
            if (type.value == value) {
                return type;
            }
        }

        return null;
    }

    DeviceSubType(int value) {
        this.value = value;
    }

    public int getValue() {
        return this.value;
    }
}