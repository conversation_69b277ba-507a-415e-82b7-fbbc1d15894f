package com.xylink.configserver.data.model.deviceseries;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-18 14:22
 */
@Data
public class DeviceSubtypeDetailUpdateDto implements Serializable {
    @Valid
    private DeviceSubtypeUpdateDto deviceSubtype;
    private DeviceSeriesDto deviceSeries;

    @Data
    public static class DeviceSubtypeUpdateDto {
        @NotNull(message = "Required int parameter 'subtype' is not present")
        private Integer subtype;
        @NotNull(message = "Required String parameter 'categoryDisplayName' is not present")
        private String categoryDisplayName;
        List<SelectedConfigs> configs;

        @Data
        public static class SelectedConfigs {
            private Long configId;
            private String platform;
        }
    }
}
