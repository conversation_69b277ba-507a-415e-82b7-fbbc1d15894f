package com.xylink.configserver.data.model.deviceseries;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xylink.configserver.data.model.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021-01-12 15:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("libra_device_subtype_series_config_dictionary_data")
public class DeviceSubtypeSeriesConfigDictionaryDataEntity extends BaseEntity {
    /**
     * 配置项ID
     */
    private Long configId;
    /**
     * 配置项大类
     */
    private String clientConfigName;
    /**
     * 配置项key
     */
    private String configName;
    /**
     * 配置项展示名称
     */
    private String configShowName;
    /**
     * 配置项描述
     */
    private String configComment;
    /**
     * 配置项取值字典key
     */
    private String configDictType;
    /**
     * 是否特殊配置项，覆盖某个配置项配置 默认值为0 如果需要覆盖的配置填系列中对应的值（该配置项尽量少用，保证各终端的配置项统一）
     */
    private Integer special;
    /**
     * 配置项展示类型 用于UI显示 {@link com.xylink.configserver.data.model.deviceseries.ConfigValuesTypeEnum}
     */
    private String configValuesType;
    /**
     * 状态字段  '状态（0正常 1停用）'
     */
    private Integer status;

}
