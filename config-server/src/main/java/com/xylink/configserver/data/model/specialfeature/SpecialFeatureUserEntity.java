package com.xylink.configserver.data.model.specialfeature;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/8/28 10:54
 */
@TableName("libra_special_feature_user")
@Data
public class SpecialFeatureUserEntity {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;

    private String featureId;

    private Long userProfileId;

    private Integer dataType;

    private String deptName;

    private Date updateTime;

}
