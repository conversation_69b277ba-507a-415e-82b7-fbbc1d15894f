package com.xylink.configserver.data.model;

import lombok.Data;

import java.io.Serializable;

/**
 * libra_device_subtype_model
 * <AUTHOR>
 */
@Data
public class LibraDeviceSubtypeModel implements Serializable {
    private String id;

    /**
     * 子类型编码
     */
    private Short subType;

    /**
     * 大类型编码
     */
    private Short type;

    /**
     * 子类型
     */
    private String category;

    /**
     * 展示类型
     */
    private String categoryDisplay;

    /**
     * 大类型
     */
    private String typeCategory;

    /**
     * 是否计方数
     */
    private Boolean isChargeSession;

    /**
     * 是否交端口使用费
     */
    private Boolean isChargePort;

    /**
     * 是否赠送方数
     */
    private Boolean isPresentSession;

    /**
     * 是否可以加入企业
     */
    private Boolean isJoinEnterprise;

    /**
     * 是否支持内外网探测
     */
    private Boolean isNetworkTopology;

    /**
     * 是否支持修改管理员
     */
    private Boolean isModifyManager;

    /**
     * 延保卡号类型
     */
    private String cartVipType;

    /**
     * 端口使用费卡号类型
     */
    private String cartSessionType;

    /**
     * 是否支持调节音量
     */
    private Boolean isAdjusteVolume;

    /**
     * 是否支持多画面
     */
    private Boolean isMultiImage;

    /**
     * 多画面能力
     */
    private String multiImage;

    /**
     * 设备号前缀
     */
    private String deviceNumerPrefix;

    /**
     * 是否可联接
     */
    private Boolean isAssociate;

    /**
     * sn前缀
     */
    private String snPrefix;

    /**
     * 描述
     */
    private String description;

    /**
     * 是否是触屏版
     */
    private Boolean isTouchScreen;

    /**
     * 是否是三方硬件
     */
    private Boolean isThirdDevice;

    /**
     * 是否具有巡检能力
     */
    private Boolean inspect;

    /**
     * 是否支持多画面轮询
     */
    private Boolean isMultiImagePoll;

    /**
     * 赠送端口时长，单位：月
     */
    private Integer chargePortPresentMonths;

    /**
     * 赠送延保时长，单位：月
     */
    private Integer cartVipPresentMonths;

    /**
     * 是否记录终端软件版本信息
     */
    private Boolean isRecordSoftVersion;

    /**
     * 端口计费分辨率
     */
    private String chargeResolution;

    /**
     * 是否为一体机,0-不是，1-是，2-待定
     */
    private Integer isIntegratedStyle;

    /**
     * 是否支持4k画面
     */
    private Boolean canK4;

    /**
     * hot_standby
     * 双机热备
     */
    private Integer  hotStandby;

    private static final long serialVersionUID = 1L;
}
