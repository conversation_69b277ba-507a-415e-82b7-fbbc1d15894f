package com.xylink.configserver.data.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class EnterpriseNemoProfileUpdateReqV1 implements Serializable {

    private static final long serialVersionUID = -7646915539662484155L;
    private String profileId;
    private String enterpriseId;
    private EnterpriseNemoProfile profile;
    private Map<String, String> configs;
    private List<EnterpriseNemoFeature> features;
}
