package com.xylink.configserver.data.exception;

import com.xylink.configserver.data.enums.ResultCodeEnum;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.data.model.RestMessage;

public class RestException extends RuntimeException {
    private static final long serialVersionUID = -614481944473788628L;
    private final int httpCode;
    private final RestMessage msg;

    public RestException(HttpStatus httpStatus, int errorCode, String userMessage, String developerMessage, String moreInfo) {
        this.httpCode = httpStatus.code();
        msg = new RestMessage();
        msg.setErrorCode(errorCode);
        msg.setUserMessage(userMessage);
        msg.setDeveloperMessage(developerMessage);
        msg.setMoreInfo(moreInfo);
    }

    public RestException(HttpStatus httpStatus, int errorCode, String userMessage, String developerMessage) {
        this.httpCode = httpStatus.code();
        msg = new RestMessage();
        msg.setErrorCode(errorCode);
        msg.setUserMessage(userMessage);
        msg.setDeveloperMessage(developerMessage);
        msg.setMoreInfo("http://www.ainemo.com/errors/" + errorCode);
    }

    public RestException(HttpStatus httpStatus, int errorCode, String userMessage) {
        this.httpCode = httpStatus.code();
        msg = new RestMessage();
        msg.setErrorCode(errorCode);
        msg.setUserMessage(userMessage);
        msg.setDeveloperMessage("");
        msg.setMoreInfo("http://www.ainemo.com/errors/" + errorCode);
    }

    public RestException(HttpStatus httpStatus, String userMessage) {
        this.httpCode = httpStatus.code();
        msg = new RestMessage();
        msg.setErrorCode(0);
        msg.setUserMessage(userMessage);
        msg.setDeveloperMessage("");
        msg.setMoreInfo("http://www.ainemo.com/errors/" + 0);
    }

    public int getHttpCode() {
        return httpCode;
    }

    public RestMessage getMsg() {
        return msg;
    }

    @Override
    public String getMessage() {
        return "httpCode:" + httpCode + "; Message: " + msg;
    }

    public static RestException newInstanceOfHttpStatus400(ResultCodeEnum resultCodeEnum, String message) {
        return new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, resultCodeEnum.getCode(), message);
    }
}
