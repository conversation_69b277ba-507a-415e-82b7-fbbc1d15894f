package com.xylink.configserver.data.model;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

@TableName("libra_user_device")
public class UserDeviceEntity {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     *
     */
    private Short deviceType;

    /**
     *
     */
    private Long userProfileId;

    /**
     *
     */
    private String deviceDisplayName;

    /**
     *
     */
    private String deviceSn;

    /**
     *
     */
    private String deviceSk;

    /**
     *
     */
    private Long deviceExpireTime;

    /**
     *
     */
    private Integer inUse;

    /**
     *
     */
    private Long devicePresense;

    /**
     *
     */
    private String fingerprint;

    /**
     *
     */
    private Long bindTimestamp;

    /**
     *
     */
    private String avatar;

    /**
     *
     */
    private String hardwareSnUnique;

    /**
     *
     */
    private String deviceCategory;

    /**
     *
     */
    private Short subType;

    /**
     * 企业id
     */
    private String enterpriseId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     *
     */
    private String gatewayDeviceId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Short getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(Short deviceType) {
        this.deviceType = deviceType;
    }

    public Long getUserProfileId() {
        return userProfileId;
    }

    public void setUserProfileId(Long userProfileId) {
        this.userProfileId = userProfileId;
    }

    public String getDeviceDisplayName() {
        return deviceDisplayName;
    }

    public void setDeviceDisplayName(String deviceDisplayName) {
        this.deviceDisplayName = deviceDisplayName;
    }

    public String getDeviceSn() {
        return deviceSn;
    }

    public void setDeviceSn(String deviceSn) {
        this.deviceSn = deviceSn;
    }

    public String getDeviceSk() {
        return deviceSk;
    }

    public void setDeviceSk(String deviceSk) {
        this.deviceSk = deviceSk;
    }

    public Long getDeviceExpireTime() {
        return deviceExpireTime;
    }

    public void setDeviceExpireTime(Long deviceExpireTime) {
        this.deviceExpireTime = deviceExpireTime;
    }

    public Integer getInUse() {
        return inUse;
    }

    public void setInUse(Integer inUse) {
        this.inUse = inUse;
    }

    public Long getDevicePresense() {
        return devicePresense;
    }

    public void setDevicePresense(Long devicePresense) {
        this.devicePresense = devicePresense;
    }

    public String getFingerprint() {
        return fingerprint;
    }

    public void setFingerprint(String fingerprint) {
        this.fingerprint = fingerprint;
    }

    public Long getBindTimestamp() {
        return bindTimestamp;
    }

    public void setBindTimestamp(Long bindTimestamp) {
        this.bindTimestamp = bindTimestamp;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getHardwareSnUnique() {
        return hardwareSnUnique;
    }

    public void setHardwareSnUnique(String hardwareSnUnique) {
        this.hardwareSnUnique = hardwareSnUnique;
    }

    public String getDeviceCategory() {
        return deviceCategory;
    }

    public void setDeviceCategory(String deviceCategory) {
        this.deviceCategory = deviceCategory;
    }

    public Short getSubType() {
        return subType;
    }

    public void setSubType(Short subType) {
        this.subType = subType;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getGatewayDeviceId() {
        return gatewayDeviceId;
    }

    public void setGatewayDeviceId(String gatewayDeviceId) {
        this.gatewayDeviceId = gatewayDeviceId;
    }
}