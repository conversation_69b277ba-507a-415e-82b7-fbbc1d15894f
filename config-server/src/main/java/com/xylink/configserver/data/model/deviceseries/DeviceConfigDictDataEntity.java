package com.xylink.configserver.data.model.deviceseries;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xylink.configserver.data.model.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021-01-27 11:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("libra_config_dict_data")
public class DeviceConfigDictDataEntity extends BaseEntity {
    /**
     * 编码
     */
    private String dictCode;
    /**
     * 值
     */
    private String dictValue;
    /**
     * 编码类型
     */
    private String dictType;
    /**
     * 排序
     */
    private Integer dictSort;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
}
