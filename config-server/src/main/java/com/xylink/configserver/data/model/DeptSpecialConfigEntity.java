package com.xylink.configserver.data.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("libra_special_feature_department")
public class DeptSpecialConfigEntity {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long deptId;

    private String featureId;

    // additional properties in order to join table select
    // related to table named libra_special_feature_config
    private String clientConfigName;

    private String configName;

    private String configValue;

}
