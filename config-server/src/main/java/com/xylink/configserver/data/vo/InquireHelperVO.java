package com.xylink.configserver.data.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/*
* 该类为ConfigInquireHelperController 助手辅助类
* 协助查询配置生效维度
* */
@Data
public class InquireHelperVO implements Serializable {

    private Long userDeviceId;

    private String enterpriseId;

    private List<String> configs;

    private String version;

    private Integer type;

    private Integer subtype;

    private String clientConfigName;

    private String userProfileId;

    private String deviceSn;

    private String platform;

    private String av;

    private Integer queryType = 0;

}