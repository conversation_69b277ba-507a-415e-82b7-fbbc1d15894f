package com.xylink.configserver.data.model;

import lombok.Data;

@Data
public class DeviceSubtypeModelV2 {

    private int subType;
    private int type;
    private String category;
    private String categoryDisplay;
    private String typeCategory;
    private boolean isChargeSession;
    private boolean isChargePort;
    private boolean isPresentSession;
    private boolean isJoinEnterprise;
    private boolean isNetworkTopology;
    private boolean isModifyManager;
    private String cartVipType;
    private String cartSessionType;
    private boolean isAdjustVolume;
    private boolean isCanMultiImage;
    private boolean isCanMultiImagePoll;
    private String multiImage;
    private String deviceNumberPrefix;
    private boolean isAssociate;
    private String snPrefix;
    private boolean isTouchScreen;
    private boolean inspect;
    private boolean isThirdDevice;
    private int chargePortPresentMonths;
    private int cartVipPresentMonths;
    private boolean isRecordSoftVersion;
}
