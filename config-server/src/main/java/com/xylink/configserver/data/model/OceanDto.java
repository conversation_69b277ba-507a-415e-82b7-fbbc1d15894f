package com.xylink.configserver.data.model;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
public class OceanDto<T extends OceanData> implements Serializable {
    private String collection;
    private long timestamp;
    private List<T> content;

    public OceanDto(String collection, List<T> content) {
        this.collection = collection;
        this.content = content;
        this.timestamp = System.currentTimeMillis();
    }

    public OceanDto(String collection, T content) {
        this.collection = collection;
        this.timestamp = System.currentTimeMillis();
        addContent(content);
    }

    public void addContent(T content) {
        if (Objects.isNull(this.content)) {
            this.content = new ArrayList<>();
        }
        this.content.add(content);
    }

}
