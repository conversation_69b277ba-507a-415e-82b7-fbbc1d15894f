package com.xylink.configserver.data.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *ClassName:BasicCertInfo
 *Package:com.xylink.configserver.data.entity
 *Description:
 *<AUTHOR>
 *@Date 2024/12/2-11:36
 *@Version: v1.0
 *
 */
/**
 * 证书资源信息
 */
@Data
@TableName(value = "basic_cert_info")
public class BasicCertInfo implements Serializable {
    /**
     * 表主键id
     */
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    /**
     * 证书名称
     */
    @TableField(value = "cert_name")
    private String certName;

    /**
     * 签名作者
     */
    @TableField(value = "sign_author")
    private String signAuthor;

    /**
     * 证书sn
     */
    @TableField(value = "cert_sn")
    private String certSn;

    /**
     * 证书起始时间
     */
    @TableField(value = "start_time")
    private String startTime;

    /**
     * 证书过期时间
     */
    @TableField(value = "end_time")
    private String endTime;

    /**
     * 证书是否可用
     */
    @TableField(value = "cert_state")
    private Integer certState;

    /**
     * 证书内容
     */
    @TableField(value = "cert_Content")
    private String certContent;

    /**
     * 证书签名值
     */
    @TableField(value = "cert_sign")
    private String certSign;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill  = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}