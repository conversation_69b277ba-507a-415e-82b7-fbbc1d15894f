package com.xylink.configserver.data.model;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class DeviceSoftVersion extends Model<DeviceSoftVersion> {

    private Long id;
    private String deviceSn;
    private String currentSoftVersion;
    private String firstSoftVersion;
    private String modiftTime;

    public boolean isVersionGreatThan(String version) {
        boolean result = false;
        if (StringUtils.isNotBlank(version)
                && version.indexOf(".") >= 0) {
            String[] version_num = version.split("\\.");
            String[] cur_num = currentSoftVersion.split("\\.");
            int loop_count = version_num.length;
            if (cur_num.length > version_num.length) {
                loop_count = cur_num.length;
            }

            for (int i = 0; i < loop_count; i++) {
                if (Integer.valueOf(cur_num[i]) > Integer.valueOf(version_num[i]) ) {
                    result = true;
                    break;
                }
            }
        }
        return result;
    }
}
