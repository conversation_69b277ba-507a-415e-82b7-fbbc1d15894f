package com.xylink.configserver.data.model.deviceseries;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xylink.configserver.data.model.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021-01-27 11:21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("libra_config_dict_type")
public class DeviceConfigDictTypeEntity extends BaseEntity {
    /**
     * 配置项名称
     */
    private String dictName;
    /**
     * 配置项类型
     */
    private String dictType;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
}
