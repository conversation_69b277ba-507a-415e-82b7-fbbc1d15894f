package com.xylink.configserver.data.model.deviceseries;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xylink.configserver.data.model.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @since 2021-01-12 14:35
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("libra_device_subtype_series_config")
public class DeviceSubtypeSeriesConfigEntity extends BaseEntity {
    /**
     * 终端类型
     */
    @TableField("sub_type")
    private Integer subtype;
    /**
     * 终端系列ID
     */
    private Long seriesId;
    /**
     * 选择的配置项ID
     */
    private Long configId;
    /**
     * 选择配置项生效平台 {@link com.xylink.configserver.data.model.deviceseries.PlatformEnum}
     */
    private String platform;
    /**
     * 类型 {@link com.xylink.configserver.data.model.deviceseries.DeviceGroupEnum}
     */
    private String type;
}
