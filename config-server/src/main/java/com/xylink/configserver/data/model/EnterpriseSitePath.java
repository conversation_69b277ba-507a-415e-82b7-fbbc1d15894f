package com.xylink.configserver.data.model;

import lombok.Data;

@Data
public class EnterpriseSitePath {

    private String id;
    private String locationOfSitecode;
    private String enterpriseOfSitecode;
    private String netToolServer;
    private String disalayName;
    private String enterpriseId;
    private String detail;
    private long timeEnable;
    private long timeDisable;
    private Boolean enabled;
    private String sublocationOfSitecode;
    private String provoderOfSitecode;
    private int networkType;


    public EnterpriseSitePath() {
    }

    public EnterpriseSitePath(String locationOfSitecode, String enterpriseOfSitecode, String netToolServer, String disalayName, String enterpriseId, String detail, long timeEnable, long timeDisable, boolean enabled) {
        this.locationOfSitecode = locationOfSitecode;
        this.enterpriseOfSitecode = enterpriseOfSitecode;
        this.netToolServer = netToolServer;
        this.disalayName = disalayName;
        this.enterpriseId = enterpriseId;
        this.detail = detail;
        this.timeEnable = timeEnable;
        this.timeDisable = timeDisable;
        this.enabled = enabled;
    }

    public EnterpriseSitePath(String locationOfSitecode, String enterpriseOfSitecode, String netToolServer, String disalayName, String enterpriseId, String detail, long timeEnable, long timeDisable, boolean enabled, String sublocationOfSitecode, String provoderOfSitecode, int networkType) {
        this.locationOfSitecode = locationOfSitecode;
        this.enterpriseOfSitecode = enterpriseOfSitecode;
        this.netToolServer = netToolServer;
        this.disalayName = disalayName;
        this.enterpriseId = enterpriseId;
        this.detail = detail;
        this.timeEnable = timeEnable;
        this.timeDisable = timeDisable;
        this.enabled = enabled;
        this.sublocationOfSitecode = sublocationOfSitecode;
        this.provoderOfSitecode = provoderOfSitecode;
        this.networkType = networkType;
    }
}
