package com.xylink.configserver.data.model;

import lombok.Data;

@Data
public class NemoFeature {

    private String id;
    private String nemoSN;
    private String featureId;
    private int featureStatus;
    private String targetNumber;
    private long startTime;
    private long expireTime;
    private boolean trial;
    private boolean statementAgreed;
    private Boolean hasMainShortCut;
    private String displayName;

    public NemoFeature() {

    }

    public NemoFeature(String nemoSN, String featureId) {
        this.nemoSN = nemoSN;
        this.featureId = featureId;
    }
}
