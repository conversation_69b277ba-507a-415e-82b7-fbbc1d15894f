package com.xylink.configserver.data.model;

import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Set;

public class FeatureRequirement {
    private String featureName;
    private String softVersion;
    private String hardVersion;
    private String model;
    private String OS;
    private boolean reboot;
    private String snList;
    private String type;
    private String value;
    private Set<String> allowedSNs;

    public String getSoftVersion() {
        return softVersion;
    }
    public void setSoftVersion(String softVersion) {
        this.softVersion = softVersion;
    }
    public String getHardVersion() {
        return hardVersion;
    }
    public void setHardVersion(String hardVersion) {
        this.hardVersion = hardVersion;
    }
    public String getModel() {
        return model;
    }
    public void setModel(String model) {
        this.model = model;
    }
    public String getOS() {
        return OS;
    }
    public void setOS(String oS) {
        OS = oS;
    }
    public boolean isReboot() {
        return reboot;
    }
    public void setReboot(boolean reboot) {
        this.reboot = reboot;
    }
    public String getFeatureName() {
        return featureName;
    }
    public void setFeatureName(String featureName) {
        this.featureName = featureName;
    }
    public String getSnList() {
        return snList;
    }
    public void setSnList(String snList) {
        this.snList = snList;
        if(!StringUtils.isEmpty(snList)) {
            allowedSNs = new HashSet<String> ();
            String[] sns = snList.split(",");
            for(String sn : sns) {
                allowedSNs.add(sn.trim().toUpperCase());
            }
        }
    }
    public Set<String> getAllowedSNs() {
        return allowedSNs;
    }
    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }
    public String getValue() {
        return value;
    }
    public void setValue(String value) {
        this.value = value;
    }

}
