package com.xylink.configserver.data.model.deviceseries;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-18 14:11
 */
@Data
public class DeviceSeriesDto implements Serializable {

    private Long id;
    @NotNull(message = "Required String parameter 'seriesName' is not present")
    private String seriesName;
    private int special;
    List<DeviceSeriesSubtypeConfigsDto> configs;
    private List<Integer> subtypes;
}
