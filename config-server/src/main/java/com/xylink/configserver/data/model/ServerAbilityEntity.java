package com.xylink.configserver.data.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: bin
 * @Date: 2021/11/18 17:23
 * @Version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("libra_server_ability")
public class ServerAbilityEntity implements Serializable {

    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField("value_offset")
    private Integer offset;

    private String abilityName;

    private Integer state;

    private Long decimalValue;

    private Integer category;

    private String abilityDescribe;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}