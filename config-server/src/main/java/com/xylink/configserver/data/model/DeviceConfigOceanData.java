package com.xylink.configserver.data.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DeviceConfigOceanData extends OceanData implements Serializable {

    /**
     * Device ID
     * */
    private String id;
    /**
     *
     * */
    private List<RestNemoConfig>configs;

    public DeviceConfigOceanData() {
    }

    public DeviceConfigOceanData(String id, List<RestNemoConfig> configs) {
        this.id = id;
        this.configs = configs;
    }
}
