package com.xylink.configserver.data.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * libra_config_type_value_limit
 * <AUTHOR>
 * @since 2021/1/9 11:03 上午
 */
@Data
@TableName("libra_config_type_value_limit")
@NoArgsConstructor
@AllArgsConstructor
public class ConfigTypeValueLimitModel implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 大类配置
     */
    private String clientConfigName;

    /**
     * 详细配置
     */
    private String configName;

    /**
     * 配置值类型
     */
    private String valueType;

    /**
     * 配置值范围
     */
    private String valueScope;

    /**
     * 配置项UI界面开关类型
     * 输入框（默认）INPUT_BOX    文本框
     * 单选        RADIO        [1,2,3]
     * 多选        CHECK_BOX    [1,2,3]
     * 开启/关闭    OPEN_CLOSE   [true,false]
     * 隐藏/显示    HIDE_SHOW    [true,false]
     */
    @TableField("config_ui_type")
    private String configUIType;

    /**
     * 配置含义
     */
    private String configComment;

    private static final long serialVersionUID = 1L;

}