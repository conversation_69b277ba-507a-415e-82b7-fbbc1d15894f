package com.xylink.configserver.data.model;

public enum HttpStatus {
    SUCCESS_OK(200),
    SUCCESS_CREATED(201),
    CLIENT_ERROR_BAD_REQUEST(400),
    CLIENT_ERROR_UNAUTHORIZED(401),
    CLIENT_ERROR_PAYMENT_REQUIRED(402),
    CLIENT_ERROR_FORBIDDEN(403),
    CLIENT_ERROR_NOT_FOUND(404),
    CLIENT_ERROR_CONFLICT(409),
    SERVER_ERROR_INTERNAL(500),
    SERVER_ERROR_NOT_IMPLEMENTED(501);

    private int code;

    HttpStatus(int _code) {
        this.code = _code;
    }

    public int code() {
        return this.code;
    }
}
