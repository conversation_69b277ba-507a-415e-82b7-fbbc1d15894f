package com.xylink.configserver.data.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@TableName("libra_user_device")
public class UserDevice extends Model<UserDevice> {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("device_type")
    private int type;

    @TableField("user_profile_id")
    private Long userProfileID;

    @TableField("device_display_name")
    private String displayName;

    @TableField("device_sn")
    private String deviceSN;

    @TableField("device_sk")
    private String securityKey;

    @TableField("device_expire_time")
    private long expirationTime;

    private Integer inUse;

    @TableField("device_presense")
    private int presence;

    private String fingerprint;

    private long bindTimestamp;

    private String avatar;

    private String hardwareSnUnique;

    @TableField("device_category")
    private String category;

    private int subType;

    private String enterpriseId;

    private Date updateTime;

    private String gatewayDeviceId;

    @TableField(exist = false)
    private String nemoNumber;

    @TableField(exist = false)
    private String deviceModel;

    @TableField(exist = false)
    private Map<String, String> config;

    @TableField(exist = false)
    private String adminDisplayName;

    @TableField(exist = false)
    private long firstBindTime;

    @TableField(exist = false)
    private boolean autoBound;

    @TableField(exist = false)
    private String departmentId;

    @TableField(exist = false)
    private boolean isOfficeDevice;

    @TableField(exist = false)
    private String deptCode;

    @TableField(exist = false)
    private String version;

    public UserDevice(long deviceId, String deviceSN, int deviceType, int deviceSubType, long userProfileId){
        this.id = deviceId;
        this.deviceSN = deviceSN;
        this.type = deviceType;
        this.subType = deviceSubType;
        this.userProfileID = userProfileId;
    }


}
