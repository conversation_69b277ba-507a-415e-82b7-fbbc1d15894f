package com.xylink.configserver.data.enums;

public interface Event {
    public static enum Type {
        FRIEND_REQ(1),
        FRIEND_REQ_FINISHED(5),
        MEMBER_REQ_FINISHED(7),
        FRIEND_NEW(2),
        FRIEND_REMOVED(3),
        FRIEND_UPDATED(4),
        DEVICE_ADDED(11),
        DEVICE_REMOVED(12),
        DEVICE_PRESENCE(21),
        NEMO_CHANGED(13),
        NEMO_AVATAR_CHANGED(14),
        DEVICE_CONFIG_CHANGED(15),
        IDLE_SCREEN_CHANGED(16),
        ADDED_TO_ENTERPRISE(17),
        DEVICE_UIDISPLAY_CHANGED(18),
        DEVICE_NETWORK_SWITCH(31),
        VOD_NEW(41),
        FAVORITE_VOD_CHANGED(50),
        METADATA_READY(51),
        MP4_FILE_READY(52),
        VOD_METADATA_REMOVED(53),
        ADD_FAVORITE_VOD(54),
        REMOVE_FAVORITE_VOD(55),
        ADD_MEETINGROOM_VOD(56),
        REMOVE_MEETINGROOM_VOD(57),
        CLEAR_MEETINGROOM_VOD(58),
        NEMO_CIRCLE_REFRESH(59),
        NEMO_CIRCLE_CHANGE(60),
        YOU_ENTER_A_CIRCLE(61),
        YOU_EXIT_A_CIRCLE(62),
        YOUR_NEMO_ENTER_A_CIRCLE(63),
        YOUR_NEMO_EXIT_A_CIRCLE(64),
        NEMO_CONFIG_CHANGE(65),
        USER_CONFIG_CHANGE(66),
        NEMO_REQ(67),
        CHANGE_NEMO_CONFIG(68),
        NEMO_SERVICE_CHANGE(69),
        SCREEN_SAVER_CHANGED(601),
        ADD_ITEM_TO_ALBUM(70),
        UPDATE_ITEM_IN_ALBUM(71),
        DELETE_ITEM_IN_ALBUM(72),
        AUTHORITY_RULES_CHANGE(73),
        NEMO_REQ_FINISHED(74),
        ADD_RECORD_TO_ALBUM(75),
        DELETE_RECORD_FROM_ALBUM(76),
        PSTN_TIME_CHANGE(80),
        NEMO_NETTOOL_ADVICE(81),
        BILL_DEVICE_ACCESS_CHANGE(82),
        NOTIFY_FEATURE(100),
        RETRIEVE_NOTIFY_FEATURE(101),
        ADD_CLOUD_CONFERENCE(110),
        UPDATE_CLOUD_CONFERENCE(111),
        UPDATE_OBSERVER_PERMISSION(112),
        ENTERPRISE_CONTACT_CHANGED(113),
        ENTERPRISE_CONTACT_RESET(114),
        ENTERPRISE_CONTACT_REQ(115),
        NOTIFY_MESSAGE(120),
        RETRIEVE_NOTIFIED_MESSAGE(121),
        SCHEDULED_MEETING_CREATED(131),
        SCHEDULED_MEETING_UPDATED(132),
        SCHEDULED_MEETING_DELETED(133),
        SCHEDULED_MEETING_REMINDER(134),
        SCHEDULED_EVENT_CREATED(135),
        SCHEDULED_EVENT_UPDATED(136),
        SCHEDULED_EVENT_DELETED(137),
        SCHEDULED_EVENT_REMINDER(138),
        VALIDATE_ENVNT_MAIL_SUCCESS(140),
        CHANGE_PASSWORD_EXPIRE_TIME(150),
        SOFT_ENDPOINT_TIME(160),
        VOTE_SIGNATURE_START(170),
        VOTE_SIGNATURE_STOP(171),
        VOTE_SIGNATURE_SACN_USER(172),
        VOTE_ANSWER_PUBLISH(173),
        INSPECTION_START(180),
        INSPECTION_STOP(181),
        USER_INFO_UPDATE(190),
        SERVER_ABILITY_CHANGED(200);

        private int value;

        private Type(int value) {
            this.value = value;
        }

        public int getValue() {
            return this.value;
        }

        public static Event.Type fromValue(int value) {
            Event.Type[] var1 = values();
            int var2 = var1.length;

            for (int var3 = 0; var3 < var2; ++var3) {
                Event.Type t = var1[var3];
                if (t.getValue() == value) {
                    return t;
                }
            }

            return null;
        }
    }
}