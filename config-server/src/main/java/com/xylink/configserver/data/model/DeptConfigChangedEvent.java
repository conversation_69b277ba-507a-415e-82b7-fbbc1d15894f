package com.xylink.configserver.data.model;

import org.springframework.context.ApplicationEvent;

import java.time.Clock;


public class DeptConfigChangedEvent extends ApplicationEvent {

    private String enterpriseId;
    private String deptId;


    public DeptConfigChangedEvent(Object source) {
        super(source);
    }

    public DeptConfigChangedEvent(Object source, String enterpriseId, String deptId) {
        super(source);
        this.enterpriseId=enterpriseId;
        this.deptId=deptId;
    }

    public String getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(String enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }
}
