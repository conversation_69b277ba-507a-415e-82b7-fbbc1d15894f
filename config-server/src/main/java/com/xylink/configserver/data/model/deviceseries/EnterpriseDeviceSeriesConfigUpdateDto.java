package com.xylink.configserver.data.model.deviceseries;

import com.xylink.configserver.data.model.DeviceConfigUpdate;
import com.xylink.configserver.data.model.EnterpriseNemoProfile;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-02-22 17:26
 */
@Data
public class EnterpriseDeviceSeriesConfigUpdateDto implements Serializable {
    @NotNull(message = "Required String parameter 'enterpriseId' is not present")
    private String enterpriseId;
    private EnterpriseNemoProfile profile;
    @Min(value = 1, message = "Min number is 1 for long parameter 'seriesId'")
    private long seriesId;
    /**
     * 更新的配置项 deviceSubType 需要使用seriesId去获取到subtypes集合去替换
     */
    private List<DeviceConfigUpdate> configs;
}
