package com.xylink.configserver.data.model;

import lombok.Data;

@Data
public class GwDevice {

    public enum GwType{
        UNKNOWN,H323,HYGW,
    }

    private String sn;
    private String number;
    private String securityKey;
    private long createdTimestamp = System.currentTimeMillis();
    private long expiredTimestamp ;
    private String type =GwType.UNKNOWN.name();
    private int maxInCount;
    private String pwd;
    private String enterpriseId;
    private Boolean isNative = true;

    public GwType getType() {
        return GwType.valueOf(type);
    }

    public void setType(GwType gwType) {
        if(gwType == null){
            this.type = GwType.UNKNOWN.name();
        }else{
            this.type = gwType.name();
        }
    }
}
