package com.xylink.configserver.data.model;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class NemoDefaultVersionConfig extends Model<NemoDefaultVersionConfig> {
    public static final String COMMON_CONFIG_KEY = "common";
    private long id;
    private String configName;
    private String configValue;
    private String clientConfigName;
    private String configVersionList;
    protected int configType;
    protected int baseConfigType;

    public void addToConfigs(Map<String, Map<String, String>> theDeviceConfigs) {
        String clientConfigName = getClientConfigName();
        if(clientConfigName == null || clientConfigName.trim().isEmpty()) {
            clientConfigName = COMMON_CONFIG_KEY;
        }
        Map<String, String> typedConfigs = theDeviceConfigs.get(clientConfigName);
        if(typedConfigs == null) {
            typedConfigs = new HashMap<>();
            theDeviceConfigs.put(clientConfigName, typedConfigs);
        }
        typedConfigs.put(getConfigName(), getConfigValue());
    }

}
