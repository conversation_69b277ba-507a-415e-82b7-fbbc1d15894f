package com.xylink.configserver.data.model.deviceseries;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xylink.configserver.data.model.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @since 2021-01-12 14:35
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("libra_device_series_subtype")
public class DeviceSeriesSubtypeEntity extends BaseEntity {
    /**
     * 终端系列ID
     */
    private Long seriesId;
    /**
     * 终端类型
     */
    @TableField("sub_type")
    private Integer subtype;
}
