package com.xylink.configserver.data.model.deviceseries;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xylink.configserver.data.model.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2021-01-12 15:09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("libra_device_subtype_series_config_dictionary")
public class DeviceSubtypeSeriesConfigDictionaryEntity extends BaseEntity {
    /**
     * 配置项编码
     */
    private String configCode;
    /**
     * 配置项显示名称
     */
    private String configName;
    /**
     * 配置项说明
     */
    private String configComment;
    /**
     * 配置项类型 group:组  item:项
     */
    private String configType;
    /**
     * 排序
     */
    private int configOrder;
    /**
     * 父级ID
     */
    private Long pId;
    /**
     * 类型 {@link com.xylink.configserver.data.model.deviceseries.DeviceGroupEnum}
     */
    private String dictionaryType;
}
