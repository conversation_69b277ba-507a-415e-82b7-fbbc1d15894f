package com.xylink.configserver.data.model.deviceseries;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xylink.configserver.data.model.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 * @since 2021-01-12 14:35
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("libra_device_series")
public class DeviceSeriesEntity extends BaseEntity {
    /**
     * 名称
     */
    private String seriesName;
    /**
     * 特殊系列：某些系列的配置项需特殊取值逻辑
     */
    private Integer special;
}
