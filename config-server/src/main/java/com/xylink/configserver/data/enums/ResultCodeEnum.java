package com.xylink.configserver.data.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @since 2022/7/11 16:40
 */
@Getter
@ToString
@AllArgsConstructor
public enum ResultCodeEnum {
    SUCCESS(1, "success"),
    FAIL(10000, "fail"),
    // 通用5xx请求
    REMOTE_CALLBACK_ERROR(500000, "remote callback error"),
    ZERO_COLUMN_UPDATE(500001, "zero column update"),
    INTERNAL_SERVER_ERROR(500002, "internal server error"),

    // 定制5xx请求

    // 通用4xx请求
    OVERLOAD_DUE_TO_EXCESSIVE_DATA(400000, "overload due to excessive data"), // 超出业务最大承载量
    INVALID_PARAMETER(400001, "invalid parameter"), // 无效参数
    HAS_BOUND(400002, "has bound"), // 已被绑定

    // 定制4xx请求
    INVALID_USER_PROFILE_ID(410000, "invalid user profile id"),
    INVALID_ENTERPRISE_ID(410001, "invalid enterprise id"),
    USER_IS_NOT_EXISTS(410002, "user is not exists"),
    DEVICE_IS_NOT_EXISTS(410003, "device is not exists"),
    INVALID_DEVICE_ID(410004, "invalid device id"), // 无效设备id
    INVALID_DEVICE_SN(410005, "invalid device sn"), // 无效设备sn

    // 特殊原因独占
    SERVICE_INSPECTION_FORBIDDEN(400300, "service inspection forbidden")
    ;

    private final int code;
    private final String message;

}