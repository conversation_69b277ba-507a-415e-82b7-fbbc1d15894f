package com.xylink.configserver.data.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("libra_gw_device")
public class GwDeviceModel {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    private String sn;
    private String number;
    private String sk;
    private Long createdTimestamp;
    private String gwType;
    private Integer maxInCount;
    private Long expiredTimestamp;
    private String pwd;
    private String enterpriseId;
    private Boolean isNative;
    private Boolean clusterId;

}
