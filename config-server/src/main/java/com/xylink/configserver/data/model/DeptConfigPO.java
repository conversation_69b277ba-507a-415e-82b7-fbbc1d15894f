package com.xylink.configserver.data.model;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("libra_dept_config")
public class DeptConfigPO extends BaseConfig{

    @TableField("enterprise_id")
    private String enterpriseId;

    @TableField("dept_id")
    private String deptId;

    @TableField("client_config_name")
    private String clientConfigName;

    @TableField("config_name")
    private String configName;

    @TableField("config_type")
    private int configType;

    @TableField("config_value")
    private String configValue;

    @TableField("create_time")
    private long createTime;

    @TableField("update_time")
    private long updateTime;

    public static DeptConfigPO build(DeviceConfigUpdate updateModel){
        DeptConfigPO deptConfigPO = new DeptConfigPO();
        deptConfigPO.setClientConfigName(updateModel.getClientConfigName());
        deptConfigPO.setConfigName(updateModel.getConfigName());
        deptConfigPO.setConfigValue(updateModel.getConfigValue());
        deptConfigPO.setConfigType(updateModel.getDeviceSubType());
        return deptConfigPO;
    }

}
