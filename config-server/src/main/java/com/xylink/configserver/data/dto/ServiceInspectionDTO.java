package com.xylink.configserver.data.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/6/9 12:08
 */
@Data
public class ServiceInspectionDTO {

    // 巡检项名称，如：redis、kafka、mysql
    private String inspectionItem;

    // 巡检值，0&1  表示 异常/正常
    private Integer inspectionValue;

    // 巡检异常情况时可携带异常简要信息，不要将堆栈都带入
    private String desc;

    public static ServiceInspectionDTO buildSucc(String item) {
        ServiceInspectionDTO serviceInspectionDTO = new ServiceInspectionDTO();
        serviceInspectionDTO.setInspectionItem(item);
        serviceInspectionDTO.setInspectionValue(1);
        serviceInspectionDTO.setDesc("成功");
        return serviceInspectionDTO;
    }

    public static ServiceInspectionDTO buildErr(String item, String errMsg) {
        ServiceInspectionDTO serviceInspectionDTO = new ServiceInspectionDTO();
        serviceInspectionDTO.setInspectionItem(item);
        serviceInspectionDTO.setInspectionValue(0);
        serviceInspectionDTO.setDesc(errMsg);
        return serviceInspectionDTO;
    }

}