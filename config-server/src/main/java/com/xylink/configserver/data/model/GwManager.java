package com.xylink.configserver.data.model;

import lombok.Data;

@Data
public class GwManager {

    public static enum GwMangerType{
        UNKNOWN,GWM,PSTNGWM
    }
    private String sn;
    private String number;
    private String securityKey;
    private long createdTimestamp = System.currentTimeMillis();
    private long expiredTimestamp ;
    private String type =GwMangerType.UNKNOWN.name();
    private int maxInCount;
    private String pwd;
    private String enterpriseId;
}
