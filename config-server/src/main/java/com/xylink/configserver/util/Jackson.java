package com.xylink.configserver.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.xylink.configserver.data.enums.ResultCodeEnum;
import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.exception.RestException;

import java.util.Map;

/**
 * 配置统一json工具
 *
 * <AUTHOR>
 * @since 2021/10/21 12:02 下午
 */

public class Jackson {

    private static final ThreadLocal<ObjectMapper> OBJECT_MAPPER_THREAD_LOCAL = ThreadLocal.withInitial(() -> {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);

        //忽略Map value为null
        objectMapper.configOverride(Map.class).setInclude(JsonInclude.Value.construct(JsonInclude.Include.NON_NULL, JsonInclude.Include.NON_NULL));

        // 忽略对象字段为null
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        return objectMapper;
    });

    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER_THREAD_LOCAL.get();
    }

    public static String writeValueAsString(Object obj) {

        try {
            return getObjectMapper().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RestException(HttpStatus.SERVER_ERROR_INTERNAL, ResultCodeEnum.INTERNAL_SERVER_ERROR.getCode(),
                                    "jackson parse error!");
        }

    }

    public static <T> T readValue(String str, TypeReference<T> typeReference) {

        try {
            return getObjectMapper().readValue(str, typeReference);
        } catch (JsonProcessingException e) {
            throw new RestException(HttpStatus.SERVER_ERROR_INTERNAL, ResultCodeEnum.INTERNAL_SERVER_ERROR.getCode(),
                                    "jackson parse error!");
        }

    }

}
