package com.xylink.configserver.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

@Slf4j
public class I18nNemoConfig {
    private static final ObjectMapper mapper = new ObjectMapper();
    private String locale;
    private String[] urls;

    public I18nNemoConfig() {
    }

    public String getLocale() {
        return this.locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String[] getUrls() {
        return this.urls;
    }

    public void setUrls(String[] urls) {
        this.urls = urls;
    }

    public static String[] getLocalizedValue(String configValue, String deviceLocale) {
        if (configValue != null) {
            try {
                I18nNemoConfig[] configs = (I18nNemoConfig[])mapper.readValue(configValue.trim(), I18nNemoConfig[].class);
                I18nNemoConfig cnConfig = null;
                I18nNemoConfig[] var4 = configs;
                int var5 = configs.length;

                for(int var6 = 0; var6 < var5; ++var6) {
                    I18nNemoConfig c = var4[var6];
                    if (c.getLocale().equalsIgnoreCase(deviceLocale)) {
                        return c.getUrls();
                    }

                    if (I18nNemoConfig.Lanuage.ZH_CN.getValue().equalsIgnoreCase(c.getLocale())) {
                        cnConfig = c;
                    }
                }

                if (cnConfig != null) {
                    return cnConfig.getUrls();
                }
            } catch (IOException var8) {
                log.error("getLocalizedValue error",var8);
            }
        }

        return null;
    }

    public static void main(String[] args) throws Exception {
        I18nNemoConfig config = new I18nNemoConfig();
        config.locale = "zh-CN";
        config.urls = new String[]{"http://devcdn.ainemo.com/page/nconsole/images/idleScreen/aixinkeshi/1.png", "http://devcdn.ainemo.com/page/nconsole/images/idleScreen/aixinkeshi/2.png", "http://devcdn.ainemo.com/page/nconsole/images/idleScreen/aixinkeshi/3.png"};
        System.out.println((new ObjectMapper()).writeValueAsString(new I18nNemoConfig[]{config}));
    }

    public static enum Lanuage {
        ZH_CN("zh-CN"),
        ZH_TW("zh-TW"),
        EN_US("en-US");

        private String value;

        private Lanuage(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }
    }
}
