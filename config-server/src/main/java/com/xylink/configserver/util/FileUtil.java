package com.xylink.configserver.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.InputStream;

@Slf4j
public class FileUtil {

    public static  <T> T parseJsonFile(InputStream file, Class<T> mapperClass) {
        T info = null;
            ObjectMapper mapper = new ObjectMapper();
            try{
                info = mapper.readValue(file, mapperClass);
            }
            catch(Exception ex) {
                log.error("fail to parse feature requirement list info " + ex.toString());
            }
        return info;
    }
}
