package com.xylink.configserver.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.xylink.configserver.enums.Configs;
import com.xylink.configserver.enums.DeviceType;
import com.xylink.configserver.service.ConfigValueHandleService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component
public class ConfigUtils {

    private ConfigValueHandleService configValueHandleService;

    private static ConfigUtils configUtils;

    public ConfigUtils(ConfigValueHandleService configValueHandleService) {
        this.configValueHandleService = configValueHandleService;
    }

    @PostConstruct
    public void init() {
        configUtils = this;
        configUtils.configValueHandleService = this.configValueHandleService;
    }

    //our defined putall to avoid put original reference to new map and change the original map later
    public static void putAll(Map<String, Map<String, String>> allConfigs, Map<String, Map<String, String>> toAdd) {
        if (toAdd == null) {
            return;
        }
        for (Map.Entry<String, Map<String, String>> entry : toAdd.entrySet()) {
            if (!allConfigs.containsKey(entry.getKey())) {
                Map<String, String> data = new HashMap<>();
                allConfigs.put(entry.getKey(), data);
            }
            allConfigs.get(entry.getKey()).putAll(entry.getValue());
        }
    }

    public static Map<String, String> combinedConfig(Map<String, Map<String, String>> sourceConfigs, int deviceType) throws JsonProcessingException {
        Map<String, String> combinedConfig = new HashMap<>();
        if (Objects.isNull(sourceConfigs)) {
            return combinedConfig;
        }
        Map<String, String> common = sourceConfigs.get(Configs.COMMON_CONFIG_KEY);
        if (Objects.nonNull(common)) {
            combinedConfig.putAll(common);
        }

        for (String clientConfigName : Configs.configsWithClientName) {
            Map<String, String> clientConfigs = sourceConfigs.get(clientConfigName);
            if (clientConfigs != null) {
                combinedConfig.put(clientConfigName, configUtils.configValueHandleService.transferToJson(
                        configUtils.configValueHandleService.parseConfigs(clientConfigs, clientConfigName)));
            }
        }
        if (deviceType == DeviceType.HARD.getValue()
                || deviceType == DeviceType.TVBOX.getValue()
                || deviceType == DeviceType.BIG_ENDPOINT_DEVICE.getValue()) {

            combinedConfig.put(
                    Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION,
                    configUtils.configValueHandleService.transferToJson(configUtils.configValueHandleService.parseConfigs(
                            sourceConfigs.get(Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION), Configs.NemoConfig.UI_DISPLAY_CUSTOMIZATION)
                    ));
        }
        return combinedConfig;
    }

    public static String transferCommonConfig(String clientConfigName) {
        if (StringUtils.isBlank(clientConfigName)) {
            return "common";
        }
        return clientConfigName.trim();
    }

}