package com.xylink.configserver.util;

import com.xylink.configserver.data.model.AppDetailInfo;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.Arrays;

@Slf4j
public class AppUAParser {
    public static String VERDOR_YST = "yst";

    public AppUAParser() {
    }

    public static AppDetailInfo parseAppInfo(String ua) {
        if (ua != null && !ua.trim().isEmpty()) {
            AppDetailInfo info = new AppDetailInfo();
            String[] values = ua.split("&");
            String[] var3 = values;
            int var4 = values.length;

            for(int var5 = 0; var5 < var4; ++var5) {
                String value = var3[var5];
                String[] detail = value.split("=");

                try {
                    Field field = info.getClass().getDeclaredField(detail[0].trim().toLowerCase());
                    field.setAccessible(true);
                    if (detail.length > 1) {
                        field.set(info, detail[1].trim());
                    } else {
                        log.warn("Invalid field: " + Arrays.toString(detail));
                    }
                } catch (SecurityException | IllegalArgumentException | IllegalAccessException | ArrayIndexOutOfBoundsException | NoSuchFieldException var10) {
                    log.warn("Failed to parse app ua info." + var10.getMessage());
                }
            }

            return info;
        } else {
            return new AppDetailInfo();
        }
    }
}
