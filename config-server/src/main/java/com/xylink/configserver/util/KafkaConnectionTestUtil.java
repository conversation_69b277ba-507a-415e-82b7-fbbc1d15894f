package com.xylink.configserver.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.config.SaslConfigs;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Kafka连接测试工具类
 * 用于测试Kafka集群的连接状态
 * 
 * <AUTHOR>
 * @since 2025-07-16
 */
@Slf4j
public class KafkaConnectionTestUtil {

    /**
     * 测试Kafka连接
     * 
     * @param bootstrapServers Kafka服务器地址
     * @param securityProtocol 安全协议
     * @param saslMechanism SASL机制
     * @param jaasConfig JAAS配置
     * @param timeoutSeconds 超时时间（秒）
     * @return 连接是否成功
     */
    public static boolean testConnection(String bootstrapServers, 
                                       String securityProtocol, 
                                       String saslMechanism, 
                                       String jaasConfig,
                                       int timeoutSeconds) {
        AdminClient adminClient = null;
        try {
            Map<String, Object> props = createAdminClientProps(
                bootstrapServers, securityProtocol, saslMechanism, jaasConfig, timeoutSeconds);
            
            adminClient = AdminClient.create(props);
            
            // 测试集群连接 - 获取集群ID
            String clusterId = adminClient.describeCluster()
                .clusterId()
                .get(timeoutSeconds, TimeUnit.SECONDS);
            
            log.info("Kafka连接测试成功，集群ID: {}", clusterId);
            return true;
            
        } catch (Exception e) {
            log.error("Kafka连接测试失败: {}", e.getMessage());
            return false;
        } finally {
            if (adminClient != null) {
                try {
                    adminClient.close(Duration.ofSeconds(2));
                } catch (Exception e) {
                    log.warn("关闭AdminClient时出现异常: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 创建AdminClient配置
     */
    private static Map<String, Object> createAdminClientProps(String bootstrapServers,
                                                            String securityProtocol,
                                                            String saslMechanism,
                                                            String jaasConfig,
                                                            int timeoutSeconds) {
        Map<String, Object> props = new HashMap<>();
        
        // 基础配置
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        
        // 超时配置
        int timeoutMs = timeoutSeconds * 1000;
        props.put(AdminClientConfig.DEFAULT_API_TIMEOUT_MS_CONFIG, timeoutMs);
        props.put(AdminClientConfig.REQUEST_TIMEOUT_MS_CONFIG, timeoutMs);
        props.put(AdminClientConfig.METADATA_MAX_AGE_CONFIG, timeoutMs);
        props.put(AdminClientConfig.CONNECTIONS_MAX_IDLE_MS_CONFIG, timeoutMs);
        
        // 安全配置
        if (StringUtils.hasText(securityProtocol)) {
            props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, securityProtocol);
            log.debug("设置安全协议: {}", securityProtocol);
        }
        
        if (StringUtils.hasText(saslMechanism)) {
            props.put(SaslConfigs.SASL_MECHANISM, saslMechanism);
            log.debug("设置SASL机制: {}", saslMechanism);
        }
        
        if (StringUtils.hasText(jaasConfig)) {
            props.put(SaslConfigs.SASL_JAAS_CONFIG, jaasConfig);
            log.debug("设置JAAS配置");
        }
        
        return props;
    }

    /**
     * 简单的连接测试（使用默认超时时间）
     */
    public static boolean testConnection(String bootstrapServers) {
        return testConnection(bootstrapServers, null, null, null, 5);
    }

    /**
     * 带安全配置的连接测试（使用默认超时时间）
     */
    public static boolean testConnectionWithSecurity(String bootstrapServers,
                                                   String securityProtocol,
                                                   String saslMechanism,
                                                   String jaasConfig) {
        return testConnection(bootstrapServers, securityProtocol, saslMechanism, jaasConfig, 5);
    }
}
