package com.xylink.configserver.util;

import com.ainemo.message.sender.service.protocl.Message;
import com.ainemo.message.sender.service.protocl.MessageFrame;
import com.ainemo.message.sender.service.util.IpUtil;
import com.ainemo.protocol.event.Event;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.io.BaseEncoding;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2019-06-24 23:13
 */
public final class MessageSenderUtils {

    private static final int MODULE_TYPE_2 = 2;
    private static final int MODULE_TYPE_3 = 3;

    private static final ObjectMapper objectMapper = new ObjectMapper();


    private MessageSenderUtils() {

    }

    public static Message createModuleType2Message(String serverName, String content) {
        return createDefaultMessage(serverName, MODULE_TYPE_2, false, content, System.currentTimeMillis());
    }

    public static Message createModuleType3Message(String serverName, String content, boolean persistent, long time) {
        return createDefaultMessage(serverName, MODULE_TYPE_3, persistent, content, time);
    }

    public static Message createDefaultMessage(String serverName, int moduleType, boolean persistent, String content, long time) {
        Message message = new Message();
        time = time == 0 ? System.currentTimeMillis() : time;
        message.setModuleType(moduleType);
        message.setNeedOfflineNotify(false);
        message.setMsgId(messageId(serverName, time));
        message.setPersistent(persistent);
        message.setContent(content);
        message.setTime(time);
        return message;
    }

    public static String messageId(String serverName, long time) {
        return serverName + "-" + IpUtil.getLocalIp() + "-" + time;
    }

    public static String bindToMsgServerContent() {
        return toJsonStr(new BindContent(true));
    }

    public static String unBindToMsgServerContent() {
        return toJsonStr(new BindContent(false));
    }

    public static String eventsToMsgServerContent(List<Event> events) {
        List<EventsContent.Event> target = events.stream().map(MessageSenderUtils::convert).collect(Collectors.toList());
        return toJsonStr(new EventsContent(target));
    }

    public static <T> String toJsonStr(T object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (IOException e) {
            throw new RuntimeException("Fail to format the msg string", e);
        }
    }

    public static String encode(MessageFrame frame) {
        int module = frame.getModule();
        String msg = frame.getMessage();
        byte[] msgByte = new byte[0];

        if (msg != null && msg.length() > 0) {
            msgByte = msg.getBytes(StandardCharsets.UTF_8);
        }

        int frameLen = 2 + msgByte.length;
        ByteBuffer buffer = ByteBuffer.allocate(frameLen);
        buffer.put((byte) module);
        buffer.put((byte) 0);
        buffer.put(msgByte);
        buffer.flip();
        return BaseEncoding.base64().encode(buffer.array());
    }

    public static String decode(String message) {
        return Arrays.toString(BaseEncoding.base64().decode(message));
    }

    public static void main(String[] args) {
        String message = "AgB7ImNvbnRlbnQiOnsicmVxdWVzdElkIjoiYWU3NDJmYzJiNmI5NGY4NDliNjhkMDQxZWU4NDFhOTMifSwic3ViVHlwZSI6MzZ9";
        System.out.println(decode(message));
    }

    private static EventsContent.Event convert(Event event) {
        String type = Objects.requireNonNull(com.xylink.configserver.data.enums.Event.Type.fromValue(event.getType())).name();
        return new EventsContent.Event(type, event.getTimestamp() == 0 ? System.currentTimeMillis() : event.getTimestamp(), event.getContent());
    }

    private static class BindContent {
        private final boolean bindstatus;

        public BindContent(boolean bindstatus) {
            this.bindstatus = bindstatus;
        }

        public boolean isBindstatus() {
            return bindstatus;
        }
    }

    private static class EventsContent {

        private final int count;

        private final List<Event> events;

        public EventsContent(List<Event> events) {
            this.events = events;
            this.count = events.size();
        }

        public int getCount() {
            return count;
        }

        public List<Event> getEvents() {
            return events;
        }

        public String jsonStr() {
            return this.toString();
        }

        private static class Event {
            private String type;
            private long timestamp;
            private String content;
            private String additional;

            public Event(String type, long timestamp, String content) {
                this.type = type;
                this.timestamp = timestamp;
                this.content = content;
            }

            public String getType() {
                return type;
            }

            public void setType(String type) {
                this.type = type;
            }

            public long getTimestamp() {
                return timestamp;
            }

            public void setTimestamp(long timestamp) {
                this.timestamp = timestamp;
            }

            public String getContent() {
                return content;
            }

            public void setContent(String content) {
                this.content = content;
            }

            public String getAdditional() {
                return additional;
            }

            public void setAdditional(String additional) {
                this.additional = additional;
            }
        }
    }


}