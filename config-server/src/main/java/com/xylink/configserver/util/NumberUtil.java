package com.xylink.configserver.util;

import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/12/21 11:21
 */
public class NumberUtil {

    public static boolean isInteger(String s) {

        if (!StringUtils.hasText(s)) {
            return false;
        } else {
            try {
                Integer.parseInt(s);
                return true;
            } catch (NumberFormatException var2) {
                return false;
            }
        }
    }

}
