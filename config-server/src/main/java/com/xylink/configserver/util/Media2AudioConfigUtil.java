package com.xylink.configserver.util;

import com.xylink.configserver.enums.Configs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

@Slf4j
public class Media2AudioConfigUtil {

    private enum  MediaChangeConfig {
        MIX_FIXED_GAIN_VALUE("micfixedgainvalue",0),
        ENABLE_AES_STREAM_ALI_NEW("enableAEStreamAliNew",1),
        AUDIO_EQ("audioEQ",2),
        ENABLE_AE_DUMPER("enableAEDumper",3),
        AUDIO_DIAG_CLOCK_DRIFT("audioDiagClockDrift",4),
        AUDIO_DIAG_REVERB("audioDiagReverb",5),
        AUDIO_CALIBRATION_SYNC("audioCalibrationSync",6),
        AUDIO_BEAMFORMING_ALG("audiobeamformingalg",7),
        AUDIO_BEST_MIC_SELECT("audio_bestmicselect",8);

        /**
         * 配置项名称
         * */
        private String configName;
        /**
         * orderValue只是用来与audioConfig相互对应，不是配置默认值
         */
        private int orderValue;

        MediaChangeConfig(String configName, int value) {
            this.configName = configName;
            this.orderValue = value;
        }

        public String getConfigName() {
            return configName;
        }

        public int getOrderValue() {
            return orderValue;
        }

        public static MediaChangeConfig valueOf(int value) {
            for (MediaChangeConfig mediaConfig : MediaChangeConfig.values()) {
                if (mediaConfig.orderValue == value) {
                    return mediaConfig;
                }
            }
            return null;
        }
        public static MediaChangeConfig configNameOf(String name) {
            for (MediaChangeConfig mediaConfig : MediaChangeConfig.values()) {
                if (mediaConfig.configName.equals(name)) {
                    return mediaConfig;
                }
            }
            return null;
        }

    }

    private   enum  AudioChangeConfig {
        NEW_MIX_FIXED_GAIN_VALUE("micfixedgainvalue",0),
        STREAM_ALIGNMENT("streamAlignment",1),
        NEW_AUDIO_EQ("audioEQ",2),
        NEW_ENABLE_AE_DUMPER("enableAEDumper",3),
        ENABLE_DIAG_CLOCK_DRIFT("enableDiagClockDrift",4),
        ENABLE_DIAG_REVERB("enableDiagReverb",5),
        ENABLE_CALIBRATION_SYNC("enableCalibrationSync",6),
        BEAN_FORMING_METHOD("BeamformingMethod",7),
        BEST_MIC_SELECT("bestMicSelect",8);

        /**
         * 配置项名称
         * */
        private String configName;
        /**
         * orderValue只是用来与audioConfig相互对应，不是配置默认值
         */
        private int orderValue;
        AudioChangeConfig(String configName, int orderValue) {
            this.configName = configName;
            this.orderValue = orderValue;
        }
        public static AudioChangeConfig valueOf(int value) {
            for (AudioChangeConfig audioConfig : AudioChangeConfig.values()) {
                if (audioConfig.orderValue == value) {
                    return audioConfig;
                }
            }
            return null;
        }
        public static AudioChangeConfig configNameOf(String name) {
            for (AudioChangeConfig audioConfig : AudioChangeConfig.values()) {
                if (audioConfig.configName.equals(name)) {
                    return audioConfig;
                }
            }
            return null;
        }


    }


    public static Set<String> syncMedia2AudioConfigName = Collections.unmodifiableSet(new HashSet<String>() {
        {
            this.add("micfixedgainvalue");
            this.add("enableAEStreamAliNew");
            this.add("streamAlignment");
            this.add("audioEQ");
            this.add("enableAEDumper");
            this.add("audioDiagClockDrift");
            this.add("enableDiagClockDrift");
            this.add("audioDiagReverb");
            this.add("enableDiagReverb");
            this.add("audioDiagReverb");
            this.add("audioCalibrationSync");
            this.add("enableCalibrationSync");
            this.add("audiobeamformingalg");
            this.add("beamformingMethod");
            this.add("audio_bestmicselect");
            this.add("bestMicSelect");

        }
    });

    /**
     * @param configName 配置项名称
     * @param clientConfigName 配置项类别
     * @return mediaconfig 与audioconfig 转换后 configName，clientConfigName
     * */
    public static String getMedia2AudioConfig(String configName, String clientConfigName){
        if (StringUtils.isBlank(configName) || StringUtils.isBlank(clientConfigName)){
            return "";
        }
        log.info("matching mediaConfig and audioConfig and clientconfigname is  " + clientConfigName +" and config is :" + configName);
        MediaChangeConfig mediaChangeConfig ;
        AudioChangeConfig audioChangeConfig ;

        if (Configs.NemoConfig.MEDIA_CONFIG.equals(clientConfigName)){
            mediaChangeConfig = MediaChangeConfig.configNameOf(configName);
            if (mediaChangeConfig != null){
                audioChangeConfig = AudioChangeConfig.valueOf(mediaChangeConfig.orderValue);
                if (audioChangeConfig != null){
                    return audioChangeConfig.configName;
                }
            }

        }else if (Configs.NemoConfig.AUDIO_CONFIG.equals(clientConfigName)){
            audioChangeConfig = AudioChangeConfig.valueOf(clientConfigName);
            if (audioChangeConfig != null){
                mediaChangeConfig = MediaChangeConfig.valueOf(audioChangeConfig.orderValue);
                if (mediaChangeConfig != null){
                    return mediaChangeConfig.configName;

                }
            }
        }
        return "";

    }
}
