package com.xylink.configserver.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xylink.configserver.data.model.DefaultServerConfig;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class SerializeClientConfigUtil {

    private static final ObjectMapper jsonMapper = new ObjectMapper();

    public static String serializeClientConfig(Map<String, String> clientConfigs) {
        Map<String, Object> clientConfigObjs = new HashMap<>();
        for(Map.Entry<String, String> entry : clientConfigs.entrySet()) {
            if(entry.getValue() == null){
                clientConfigObjs.put(entry.getKey(), "");
                continue;
            }
            if(entry.getValue().toUpperCase().equals("TRUE") || entry.getValue().toUpperCase().equals("FALSE")) {
                Boolean value = Boolean.parseBoolean(entry.getValue());
                clientConfigObjs.put(entry.getKey(), value);
            }
            try {
                Long value = jsonMapper.readValue(entry.getValue(), Long.class);
                clientConfigObjs.put(entry.getKey(), value);
                continue;
            } catch (IOException e) {
            }
            try {
                Integer value = jsonMapper.readValue(entry.getValue(), Integer.class);
                clientConfigObjs.put(entry.getKey(), value);
                continue;
            } catch (IOException e) {
            }
            try {
                Double value = jsonMapper.readValue(entry.getValue(), Double.class);
                clientConfigObjs.put(entry.getKey(), value);
                continue;
            } catch (IOException e) {
            }
            try {
                Object[] value = jsonMapper.readValue(entry.getValue(), Object[].class);
                clientConfigObjs.put(entry.getKey(), value);
                continue;
            } catch (IOException e) {
            }
            clientConfigObjs.put(entry.getKey(), entry.getValue());
        }
        try {
            return jsonMapper.writeValueAsString(clientConfigObjs);
        } catch (JsonProcessingException e) {
            log.error("Failed to serialize objects", e);
            return null;
        }
    }

    public static  Map<String,Object> coverValueType(Map<String, DefaultServerConfig> config){
        Map<String, Object> serverConfigs = new HashMap<>();
        config.forEach((key, value) -> {
            Object v = value.getConfigValue();
            int valueType = value.getValueType();
            if (valueType == 2){
                v = Integer.parseInt(value.getConfigValue());
            }else if(valueType == 3){
                try {
                    v = Jackson.getObjectMapper().readValue(value.getConfigValue(), Object.class);
                } catch (JsonProcessingException e) {
                    e.printStackTrace();
                }
            }
            serverConfigs.put(key,v);
        });
        return serverConfigs;
    }
}
