package com.xylink.configserver.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PrivateCloud {
    private static final boolean isPrivate = Boolean.getBoolean("PRIVATE_CLOUD");

    public PrivateCloud() {
    }

    public static final boolean isPrivate() {
        return isPrivate;
    }

    public static final void main(String[] args) {
        System.out.println(isPrivate);
    }

    static {
        log.info("This is " + (isPrivate ? "private" : "public") + " cloud.");
    }
}
