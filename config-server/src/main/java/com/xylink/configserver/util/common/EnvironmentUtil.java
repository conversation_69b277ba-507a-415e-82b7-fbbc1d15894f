package com.xylink.configserver.util.common;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/7/27 15:35
 */
public class EnvironmentUtil {

    public enum DbType {
        MYSQL,
        PGSQL,
        DM,
        ST,
        JC,
        OB;

        public static DbType valueFrom(String db) {
            if (StringUtils.isBlank(db)) {
                return DbType.MYSQL;
            }

            for (DbType dbType : values()) {
                if (dbType.name().equals(db)) {
                    return dbType;
                }
            }

            return DbType.MYSQL;
        }
    }

    public static DbType getDbType() {
        return DbType.valueFrom(System.getenv("DATABASE_TYPE"));
    }

}