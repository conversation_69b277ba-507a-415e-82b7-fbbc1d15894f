package com.xylink.configserver.util;

import com.xylink.configserver.data.model.AppDetailInfo;
import com.xylink.configserver.data.model.UserDevice;
import com.xylink.configserver.enums.DeviceType;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class RestApiContext {

    private static ThreadLocal<AppDetailInfo> currentClientInfo = new ThreadLocal();

    private static ThreadLocal<UserDevice> currentDevice = new ThreadLocal();

    private static ThreadLocal<DeviceType> currentDeviceType = new ThreadLocal<>();

    public static AppDetailInfo getCurrentClientInfo() {
        return currentClientInfo.get();
    }

    public static void setCurrentClientInfo(AppDetailInfo clientInfo) {
        currentClientInfo.set(clientInfo);
    }

    public static void removeCurrentClientInfo() {
        currentClientInfo.remove();
    }

    public static UserDevice getCurrentDevice() {
        return currentDevice.get();
    }

    public static void setCurrentDevice(UserDevice userDevice) {
        currentDevice.set(userDevice);
    }

    public static void removeCurrentDevice() {
        currentDevice.remove();
    }

    public static DeviceType getCurrentDeviceType() {
        return currentDeviceType.get();
    }

    public static void setCurrentDeviceType(DeviceType deviceType) {
        currentDeviceType.set(deviceType);
    }

    public static void removeCurrentDeviceType() {
        currentDevice.remove();
    }
}
