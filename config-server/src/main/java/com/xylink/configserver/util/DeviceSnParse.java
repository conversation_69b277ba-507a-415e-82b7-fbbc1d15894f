package com.xylink.configserver.util;

import com.xylink.configserver.data.model.DeviceSubtypeModelV2;
import com.xylink.configserver.data.model.SnAndFingerprint;
import com.xylink.configserver.enums.DeviceSubType;
import com.xylink.configserver.enums.DeviceType;
import com.xylink.configserver.enums.NemoModel;
import com.xylink.configserver.enums.ProductFamily;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class DeviceSnParse {

    private static String ME40 = "5D";
    private static String ME90 = "6D";
    private static String NE60 = "7D";
    private static String ME20 = "5A";

    public static SnAndFingerprint parse(String snAndFgp) {

        if(StringUtils.isEmpty(snAndFgp))
            return new SnAndFingerprint(snAndFgp, null);

        int splitter = snAndFgp.indexOf("_");

        if(splitter > 0) {
            return new SnAndFingerprint(snAndFgp.substring(0, splitter), snAndFgp.substring(splitter + 1));
        }

        return new SnAndFingerprint(snAndFgp, "");
    }

    public static SnAndFingerprint parse(String snAndFgp, DeviceType type) {

        if(type == DeviceType.SOFT || type == DeviceType.DESKTOP)
            return new SnAndFingerprint(snAndFgp, "");

        return parse(snAndFgp);
    }

    public static NemoModel getNemoModelTypeBySn(String sn) {

        if (StringUtils.isEmpty(sn))
            return null;

        NemoModel nemoModel = NemoModel.NV1001;	// default
        String nm = sn.substring(0,1);

        if (nm.equals("2")){
            nemoModel = NemoModel.NV1001;
        } else if (nm.equals("3")) {
            nemoModel = NemoModel.NV1010;
        } else if (nm.equals("4")){
            nemoModel = NemoModel.NV1501;
        } else if (nm.equals("8")) {
            nemoModel = NemoModel.NE2001;
        }

        return nemoModel;

    }

    public static String getNemoModelBySn(String sn){
        NemoModel nemoModel = getNemoModelTypeBySn(sn);
        return nemoModel == null ? null : nemoModel.getValue();

    }

    public static boolean isEnterpriseNemoDecideBySn(String sn){
        if (StringUtils.isEmpty(sn)) {
            return false;
        }
        return sn.startsWith("7") || sn.startsWith("8");
    }


    public static ProductFamily getProductFamilyBySn(String sn) {
        if (StringUtils.isEmpty(sn)) {
            return ProductFamily.NEMO_CLASSIC;
        }
        return sn.startsWith("3F") ? ProductFamily.NEMO_MINI : ProductFamily.NEMO_CLASSIC;
    }

    public static int getBasicSubTypeBySn(String sn) {
        if(sn == null) {
            return -1;
        }
        if(sn.startsWith(ME20) || sn.startsWith(ME40)) {
            return DeviceSubType.GILL.getValue();
        } else if(sn.startsWith(ME90)) {
            return DeviceSubType.ME90.getValue();
        } else if(sn.startsWith(NE60)) {
            return DeviceSubType.NE60.getValue();
        } else {
            return DeviceSubType.NEMO.getValue();
        }
    }

    public static void main(String[] args) {
        System.out.println(parse("ffffffff-d1bd-7ea9-9d83-0c6710423c33_125aab45"));
    }

    /**
     * 是否是要交端口使用费的设备
     * @param deviceType
     * @param subType
     * @param deviceSn
     * @return
     */
    public static boolean isEnterpriseDeviceByDeviceTypeAndSn(int deviceType,int subType, String deviceSn, DeviceSubtypeModelV2 deviceSubtypeModelV2) {

        try{
            switch (DeviceType.valueOf(deviceType)){
                case HARD:
                    if(deviceSn.startsWith("7") || deviceSn.startsWith("8")){
                        return true;
                    }
            }
            if (deviceSubtypeModelV2.isChargePort()){
                return true;
            }

        }catch (Exception e){
            log.error("isEnterpriseDeviceByDeviceTypeAndSn error! devicetype="+deviceType+" subtype="+subType+" devicesn:"+deviceSn,e);
            return false;
        }

        return false;
    }
}
