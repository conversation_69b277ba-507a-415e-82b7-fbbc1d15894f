package com.xylink.configserver.util;

public class Constants {

    public static final String IAUTH_ROLE_RESOURCE_URL = "/api/rest/internal/v1/role/%s/resources";
    public static final String BUFFET_ENTERPRISE_CONFIG_URL = "/api/rest/internal/v1/buffet/enterprise/config?enterpriseId=";
    public static final String CONNECTION_TEST_URI = "/api/rest/internal/v1/locator/%s/server/cache/connectionTest/configs";
    public static final String DEVICE_PRESENCE_REQUEST="/api/rest/internal/v3/en/presence/query";

    public static final String VCS_DELETE_CLIENT_FEATURE = "/api/rest/internal/v1/vcs/deleteClientFeature?featureId=%s&sn=%s";

    public static final String CUSTOMIZED_FEATURES = "/api/rest/internal/v1/nemo/%s/customizedfeatures";

    public static final String VCS_CUSTOMIZED_FEATURES = "/api/rest/internal/v1/vcs/%s/customizedfeatures";

    public static final String BILL_RESOLUTION_URL = "/api/rest/internal/v1/bill/device/resource-record/newest-4k";

    public static final String REDIS_KEY_DEVICE_PRESENCE = "device_presence";
    public static final  String APP_INFO = "APP_INFO";

    public static final String ME_NEMO_SHOW_JOIN_ENT_QRCODE= "showJoinEntQRCode";

    public static final String CONNECTION_TEST = "connectionTest";

    public static final String SITE_PATH = "sitePath";

    public static final String PUBLIC_PATH_KEY = "default";

    public static final String EN_NEMOS_WITHOUT_ADMIN = "enNemosWithoutAdmin";

    public final static String LOGIN_USER_PASSWORD_EXPIRE_TIME = "LOGIN_USER_PASSWORD_EXPIRE_TIME";

    public static final String SERVER_ABILITY_MATCH_UP = "server_ability_match_up";

    public static final String ABILITY_DEVICE  = "ability:device:";

    public static final String ABILITY_COMBINED  = "ability:combined:";

    public static final String SOFT = "@SOFT";
    public static final String HARD = "@HARD";
    public static final String DESK = "@DESK";
    public static final String BRUCE = "@BRUCE";
    public static final String TVBOX = "@TVBOX";

    public static final  Integer sendMessage2DeviceLimitCount = 200;

    public static final int TYPE_NEMO_CONFIG = 2;
    public static final int TYPE_H5_CONFIG = 10;
    public static final int TYPE_PC_CONFIG=5;
    public static final int TYPE_APP_CONFIG = 1;
    public static final int TYPE_TVBOX_CONFIG=8;
    public static final int TYPE_BRUCE_CONFIG=7;
    public static final String BILL_GATEWAY_RESOURCE_URL = "/api/rest/internal/v1/bill/gateway/get";
    public static final String OMS_SHORT_MESSAGE_URL = "/api/rest/oms/v1/internal/shortMessage/enterpriseName?enterpriseId=";
    //根据部门id查询设备
    public static final String CONTACT_SCROLL_DEVICE_BY_DEPT_URL = "/api/rest/department/internal/v1/listMemberId/scroll";
    //根据设备id查询所属部门
    public static final String CONTACT_ONE_LEVEL_DEPT_BY_DEVICE_URL = "/api/rest/department/internal/v1/getMainParentDepartmentIdAndLevelByMemberIds";

    //查询部门是否可配置背景水印
    public static final String CONTACT_DEPT_SWITCH_URL = "/console/api/rest/internal/v1/enterprise/configByName";

    //查询企业的跟部门id
    public static final String CONTACT_QUERY_ROOT_DEPARTMENT_BY_ENTERPRISE_ID = "/api/rest/department/internal/v1/department/{enterpriseId}";

    //根据部门id查询所属子部门
    public static final String CONTACT_CHILD_DEPT_BY_DEPT_ID = "/api/rest/department/internal/v1/childDepIds";
}