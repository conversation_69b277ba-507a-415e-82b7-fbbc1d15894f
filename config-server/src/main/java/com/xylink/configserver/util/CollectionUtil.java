package com.xylink.configserver.util;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.*;

public class CollectionUtil {

    public static <T> List<T> newArrayList(){
        return new ArrayList<T>();
    }

    public static <T> List<T> newArrayList(T... array){
        List<T> list = new ArrayList<>();
        if (array.length > 0){
            for (T t : array) {
                list.add(t);
            }
        }
        return list;
    }

    public static boolean isEmpty(List list){
        return list==null || list.size()==0;
    }

    public static boolean isNotEmpty(List list){
        return !isEmpty(list);
    }

    public static boolean isEmpty(Map map){
        return map==null || map.size()==0;
    }

    public static boolean isNotEmpty(Map map){
        return !isEmpty(map);
    }

    public static <K, V> Map<K, V>  newMap(Entry<K, V>... entryArray){
        Map<K, V> map = new HashMap<>();
        if (Objects.nonNull(entryArray) && entryArray.length>0){
            for (Entry<K, V> entryItem : entryArray){
                map.put(entryItem.getKey(), entryItem.getValue());
            }
        }
        return map;
    }
    
    public static <K, V> Entry<K, V> kv(K key, V value){
        return new Entry<K, V>(key, value);
    }

    @Data
    @AllArgsConstructor
    public static class Entry<K,V> {
        private K key;
        private V value;
    }

    public static void main(String[] args) {

        Map<String, String> map1 = CollectionUtil.newMap(
                kv("1", "a")
        );

    }

}
