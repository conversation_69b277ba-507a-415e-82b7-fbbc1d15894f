package com.xylink.configserver.configuration;

import com.xylink.configserver.util.SnowflakeIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class CommonConfiguration {

    @Value("${common.id.trace.workId}")
    int traceWrokId;

    @Value("${common.id.centerId}")
    int centerId;

    @Value("${common.seaMonitorEnable:true}")
    private boolean seaMonitorEnable;

    @Bean
    public SnowflakeIdGenerator snowflakeIdWorker() {
        SnowflakeIdGenerator snowflakeIdGenerator = new SnowflakeIdGenerator(traceWrokId, centerId);
        log.info("SnowflakeIdGenerator [{}]", snowflakeIdGenerator);
        return snowflakeIdGenerator;
    }

    public boolean isSeaMonitorEnable() {
        return seaMonitorEnable;
    }

}
