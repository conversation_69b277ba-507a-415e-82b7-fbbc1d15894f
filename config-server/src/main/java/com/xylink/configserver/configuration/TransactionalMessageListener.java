package com.xylink.configserver.configuration;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.xylink.configserver.data.model.*;
import com.xylink.configserver.enums.Configs;
import com.xylink.configserver.enums.DeviceType;
import com.xylink.configserver.mapper.UserDeviceMapper;
import com.xylink.configserver.proxy.InternalApiProxy;
import com.xylink.configserver.service.*;
import com.xylink.configserver.util.ThreadUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/5/25 9:26 下午
 */
@Slf4j
@Component
public class TransactionalMessageListener {
    @Resource
    private NotificationService notificationService;
    private final JsonMapper jsonMapper = new JsonMapper();

    @Autowired
    private InternalApiProxy internalApiProxy;

    @Autowired
    private UserDeviceMapper userDeviceMapper;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private PresenceService presenceService;

    @Autowired
    private OceanCollectionService oceanCollectionService;

    @Autowired
    private DeptConfigService deptConfigService;

    private static final ThreadFactory DEFAULT_THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("NotifyConfigChangedThread-%d").build();
    ExecutorService enterpriseDeviceMessageExecutor = new ThreadPoolExecutor(2,
            4,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(300),
            DEFAULT_THREAD_FACTORY,
            new ThreadPoolExecutor.CallerRunsPolicy()
    );

    @TransactionalEventListener(classes = EnterpriseConfigChangedEvent.class)
    public void handleEnterpriseConfigsChange(EnterpriseConfigChangedEvent event) {
        EnterpriseNemoConfig data;
        try {
            data = (EnterpriseNemoConfig) event.getSource();
            log.info("EnterpriseConfigChangedEvent:EnterpriseId:{} changes configs:{}", event.getEnterpriseId(), jsonMapper.writeValueAsString(data));
        } catch (Exception e) {
            log.error("EnterpriseConfigChangedEvent:HandleEnterpriseConfigsChange error.", e);
            return;
        }
        if (data != null) {
            enterpriseDeviceMessageExecutor.execute(() ->
                    notificationService.norifyProfileNemoConfigChange(data)
            );
        }
    }





    @TransactionalEventListener(classes = DeptConfigChangedEvent.class)
    public void handDeptConfigChange(DeptConfigChangedEvent event) {
        try {
            log.info("部门配置变更事件: enterpriseId={}, deptId={}, sourceClass={}",
                    event.getEnterpriseId(), event.getDeptId(), event.getSource().getClass().getName());

            // 1. 获取并处理部门配置
            List<String> childDeptIds = getChildDeptIds(event);
            
            // 2. 获取配置更新列表
            final List<DeviceConfigUpdate> configUpdates = new ArrayList<>((List<DeviceConfigUpdate>) event.getSource());
            
            // 3. 分页处理设备
            processDevicesInBatches(event.getEnterpriseId(), event.getDeptId(), childDeptIds, configUpdates);
            
            log.info("部门配置变更处理完成: enterpriseId={}, deptId={}", event.getEnterpriseId(), event.getDeptId());
        } catch (Exception e) {
            log.error("处理部门配置变更事件失败: {}", event, e);
        }
    }

    private List<String> getChildDeptIds(DeptConfigChangedEvent event) {
        String deptId = event.getDeptId();
        String rootDeptId = internalApiProxy.getDeptRootId(event.getEnterpriseId());
        
        if (!StringUtils.hasText(rootDeptId) || !deptId.equals(rootDeptId)) {
            return null;
        }

        List<String> childDeptIdList = internalApiProxy.findChildDeptBySourceDeptId(event.getEnterpriseId(), deptId);
        if (CollectionUtils.isEmpty(childDeptIdList)) {
            return null;
        }

        return deptConfigService.findConfigListByDeptIds(event.getEnterpriseId(), childDeptIdList)
                .stream()
                .map(DeptConfigPO::getDeptId)
                .distinct()
                .filter(id -> !id.equals(deptId))
                .collect(Collectors.toList());
    }

    private void processDevicesInBatches(String enterpriseId, String deptId, List<String> childDeptIds, List<DeviceConfigUpdate> configUpdates) {
        Long totalCount = userDeviceMapper.selectCount(new QueryWrapper<>());
        String scrollId = "";
        int batchSize = 200;
        int maxBatches = (int) Math.ceil((double) totalCount / batchSize);

        for (int i = 0; i < maxBatches; i++) {
            ContactScrollModel scrollModel = internalApiProxy.scrollGetCurrentDeptDeviceFormContact(
                    enterpriseId, deptId, scrollId, childDeptIds);

            if (!isValidScrollModel(scrollModel)) {
                log.info("无更多设备需要处理: deptId={}", deptId);
                break;
            }

            processDeviceBatch(scrollModel.getMemberIdTuple().getDeviceIdSet(), configUpdates);
            
            scrollId = scrollModel.getNextScrollId();
            if (!StringUtils.hasText(scrollId)) {
                log.info("分页处理完成: deptId={}", deptId);
                break;
            }
        }
    }

    private boolean isValidScrollModel(ContactScrollModel model) {
        return Objects.nonNull(model) 
                && Objects.nonNull(model.getMemberIdTuple())
                && !CollectionUtils.isEmpty(model.getMemberIdTuple().getDeviceIdSet());
    }

    private void processDeviceBatch(List<String> deviceIds, List<DeviceConfigUpdate> configUpdates) {
        List<UserDevice> devices = userDeviceMapper.batchGetDeviceListByIdList(deviceIds);
        devices.forEach(device -> enterpriseDeviceMessageExecutor.execute(() -> 
            processDevice(device, new ArrayList<>(configUpdates))));
    }

    private void processDevice(UserDevice device, List<DeviceConfigUpdate> configUpdates) {
        try {
            log.debug("处理设备: {} 在线程: {}", device.getId(), Thread.currentThread().getName());
            ThreadUtil.sleep(5);

            if (!isDeviceOnline(device)) {
                handleOfflineDevice(device);
                return;
            }

            handleDeviceConfigUpdates(device, configUpdates);
        } catch (Exception e) {
            log.error("处理设备配置失败: deviceId={}, error={}", device.getId(), e.getMessage(), e);
        }
    }

    private boolean isDeviceOnline(UserDevice device) {
        long number = device.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue() 
                ? deviceService.getNemoNumberByDeviceId(device.getId()) 
                : -1;
        return presenceService.deviceIsOnline(device, String.valueOf(number));
    }

    private void handleOfflineDevice(UserDevice device) {
        log.info("设备离线或BRUE: deviceId={}", device.getId());
        oceanCollectionService.deviceConfigUpdate(device);
    }

    private void handleDeviceConfigUpdates(UserDevice device, List<DeviceConfigUpdate> configUpdates) {
        boolean carouselHandled = handleCarouselImages(device, configUpdates);
        
        if (configUpdates.isEmpty() && carouselHandled) {
            log.debug("设备: {} - 仅处理了轮播图配置", device.getId());
            return;
        }

        RestNemoConfig[] configArray = buildConfigArray(configUpdates);
        if (configArray.length == 0) {
            log.warn("设备: {} - 无有效配置需要发送", device.getId());
            return;
        }

        sendConfigUpdates(device, configArray);
    }

    private boolean handleCarouselImages(UserDevice device, List<DeviceConfigUpdate> configUpdates) {
        if (configUpdates.contains(Configs.NemoConfig.CAROUSEL_IMAGES)) {
            notificationService.notifyScreenSaverChanged(device.getId(), device.getType());
            configUpdates.remove(Configs.NemoConfig.CAROUSEL_IMAGES);
            return true;
        }
        return false;
    }

    private RestNemoConfig[] buildConfigArray(List<DeviceConfigUpdate> configUpdates) {
        return configUpdates.stream()
                .map(RestNemoConfig::build)
                .collect(Collectors.toList())
                .toArray(new RestNemoConfig[0]);
    }

    private void sendConfigUpdates(UserDevice device, RestNemoConfig[] configArray) {
        if (device.getType() == DeviceType.BIG_ENDPOINT_DEVICE.getValue()) {
            List<RestNemoConfig> configList = new ArrayList<>(configArray.length);
            Collections.addAll(configList, configArray);
            notificationService.notifyChangeDeviceConfig(
                    notificationService.restNemoConfigToMap(configList), 
                    device);
        } else {
            notificationService.notifyNemoConfigChange(device, configArray);
        }
        log.debug("设备: {} - 配置通知已发送", device.getId());
    }

}
