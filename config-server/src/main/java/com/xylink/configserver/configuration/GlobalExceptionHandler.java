package com.xylink.configserver.configuration;

import com.xylink.configserver.data.model.HttpStatus;
import com.xylink.configserver.data.model.RestMessage;
import com.xylink.configserver.enums.ErrorStatus;
import com.xylink.configserver.exception.RestException;
import com.xylink.configserver.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @since 2021-01-08 14:17
 */
@Slf4j
@ControllerAdvice
public class GlobalExceptionHandler {
    /**
     * handle {@link com.xylink.configserver.exception.RestException} ,response status : {@link RestException#getHttpCode()}
     *
     * @param response
     * @param restException
     * @return
     */
    @ResponseBody
    @ExceptionHandler(RestException.class)
    public RestMessage restException(HttpServletResponse response, RestException restException) {
        log.error(restException.getMessage(), restException);
        response.setStatus(restException.getHttpCode());
        return restException.getMsg();
    }

    @ResponseBody
    @ExceptionHandler(com.xylink.configserver.data.exception.RestException.class)
    public RestMessage restExceptionV2(HttpServletResponse response, com.xylink.configserver.data.exception.RestException restException) {
        log.error(restException.getMessage(), restException);
        response.setStatus(restException.getHttpCode());
        return restException.getMsg();
    }

    /**
     * handle {@link com.xylink.configserver.exception.ServiceException} ,response status : 400
     *
     * @param response
     * @param serviceException
     * @return
     */
    @ResponseBody
    @ExceptionHandler(ServiceException.class)
    public RestMessage serviceException(HttpServletResponse response, ServiceException serviceException) {
        log.error(serviceException.getMessage(), serviceException);
        RestException restException = build(serviceException, HttpStatus.CLIENT_ERROR_BAD_REQUEST);
        response.setStatus(restException.getHttpCode());
        return restException.getMsg();
    }

    /**
     * handle {@link org.springframework.web.bind.MethodArgumentNotValidException} ,response status : 400
     *
     * @param response
     * @param methodArgumentNotValidException
     * @return
     */
    @ResponseBody
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public RestMessage serviceException(HttpServletResponse response, MethodArgumentNotValidException methodArgumentNotValidException) {
        log.error(methodArgumentNotValidException.getMessage(), methodArgumentNotValidException);
        BindingResult bindResult = methodArgumentNotValidException.getBindingResult();
        StringBuilder sb = new StringBuilder();
        if (bindResult.hasErrors()) {
            bindResult.getAllErrors().forEach(item -> sb.append(item.getDefaultMessage()).append(";"));
        }
        RestException restException = new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER.getErrorCode(), ErrorStatus.INVALID_PARAMETER.getResId(), sb.toString());
        response.setStatus(restException.getHttpCode());
        return restException.getMsg();
    }

    /**
     * handle {@link org.springframework.web.bind.MissingServletRequestParameterException} ,response status : 400
     *
     * @param response
     * @param missingServletRequestParameterException
     * @return
     */
    @ResponseBody
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public RestMessage serviceException(HttpServletResponse response, MissingServletRequestParameterException missingServletRequestParameterException) {
        log.error(missingServletRequestParameterException.getMessage(), missingServletRequestParameterException);
        RestException restException = new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST, ErrorStatus.INVALID_PARAMETER.getErrorCode(), ErrorStatus.INVALID_PARAMETER.getResId(), missingServletRequestParameterException.getMessage());
        response.setStatus(restException.getHttpCode());
        return restException.getMsg();
    }

    @ResponseBody
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public RestMessage methodNotSupportedException(HttpServletResponse response,
                                                   HttpRequestMethodNotSupportedException exception) {
        log.error(exception.getMessage(), exception);
        RestException restException = new RestException(HttpStatus.CLIENT_ERROR_BAD_REQUEST,
                                                        ErrorStatus.INVALID_PARAMETER.getErrorCode(),
                                                        ErrorStatus.INVALID_PARAMETER.getResId(),
                                                        exception.getMessage());
        response.setStatus(restException.getHttpCode());
        return restException.getMsg();
    }

    /**
     * handle {@link java.lang.Exception},response status : 500
     *
     * @param exception
     * @return
     */
    @ResponseBody
    @ExceptionHandler(Exception.class)
    public RestMessage exception(HttpServletResponse response, Exception exception) {
        log.error("Exception", exception);
        RestException restException = new RestException(HttpStatus.SERVER_ERROR_INTERNAL, ErrorStatus.INTERNAL_ERROR.getErrorCode(), ErrorStatus.INTERNAL_ERROR.getResId());
        response.setStatus(restException.getHttpCode());
        return restException.getMsg();
    }


    private RestException build(ServiceException serviceException, HttpStatus httpStatus) {
        return new RestException(httpStatus, serviceException.getStatus().getErrorCode(), serviceException.getStatus().getResId());
    }


}
