package com.xylink.configserver.configuration;

import com.xylink.configserver.filter.RestApiContextAwareInterceptor;
import com.xylink.configserver.filter.SecurityFilter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Slf4j
@Configuration
public class WebFilterConfiguration {

    @Bean
    public WebMvcConfigurer webMvcConfigurer() {
        WebMvcConfigurer webMvcConfigurer = new WebMvcConfigurer() {
            @Override
            public void addInterceptors(InterceptorRegistry registry) {
                registry.addInterceptor(new RestApiContextAwareInterceptor()).addPathPatterns("/**");

            }
        };
        return webMvcConfigurer;
    }

    @Bean
    public FilterRegistrationBean registrationSecurityFilter(SecurityFilter securityFilter) {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(securityFilter);
        registration.addUrlPatterns("/*");
        registration.setOrder(1);
        return registration;
    }

    @Bean
    public SecurityFilter securityFilter(){
        return new SecurityFilter();
    }
}
