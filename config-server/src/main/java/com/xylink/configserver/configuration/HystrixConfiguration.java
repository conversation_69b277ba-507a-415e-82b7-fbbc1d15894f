package com.xylink.configserver.configuration;

import com.netflix.config.ConfigurationManager;
import com.netflix.hystrix.contrib.javanica.aop.aspectj.HystrixCommandAspect;
import org.apache.commons.configuration.AbstractConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HystrixConfiguration {

    @Bean
    public HystrixCommandAspect hystrixAspect() {
        AbstractConfiguration configInstance = ConfigurationManager.getConfigInstance();
        configInstance.setProperty("hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds", 2000);
        //configInstance.setProperty("hystrix.command.default.execution.isolation.thread.interruptOnTimeout", false);
        //configInstance.setProperty("hystrix.command.default.metrics.rollingPercentile.numBuckets", 60);
        return new HystrixCommandAspect();
    }

}