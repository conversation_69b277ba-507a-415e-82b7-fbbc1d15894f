package com.xylink.configserver.configuration.db;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.xylink.configserver.util.common.EnvironmentUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class MyBatisPlusConfiguration {

    /**
     * @description: 配置分页插件
     */
    @Bean
    public MybatisPlusInterceptor paginationInterceptor() {
        log.debug("注册分页插件");
        MybatisPlusInterceptor mybatisPlusInterceptor = new MybatisPlusInterceptor();

        DbType dbType;
        switch (EnvironmentUtil.getDbType()) {
            case MYSQL: {
                dbType = DbType.MYSQL;
                break;
            }
            case DM: {
                dbType = DbType.DM;
                break;
            }
            case JC: {
                dbType = DbType.KINGBASE_ES;
                break;
            }
            case ST: {
                dbType = DbType.OSCAR;
                break;
            }
            case PGSQL: {
                dbType = DbType.POSTGRE_SQL;
                break;
            }
            default: {
                dbType = DbType.MYSQL;
            }
        }
        mybatisPlusInterceptor.addInnerInterceptor(new PaginationInnerInterceptor(dbType));

        return mybatisPlusInterceptor;
    }

    @Bean
    public MetaObjectHandler MyMetaObjectHandler(){
        return new MyMetaObjectHandler();
    }

}