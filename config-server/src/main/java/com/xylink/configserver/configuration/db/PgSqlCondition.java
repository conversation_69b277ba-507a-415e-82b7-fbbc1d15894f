package com.xylink.configserver.configuration.db;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.util.StringUtils;

/**
 * ClassName:PgSqlCondition
 * Package:cn.binge.config
 * Description:
 *
 * <AUTHOR>
 * @Date 2023/7/25-16:19
 * @Version: v1.0
 */


@Slf4j
public class PgSqlCondition implements Condition {

    private static final String PG_DRIVER = "org.postgresql.Driver";

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        Environment env = context.getEnvironment();
        String driverName = env.getProperty("spring.datasource.dynamic.datasource.master.driver-class-name");

        if (StringUtils.hasText(driverName)) {
            return PG_DRIVER.equalsIgnoreCase(driverName.trim());
        }

        return false;
    }
}

