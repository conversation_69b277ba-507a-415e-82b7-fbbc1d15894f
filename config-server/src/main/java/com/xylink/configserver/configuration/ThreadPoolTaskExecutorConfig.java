package com.xylink.configserver.configuration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Author: bin
 * @Date: 2021/11/15 11:05
 * @Version: 1.0
 */
@Configuration
@Slf4j
public class ThreadPoolTaskExecutorConfig {

    @Bean(name = "threadPoolTaskExecutor")
    public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
        ThreadPoolTaskExecutor poolTaskExecutor = new ThreadPoolTaskExecutor();
        //最大线程数
        poolTaskExecutor.setMaxPoolSize(20);
        //核心线程数
        //线程数 = CPU可用核心数/(1 - 阻塞系数)  阻塞系数取0.5
        poolTaskExecutor.setCorePoolSize(6);
        //队列
        poolTaskExecutor.setQueueCapacity(20);
        //空闲时间
        poolTaskExecutor.setKeepAliveSeconds(60);
        //线程默认名称开头
        poolTaskExecutor.setThreadNamePrefix("batch exec thread");
        //拒绝策略
        poolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        poolTaskExecutor.initialize();
        return poolTaskExecutor;
    }

}
