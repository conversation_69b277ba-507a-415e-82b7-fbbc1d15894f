package com.xylink.configserver.configuration;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.RedisSentinelConfiguration;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import redis.clients.jedis.JedisPoolConfig;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Slf4j
@Configuration
public class RedisTemplateConfiguration {

    //access
    @Value("${spring.redis.access.host}")
    private String accessHost;

    @Value("${spring.redis.access.port}")
    private String accessPort;

    @Value("${spring.redis.access.username}")
    private String accessUsername;

    @Value("${spring.redis.access.password}")
    private String accessStandPassword;

    @Value("${spring.redis.access.sentinel.password}")
    private String accessSentinelPassword;

    @Value("${spring.redis.access.timeout}")
    private int accessTimeout;

    @Value("${spring.redis.access.sentinel.enable:false}")
    private boolean accessSentinelEnable;

    @Value("${spring.redis.access.sentinel.master}")
    private String accessSentinelMaster;

    @Value("#{'${spring.redis.access.sentinel.nodes}'.split(',')}")
    private List<String> accessSentinelNodes;

    //config
    @Value("${spring.redis.config.host}")
    private String configHost;

    @Value("${spring.redis.config.username}")
    private String configUsername;

    @Value("${spring.redis.config.port}")
    private String configPort;

    @Value("${spring.redis.config.password}")
    private String configStandPassword;
    @Value("${spring.redis.config.sentinel.password}")
    private String configSentinelPassword;

    @Value("${spring.redis.config.timeout}")
    private int configTimeout;

    @Value("${spring.redis.config.sentinel.enable:false}")
    private boolean configSentinelEnable;

    @Value("${spring.redis.config.sentinel.master}")
    private String configSentinelMaster;

    @Value("#{'${spring.redis.config.sentinel.nodes}'.split(',')}")
    private List<String> configSentinelNodes;

    //craft
    @Value("${spring.redis.craft.host}")
    private String craftHost;

    @Value("${spring.redis.craft.port}")
    private String craftPort;

    @Value("${spring.redis.craft.username}")
    private String craftUsername;

    @Value("${spring.redis.craft.password}")
    private String craftStandPassword;

    @Value("${spring.redis.craft.sentinel.password}")
    private String craftSentinelPassword;

    @Value("${spring.redis.craft.timeout}")
    private int craftTimeout;

    @Value("${spring.redis.craft.sentinel.enable:false}")
    private boolean craftSentinelEnable;

    @Value("${spring.redis.craft.sentinel.master}")
    private String craftSentinelMaster;

    @Value("#{'${spring.redis.craft.sentinel.nodes}'.split(',')}")
    private List<String> craftSentinelNodes;

    @Value("${xylink.redis.cluster.limit:false}")
    private boolean clusterUseLimit = false;

    // clusterHosts, clusterUser, clusterPwd, maxRedirects
    @Value("${xylink.redis.cluster.hosts}")
    private  String clusterHosts;
    @Value("${xylink.redis.cluster.user}")
    private String clusterUser;
    @Value("${xylink.redis.cluster.password}")
    private String clusterPwd;

    @Value("${xylink.redis.cluster.max-redirects}")
    private Integer maxRedirects;

    @Value("${xylink.redis.mode:single}")
    private String redisMode;

    private static final int MAX_IDLE = 200; //最大空闲连接数
    private static final int MAX_TOTAL = 1024; //最大连接数


    //配置工厂
    public JedisConnectionFactory connectionFactory(String host, int port, String userName, String standPwd, String sentinelPwd, int maxIdle,
                                                    int maxTotal, long maxWaitMillis, boolean enableSentinel, List<String> nodes, String master) {

        JedisClientConfiguration.JedisPoolingClientConfigurationBuilder jpcf = (JedisClientConfiguration.JedisPoolingClientConfigurationBuilder) JedisClientConfiguration.builder();
        jpcf.poolConfig(getPoolConfig(maxIdle, maxTotal, maxWaitMillis, false));
        JedisClientConfiguration jedisClientConfiguration = jpcf.build();

        JedisConnectionFactory jedisConnectionFactory;
        if (enableSentinel) {
            RedisSentinelConfiguration redisSentinelConfiguration = getRedisSentinelConfiguration(nodes, master, userName, sentinelPwd, standPwd);
            jedisConnectionFactory = new JedisConnectionFactory(redisSentinelConfiguration, jedisClientConfiguration);
        } else {
            RedisStandaloneConfiguration redisStandaloneConfiguration = getRedisStandaloneConfiguration(host, port, userName, standPwd);
            jedisConnectionFactory = new JedisConnectionFactory(redisStandaloneConfiguration, jedisClientConfiguration);
        }
        jedisConnectionFactory.afterPropertiesSet();
        return jedisConnectionFactory;
    }


    public JedisConnectionFactory connectionFactory(int maxIdle, int maxTotal, long maxWaitMillis,String clusterHosts,String clusterUserName,String clusterPwd,Integer maxRedirects) {
        JedisClientConfiguration.JedisPoolingClientConfigurationBuilder jpcf = (JedisClientConfiguration.JedisPoolingClientConfigurationBuilder) JedisClientConfiguration.builder();
        jpcf.poolConfig(getPoolConfig(maxIdle, maxTotal, maxWaitMillis, false));
        JedisClientConfiguration jedisClientConfiguration = jpcf.build();

        RedisClusterConfiguration redisClusterConfiguration = getRedisClusterConfiguration(clusterHosts, clusterUserName, clusterPwd, maxRedirects);

        JedisConnectionFactory jedisConnectionFactory = new JedisConnectionFactory(redisClusterConfiguration, jedisClientConfiguration);
        jedisConnectionFactory.afterPropertiesSet();
        return jedisConnectionFactory;
    }

    public JedisPoolConfig getPoolConfig(int maxIdle, int maxTotal, long maxWaitMillis, boolean testOnBorrow) {
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMaxTotal(maxTotal);
        poolConfig.setMaxWaitMillis(maxWaitMillis);
        poolConfig.setTestOnBorrow(testOnBorrow);
        return poolConfig;
    }

    public RedisStandaloneConfiguration getRedisStandaloneConfiguration(String host, int port, String userName, String password) {
        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration();
        redisStandaloneConfiguration.setHostName(host);
        redisStandaloneConfiguration.setPort(port);

        redisStandaloneConfiguration.setUsername(StringUtils.isBlank(userName) ? null : userName);
        redisStandaloneConfiguration.setPassword(password);
        return redisStandaloneConfiguration;
    }

    public RedisClusterConfiguration getRedisClusterConfiguration(String clusterHosts, String clusterUserName, String clusterPassword, Integer maxRedirects) {
        RedisClusterConfiguration redisClusterConfiguration = new RedisClusterConfiguration();
        Set<RedisNode> redisNodeSet = new HashSet<>();
        String[] hostArray = clusterHosts.split(",");
        for (String host : hostArray) {
            String[] hostAndPort = host.split(":");
            redisNodeSet.add(new RedisNode(hostAndPort[0], Integer.parseInt(hostAndPort[1])));
        }
        redisClusterConfiguration.setClusterNodes(redisNodeSet);
        redisClusterConfiguration.setMaxRedirects(maxRedirects);
        redisClusterConfiguration.setPassword(clusterPassword);
        if (StringUtils.isNotBlank(clusterUserName)){
            redisClusterConfiguration.setUsername(clusterUserName);
        }
        return redisClusterConfiguration;
    }

    public RedisSentinelConfiguration getRedisSentinelConfiguration(List<String> nodes, String master, String userName, String sentinelpwd,String redispwd) {
        RedisSentinelConfiguration redisSentinelConfiguration = new RedisSentinelConfiguration();
        redisSentinelConfiguration.master(master);
        Set<RedisNode> redisNodeSet = new HashSet<>();
        nodes.forEach(x -> redisNodeSet.add(new RedisNode(x.split(":")[0], Integer.parseInt(x.split(":")[1]))));
        log.info("redisNodeSet -->" + redisNodeSet);
        redisSentinelConfiguration.setSentinels(redisNodeSet);
        redisSentinelConfiguration.setUsername(StringUtils.isBlank(userName) ? null : userName);
        redisSentinelConfiguration.setSentinelPassword(sentinelpwd);
        redisSentinelConfiguration.setPassword(redispwd);
        return redisSentinelConfiguration;
    }


    @Bean(name = "redisAccessTemplate")
    public RedisTemplate redisAccessTemplate() {
        RedisTemplate template = new RedisTemplate();
        switch (redisMode){
            case "sentinel":
                clusterUseLimit = false;
                accessSentinelEnable = true;
                break;
            case "cluster":
                clusterUseLimit = true;
                accessSentinelEnable = false;
                break;
            default:
                clusterUseLimit = false;
                accessSentinelEnable = false;
        }
        if (clusterUseLimit){
            template.setConnectionFactory(connectionFactory(MAX_IDLE, MAX_TOTAL, accessTimeout, clusterHosts, clusterUser, clusterPwd, maxRedirects));
        }else {
            template.setConnectionFactory(
                    connectionFactory(accessHost, Integer.parseInt(accessPort), accessUsername, accessStandPassword, accessSentinelPassword, MAX_IDLE, MAX_TOTAL, accessTimeout, accessSentinelEnable, accessSentinelNodes, accessSentinelMaster));
        }
        template.afterPropertiesSet();
        return template;
    }

    @Bean(name = "redisConfigTemplate")
    public RedisTemplate redisConfigTemplate() {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        switch (redisMode){
            case "sentinel":
                clusterUseLimit = false;
                configSentinelEnable = true;
                break;
            case "cluster":
                clusterUseLimit = true;
                configSentinelEnable = false;
                break;
            default:
                clusterUseLimit = false;
                configSentinelEnable = false;
        }
        if (clusterUseLimit){
            template.setConnectionFactory(connectionFactory(MAX_IDLE, MAX_TOTAL, accessTimeout, clusterHosts, clusterUser, clusterPwd, maxRedirects));
        }else {
            template.setConnectionFactory(
                    connectionFactory(configHost, Integer.parseInt(configPort), configUsername, configStandPassword, configSentinelPassword, MAX_IDLE, MAX_TOTAL, configTimeout, configSentinelEnable, configSentinelNodes, configSentinelMaster));
        }
        Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        jackson2JsonRedisSerializer.setObjectMapper(om);
        RedisSerializer<String> stringSerializer = new StringRedisSerializer();
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);
        template.setValueSerializer(jackson2JsonRedisSerializer);
        template.setHashValueSerializer(jackson2JsonRedisSerializer);
        template.setDefaultSerializer(jackson2JsonRedisSerializer);
        return template;
    }

    @Bean(name = "redisCraftTemplate")
    public RedisTemplate redisCraftTemplate() {
        RedisTemplate template = new RedisTemplate();
        switch (redisMode){
            case "sentinel":
                clusterUseLimit = false;
                craftSentinelEnable = true;
                break;
            case "cluster":
                clusterUseLimit = true;
                craftSentinelEnable = false;
                break;
            default:
                clusterUseLimit = false;
                craftSentinelEnable = false;
        }
        if (clusterUseLimit){
            template.setConnectionFactory(connectionFactory(MAX_IDLE, MAX_TOTAL, accessTimeout, clusterHosts, clusterUser, clusterPwd, maxRedirects));
        }else{
            template.setConnectionFactory(
                    connectionFactory(craftHost, Integer.parseInt(craftPort), craftUsername, craftStandPassword, craftSentinelPassword, MAX_IDLE, MAX_TOTAL, craftTimeout, craftSentinelEnable, craftSentinelNodes, craftSentinelMaster));
        }
        template.afterPropertiesSet();
        return template;
    }
}
