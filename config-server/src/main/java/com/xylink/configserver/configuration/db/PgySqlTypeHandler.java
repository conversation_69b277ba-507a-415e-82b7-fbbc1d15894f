package com.xylink.configserver.configuration.db;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;


/*
 * <AUTHOR>
 * typeHandler 类型转换处理器
 * @date 2023/07/25
 *
 * */


@Conditional(PgSqlCondition.class)
@Component
public class PgySqlTypeHandler extends BaseTypeHandler<Boolean> {


    /*
     * parameter:bool类型的转换值
     * */
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Boolean parameter, JdbcType jdbcType) throws SQLException {
        //根据bool类型的值，转换为int类型的值
        ps.setInt(i, parameter ? 1 : 0);
    }

    @Override
    public Boolean getNullableResult(ResultSet rs, String columnName) throws SQLException {
        //resultTypeHandler处理结果,反处理
        int man = rs.getInt(columnName);
        return man == 1;
    }

    @Override
    public Boolean getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        int man = rs.getInt(columnIndex);
        return man == 1;
    }

    @Override
    public Boolean getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        int man = cs.getInt(columnIndex);
        return man == 1;
    }
}
