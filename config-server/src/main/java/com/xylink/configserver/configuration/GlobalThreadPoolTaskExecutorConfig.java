package com.xylink.configserver.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 */
@Configuration
public class GlobalThreadPoolTaskExecutorConfig {

    @Bean("sharedThreadPoolTaskExecutor")
    public ThreadPoolTaskExecutor globalThreadPoolTaskExecutor() {
        ThreadPoolTaskExecutor poolTaskExecutor = new ThreadPoolTaskExecutor();
        //最大线程数
        poolTaskExecutor.setMaxPoolSize(5);
        //核心线程数
        poolTaskExecutor.setCorePoolSize(5);
        //队列
        poolTaskExecutor.setQueueCapacity(5000);
        //空闲时间
        poolTaskExecutor.setKeepAliveSeconds(300);
        //线程默认名称开头
        poolTaskExecutor.setThreadNamePrefix("globalThread-");
        //拒绝策略
        poolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        poolTaskExecutor.initialize();
        return poolTaskExecutor;

    }

}