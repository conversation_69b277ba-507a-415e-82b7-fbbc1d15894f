package com.xylink.configserver.filter;

import com.xylink.configserver.service.LoginService;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Pattern;

public class SecurityFilter implements Filter {

    @Autowired
    private LoginService loginService;

    private String urlPatternNotSecure;

    private final AtomicReference<Pattern> pattern = new AtomicReference<>();

    @Override
    public void destroy() {
        // don't override
    }

    @Override
    public void doFilter(ServletRequest req, ServletResponse resp, FilterChain chain)
            throws IOException,ServletException {

        String securityKey = req.getParameter("securityKey");
        String deviceTypeStr = req.getParameter("deviceType");
        int deviceType = deviceTypeStr != null ? Integer.parseInt(deviceTypeStr) : Integer.MIN_VALUE;

        HttpServletRequest request = (HttpServletRequest)req;
        String path = request.getRequestURI();
        if (getPattern().matcher(path).matches()){
            chain.doFilter(req, resp);
            return;
        }
        if (loginService.isValidSecurityKey(securityKey, deviceType)) {
            chain.doFilter(req, resp);
        }else {
            ((HttpServletResponse) resp).sendError(401);
        }
    }

    @Override
    public void init(FilterConfig arg0) {
        urlPatternNotSecure = "(" +

                // 通用规则
                // 未避免该列表规则爆炸式增长,影响可读性与性能,请优先考虑通用匹配规则
                // 1、某些特殊外部接口缺失sk，请以/external/unsafe为统一匹配规则
                "/api/rest/clientConfg/external/unsafe/.*|" +

                // 2、内部接口无需校验
                "/api/rest/internal/.*|" +
                "/api/rest/clientConfg/internal/.*|" +

                // 自定义规则
                // 针对某个或某类接口定义，通常标识一类资源
                "/api/rest/v(\\d)/.*/clientcustomization|" +
                "/api/rest/serverConfig/init|" +
                "/api/rest/v(\\d)/.*/device/screensaver|" +
                "/api/rest/clientConfg/v(\\d)/getDefaultServerConfig|" +
                "/api/rest/clientConfg/wx/v(\\d)/getDefaultServerConfig|" +
                "/api/rest/health|" +
                "/api/rest/v2/api-docs|" +
                "/api/rest/internal/inspection/v1|" +
                "/api/rest/clientConfg/external/serverConfig/default/v1|"+
                "/api/rest/clientConfg/external/nemo/rootCert/list|"+
                "/api/rest/clientConfg/external/cloud/config/before|"+
                "/api/rest/clientConfg/external/nemo/root/cert"
        + ")";
    }


    private Pattern getPattern(){
        Pattern p = pattern.get();
        if(p == null){
            p = Pattern.compile(urlPatternNotSecure);
            pattern.compareAndSet(null, p);
            p = pattern.get();
        }
        return p;
    }
}