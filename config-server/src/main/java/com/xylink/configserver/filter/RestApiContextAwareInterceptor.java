package com.xylink.configserver.filter;

import com.xylink.configserver.data.model.AppDetailInfo;
import com.xylink.configserver.util.AppUAParser;
import com.xylink.configserver.util.RestApiContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@Component
public class RestApiContextAwareInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        AppDetailInfo appUAInfo = AppUAParser.parseAppInfo(request.getHeader("n-ua"));
        appUAInfo.setNginxIp(request.getHeader("x-nginx-ip"));
        appUAInfo.setRemoteIp(request.getHeader("X-Real-IP"));
        RestApiContext.setCurrentClientInfo(appUAInfo);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        RestApiContext.removeCurrentClientInfo();
        RestApiContext.removeCurrentDevice();
        RestApiContext.removeCurrentDeviceType();
    }
}
