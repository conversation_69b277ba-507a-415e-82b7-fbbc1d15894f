package com.xylink.configserver.mapper.specialfeatue;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.NemoFeature;
import com.xylink.configserver.data.model.SpecialNemoCustomizeFeature;
import com.xylink.configserver.data.model.specialfeature.SpecialFeatureEntity;
import com.xylink.configserver.data.model.specialfeature.SpecialFeatureNemoEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SpecialFeatureMapper extends BaseMapper<SpecialFeatureEntity> {

    List<SpecialFeatureEntity> getNemoFeatures(@Param("nemoSN") String nemoSN, @Param("appVersion") int appVersion, @Param("type") int type);

    SpecialFeatureEntity getFeatureById(String featureId);

    SpecialFeatureNemoEntity getSpecialNemo(@Param("featureId") String featureId, @Param("nemoSN") String nemoSN, @Param("checkStatus") boolean checkStatus);

    String addSpecialFeatureNemo(@Param("nemo") SpecialFeatureNemoEntity nemo);

    List<SpecialNemoCustomizeFeature> getBySpecialFeatureId(String featureId);

    NemoFeature getNemoFeature(@Param("nemoSN") String nemoSN, @Param("featureId") String featureId);

    void updateNemoFeature(@Param("feature") NemoFeature nemoFeature);

    void addNemoFeature(@Param("feature") NemoFeature nemoFeature);

    void deleteNemoSpecialFeature(@Param("feature") SpecialFeatureNemoEntity nemo);
}
