package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.DeptSpecialConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/4 10:59
 */
@Mapper
public interface SpecialFeatureDeptMapper extends BaseMapper<DeptSpecialConfigEntity> {

    @Select("<script>" +
            "select dept.dept_id, dept.feature_id, client_config_name, config_name, config_value\n" +
            "from ainemo.libra_special_feature_department as dept\n" +
            "         left join ainemo.libra_special_feature as feature on feature.id = dept.feature_id\n" +
            "         left join ainemo.libra_special_feature_config as config on feature.id = config.special_feature_id\n" +
            "where dept_id in " +
            "<foreach item='deptId' index='index' collection='deptIds' open='(' separator=',' close=')'>" +
            "#{deptId}" +
            "</foreach>" +
            "</script>")
    List<DeptSpecialConfigEntity> selectByDeptIds(@Param("deptIds") List<Long> deptIds);

}
