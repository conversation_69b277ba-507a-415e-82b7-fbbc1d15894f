package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.CustomizeFeature;
import com.xylink.configserver.data.model.ThirdAppFeature;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ThirdAppFeatureMapper extends BaseMapper<ThirdAppFeature> {

    List<ThirdAppFeature> getAll();

    CustomizeFeature getCustomizeFeatureById(String id);
}
