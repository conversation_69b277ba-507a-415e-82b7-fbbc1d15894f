package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.DefaultServerConfig;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Map;

@Repository
public interface EnterpriseServersConfigMapper extends BaseMapper<DefaultServerConfig> {

    @MapKey("configName")
    Map<String, DefaultServerConfig> getDefaultServerConfig();

    @MapKey("configName")
    Map<String, DefaultServerConfig> getDefaultServerConfigByConfigName(String configName);

    @MapKey("configName")
    Map<String, DefaultServerConfig> getDeviceTypeServerConfigs(int subType);

    @MapKey("configName")
    Map<String, DefaultServerConfig> getEnterpriseServerConfigs(String enterpriseId);

    @MapKey("configName")
    Map<String, DefaultServerConfig> getDeviceServerConfig(Long deviceId);

    void addDefaultServerConfig(@Param("params") Map<String, Object> serverconfigs);

    DefaultServerConfig getDefaultServerConfigPoByConfigName(String configName);

}
