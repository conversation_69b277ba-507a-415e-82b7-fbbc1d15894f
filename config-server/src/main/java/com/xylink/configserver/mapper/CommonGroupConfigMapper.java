package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.CommonGroupConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommonGroupConfigMapper extends BaseMapper<CommonGroupConfig> {

    /**
     * 根据 clientConfigName 和 分组 获取配置
     */
    List<CommonGroupConfig> getGroupConfigsByType(@Param("configGroup") String configGroup, @Param("configType") int configType);

    void insertList(@Param("commonGroupConfigs") List<CommonGroupConfig> commonGroupConfigs);
}
