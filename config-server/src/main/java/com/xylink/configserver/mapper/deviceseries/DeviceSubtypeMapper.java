package com.xylink.configserver.mapper.deviceseries;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xylink.configserver.data.model.DeviceSubtypeModelV2;
import com.xylink.configserver.data.model.deviceseries.DeviceSubtypeDto;
import com.xylink.configserver.data.model.deviceseries.DeviceSubtypeListDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2021-01-18 16:00
 */
@Mapper
public interface DeviceSubtypeMapper extends BaseMapper<DeviceSubtypeModelV2> {
    /**
     * 分页查询数据
     *
     * @param page
     * @param key
     * @return
     */
    Page<DeviceSubtypeListDto> page(Page<DeviceSubtypeListDto> page, @Param("key") String key);

    /**
     * 终端详情查询
     *
     * @param subtype
     * @return
     */
    DeviceSubtypeDto getBySubtype(int subtype);

    /**
     * 修改管理平台显示型号名称
     *
     * @param categoryDisplay
     * @param subtype
     * @return
     */
    void updateSubtypeCategoryDisplayName(@Param("categoryDisplay") String categoryDisplay, @Param("subtype") int subtype);
}
