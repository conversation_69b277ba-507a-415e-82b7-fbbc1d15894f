package com.xylink.configserver.mapper.specialfeatue;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.specialfeature.SpecialFeatureConfigEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SpecialFeatureConfigMapper extends BaseMapper<SpecialFeatureConfigEntity> {

    List<SpecialFeatureConfigEntity> getSpecialConfig(String deviceSN);

    List<SpecialFeatureConfigEntity> getSpecialConfigByUserId(long userProfileId);

    List<SpecialFeatureConfigEntity> getSpecialClientConfig(@Param("deviceSN") String deviceSN, @Param("clientConfigName") String clientConfigName);

    SpecialFeatureConfigEntity getDeviceSpecialConfig(@Param("nemoSN") String nemoSN, @Param("configName") String configName);

    List<SpecialFeatureConfigEntity> getSpecialConfigByConfigNameAndClientName(@Param("deviceSN") String deviceSN, @Param("configName") String configName, @Param("clientConfigName") String clientConfigName, @Param("specialFeatureId") String specialFeatureId);

    List<SpecialFeatureConfigEntity> getSpecialConfigsByFeatureById(String featureId);

}
