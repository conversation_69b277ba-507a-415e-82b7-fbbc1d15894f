package com.xylink.configserver.mapper.deviceseries;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigDictionaryDataEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-02-05 15:21
 */
@Mapper
public interface DeviceSubtypeSeriesConfigDictionaryDataMapper extends BaseMapper<DeviceSubtypeSeriesConfigDictionaryDataEntity> {
    /**
     * 获取配置项
     *
     * @param configId
     * @param special
     * @return
     */
    List<DeviceSubtypeSeriesConfigDictionaryDataEntity> getByConfigIdAndSpecial(@Param("configId") long configId, @Param("special") int special);

    /**
     * 获取系列的配置项
     *
     * @param seriesId
     * @return
     */
    List<DeviceSubtypeSeriesConfigDictionaryDataEntity> getBySeriesId(@Param("seriesId") long seriesId);
}
