package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.DeptConfigPO;
import com.xylink.configserver.util.CollectionUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.*;

@Mapper
public interface DeptConfigMapper extends BaseMapper<DeptConfigPO> {

    default DeptConfigPO selectConfig(DeptConfigPO deptConfigPO){
        List<DeptConfigPO> deptConfigPOList = this.selectByMap(CollectionUtil.newMap(
                CollectionUtil.kv("enterprise_id", deptConfigPO.getEnterpriseId()),
                CollectionUtil.kv("dept_id", deptConfigPO.getDeptId()),
                CollectionUtil.kv("client_config_name", deptConfigPO.getClientConfigName()),
                CollectionUtil.kv("config_name", deptConfigPO.getConfigName()),
                CollectionUtil.kv("config_type", deptConfigPO.getConfigType())
        ));
        return CollectionUtil.isNotEmpty(deptConfigPOList) ? deptConfigPOList.get(0) : null;
    }

    default List<DeptConfigPO> getDeptConfig(String enterpriseId, String deptId){
        Map<String, Object> paramMap = CollectionUtil.newMap(CollectionUtil.kv("enterprise_id", enterpriseId));
        if (StringUtils.isNotBlank(deptId)){
            paramMap.put("dept_id", deptId);
        }
        List<DeptConfigPO> deptConfigPOList = this.selectByMap(paramMap);
        return deptConfigPOList;
    }

    default void saveOrUpdate(DeptConfigPO configPO){
        long currentTimeMillis = System.currentTimeMillis();
        DeptConfigPO selectConfigPO = selectConfig(configPO);
        if (Objects.isNull(selectConfigPO)){
            configPO.setCreateTime(currentTimeMillis);
            configPO.setUpdateTime(currentTimeMillis);
            this.insert(configPO);
        } else {
            this.updateConfigValueById(configPO.getConfigValue(), selectConfigPO.getId(), currentTimeMillis);
        }
    }

    /** 根据id修改配置值 */
    int updateConfigValueById(@Param("configValue") String configValue, @Param("id") Long id, @Param("updateTime") Long updateTime);

    /** 根据部门id删除配置 */
    default int deleteByDeptId(String enterpriseId, String deptId, String configName){
        Map<String, Object> paramMap = CollectionUtil.newMap(CollectionUtil.kv("dept_id", deptId), CollectionUtil.kv("enterprise_id", enterpriseId));
        if (StringUtils.isNotBlank(configName)){
            paramMap.put("config_name", configName);
        }
        return this.deleteByMap(paramMap);
    }


    List<DeptConfigPO> findConfigListByDeptIdList(@Param("enterpriseId") String enterpriseId,
                                                  @Param("deptIdList") List<String> deptIdList,
                                                  @Param("configName") String configName);
}
