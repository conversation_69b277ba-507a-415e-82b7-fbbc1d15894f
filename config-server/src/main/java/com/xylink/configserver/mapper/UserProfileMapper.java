package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xylink.configserver.data.model.UserProfileModel;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface UserProfileMapper extends BaseMapper<UserProfileModel> {

    /**
     * 通过用户id查询用户企业id
     */
    default UserProfileModel getUserProfileByProfileId(@Param("userProfileId") long userProfileId) {
        return selectById(userProfileId);
    }

    default List<UserProfileModel> getUserProfileByUserPhones(@Param("userPhones")List<String> userPhones) {
        if (CollectionUtils.isEmpty(userPhones)) {
            return new ArrayList<>(1);
        }
        return selectList(Wrappers.<UserProfileModel>lambdaQuery()
                                  .in(UserProfileModel::getUserPhone, userPhones));
    }

}
