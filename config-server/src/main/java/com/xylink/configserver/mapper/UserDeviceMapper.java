package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xylink.configserver.data.model.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface UserDeviceMapper extends BaseMapper<UserDevice> {

    Long getGWDeviceExpiredTimestamp(@Param("securityKey") String securityKey);

    UserDevice getUserDeviceBySk(String securityKey);

    List<String> getEnterpriseNemoBySn(String nemoSN);

    List<String> getEnBoxPOBySn(String nemoSN);

    Long getNemoFirstBindTime(String deviceSN);

    String getUserProfileEnterpriseByUserId(long userProfileId);

    UserDevice getUserDeviceByNemoId(long nemoId);

    List<Long> getCommunityUserIds(long id);

    long getNemoNumberByDeviceId(long deviceId);

    List<UserDevice> getUserDeviceByDevSn(String deviceSn);

    List<GwDevice> getGwDeviceBySkAndType(@Param("sk") String sk, @Param("name") String name);

    List<UserDevice> getInUseUserDeviceByDevSnAndTypes(@Param("deviceSn") String deviceSn, @Param("types") String types);

    List<UserDevice> getUserDeviceByUserId(long userId, int type);

    List<UserDevice>getUserProfile2Device(long userId);

    List<UserProfile> getUserProfileByEnterpriseId(String enterpriseId);

    List<UserProfile> getUserProfileIds(@Param("telphones")String telphones);
    List<UserDevice> getDeviceIds(@Param("numbers")String numbers);

    UserPO getUserById(String userId);

    void updateUserPasswordExpireTimeById(@Param("id") String id, @Param("passwordExpireTime") long passwordExpireTime);

    List<UserDevice> getEnterpriseDevices(@Param("enterpriseId") String enterpriseId, @Param("deviceType") int deviceType);


    Set<UserDevice> getEnterpriseNemos(String enterpriseProfileId);

    List<UserDevice>getEnterpriseProfileNemosByType(@Param("enterpriseProfileId") String enterpriseProfileId,@Param("deviceTypeOrSubType")Integer deviceTypeOrSubType,@Param("startId")  Integer startId, @Param("limit") Integer limit);

    List<UserDevice>getEnterpriseProfileNemosBySubType(@Param("enterpriseProfileId") String enterpriseProfileId,@Param("deviceTypeOrSubType")Integer deviceTypeOrSubType,@Param("startId")  Integer startId, @Param("limit") Integer limit);

    default List<UserDevice> selectByIds(List<Long> did) {
        return selectList(Wrappers.<UserDevice>lambdaQuery().in(UserDevice::getId, did));
    }

    List<UserDevice> batchGetDeviceListByIdList(@Param("deviceIdList") List<String> deviceIdList);
}
