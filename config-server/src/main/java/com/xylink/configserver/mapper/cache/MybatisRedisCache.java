package com.xylink.configserver.mapper.cache;

import com.xylink.configserver.util.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.cache.Cache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisServerCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;

import java.util.Set;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

@Slf4j
public class MybatisRedisCache implements Cache {


    // 读写锁
    private final ReadWriteLock readWriteLock = new ReentrantReadWriteLock(true);

    //这里使用了redis缓存，使用springboot自动注入
    @Autowired
    private RedisTemplate<String, Object> redisConfigTemplate;

    private String id;

    public MybatisRedisCache(final String id) {
        if (id == null) {
            throw new IllegalArgumentException("Cache instances require an ID");
        }
        this.id = id;
    }

    @Override
    public String getId() {
        return this.id;
    }

    @Override
    public void putObject(Object key, Object value) {
        try {
            if (redisConfigTemplate == null) {
                redisConfigTemplate = (RedisTemplate<String, Object>) SpringContextUtil.getBean("redisConfigTemplate");
            }
            if (value != null) {
                redisConfigTemplate.opsForValue().set(key.toString(), value);
            }
        }catch (Exception e){
            log.error("cache mybatis object error",e);
        }

    }

    @Override
    public Object getObject(Object key) {
        try {
            if (redisConfigTemplate == null) {
                redisConfigTemplate = (RedisTemplate<String, Object>) SpringContextUtil.getBean("redisConfigTemplate");
            }
            if (key != null) {
                return redisConfigTemplate.opsForValue().get(key.toString());
            }
        } catch (Exception e) {
            log.error("缓存出错 ");
        }
        return null;
    }

    @Override
    public Object removeObject(Object key) {
        try {
            if (redisConfigTemplate == null) {
                redisConfigTemplate = (RedisTemplate<String, Object>) SpringContextUtil.getBean("redisConfigTemplate");
            }
            if (key != null) {
                redisConfigTemplate.delete(key.toString());
            }
        }catch (Exception e){
            log.error("remove mybatis cache error",e);
        }

        return null;
    }

    @Override
    public void clear() {
        log.debug("清空缓存");
        try {
            if (redisConfigTemplate == null) {
                redisConfigTemplate = (RedisTemplate<String, Object>) SpringContextUtil.getBean("redisConfigTemplate");
            }
            Set<String> keys = redisConfigTemplate.keys("*:" + this.id + "*");
            if (!CollectionUtils.isEmpty(keys)) {
                redisConfigTemplate.delete(keys);
            }
        }catch (Exception e){
            log.error("clear mybatis cache error",e);
        }

    }

    @Override
    public int getSize() {
        try {
            if (redisConfigTemplate == null) {
                redisConfigTemplate = (RedisTemplate<String, Object>) SpringContextUtil.getBean("redisConfigTemplate");
            }
            Long size = redisConfigTemplate.execute((RedisCallback<Long>) RedisServerCommands::dbSize);
            return size.intValue();
        }catch (Exception e){
            log.error("get mybatis cache size error",e);
        }
        return 0;
    }

    @Override
    public ReadWriteLock getReadWriteLock() {
        return this.readWriteLock;
    }
}
