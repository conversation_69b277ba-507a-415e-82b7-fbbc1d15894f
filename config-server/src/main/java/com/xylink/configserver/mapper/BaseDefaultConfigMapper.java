package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.NemoBaseDefaultConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BaseDefaultConfigMapper extends BaseMapper<NemoBaseDefaultConfig> {

    List<NemoBaseDefaultConfig> getAllBaseDefaultConfig();

    List<NemoBaseDefaultConfig> selectListByCondition(@Param("configType") Integer configType, @Param("clientConfigName") String clientConfigName, @Param("configName") String configName);

    void insertList(@Param("nemoBaseDefaultConfigs") List<NemoBaseDefaultConfig> nemoBaseDefaultConfigs);
}