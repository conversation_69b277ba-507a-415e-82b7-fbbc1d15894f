package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xylink.configserver.data.model.GwDeviceModel;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @since 2023/10/20 13:04
 */
@Mapper
public interface GwDeviceMapper extends BaseMapper<GwDeviceModel> {

    default GwDeviceModel selectByNumber(Long number) {
        return selectOne(Wrappers.<GwDeviceModel>lambdaQuery()
                .eq(GwDeviceModel::getNumber, number)
                .last("limit 1"));
    }

}
