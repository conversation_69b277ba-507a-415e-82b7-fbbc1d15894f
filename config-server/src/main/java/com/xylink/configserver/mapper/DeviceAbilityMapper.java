package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xylink.configserver.data.model.DeviceAbilityModel;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022/7/20 16:50
 */
@Mapper
public interface DeviceAbilityMapper extends BaseMapper<DeviceAbilityModel> {

    default List<DeviceAbilityModel> selectLimit(long deviceId, int limit) {
        return selectList(Wrappers.<DeviceAbilityModel>lambdaQuery()
                .orderByAsc(DeviceAbilityModel::getDeviceId)
                .gt(DeviceAbilityModel::getDeviceId, deviceId)
                .last("limit " + limit));
    }

}