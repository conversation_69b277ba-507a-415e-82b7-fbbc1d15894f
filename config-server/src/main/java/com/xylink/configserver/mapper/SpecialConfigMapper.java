package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.SpecialConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SpecialConfigMapper extends BaseMapper<SpecialConfig>{

    List<SpecialConfig> getSpecialConfig(String deviceSN);

    List<SpecialConfig> getSpecialConfigByUserId(long userProfileId);

    List<SpecialConfig> getSpecialClientConfig(@Param("deviceSN") String deviceSN, @Param("clientConfigName") String clientConfigName);

    SpecialConfig getDeviceSpecialConfig(@Param("nemoSN") String nemoSN, @Param("configName") String configName);

    List<SpecialConfig> getSpecialConfigByConfigNameAndClientName(@Param("deviceSN") String deviceSN, @Param("configName") String configName, @Param("clientConfigName") String clientConfigName, @Param("specialFeatureId") String specialFeatureId);

    List<SpecialConfig> getSpecialConfigsByFeatureById(String featureId);

    List<SpecialConfig> getSpecialConfigByCondition(@Param("deviceSN") String deviceSN, @Param("userProfileId") String userProfileId, @Param("clientConfigName") String clientConfigName);
}
