package com.xylink.configserver.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.NemoDefaultBrandConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface NemoDefaultBrandConfigMapper  extends BaseMapper<NemoDefaultBrandConfig> {

    List<NemoDefaultBrandConfig> getDefaultBrandConfigByBrandAndType(@Param("brand") String brand,@Param("type") int type,@Param("brandModel") String brandModel);

    Map<String, String> getDefaultBrandConfigAll();

    NemoDefaultBrandConfig getDefaultBrandConfigUnique(@Param(value="config") NemoDefaultBrandConfig defaultBrandConfig);

    void updateBrandConfig(@Param(value="config") NemoDefaultBrandConfig defaultBrandConfig);

    void saveBrandConfig(@Param(value="config") NemoDefaultBrandConfig defaultBrandConfig);

    void deleteBrandConfig(long id);
}
