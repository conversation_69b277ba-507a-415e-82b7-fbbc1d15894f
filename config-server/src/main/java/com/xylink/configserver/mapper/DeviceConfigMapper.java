package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.BaseConfig;
import com.xylink.configserver.data.model.DeviceConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DeviceConfigMapper extends BaseMapper<DeviceConfig> {

    List<BaseConfig> getDeviceConfig(long deviceId);

    List<DeviceConfig> getDeviceClientConfigs(@Param("deviceId") long deviceId, @Param("clientConfigName") String clientConfigName);

    List<DeviceConfig> getNemoConfig(@Param("deviceId") long deviceId, @Param("configName") String configName);

    List<DeviceConfig> getNemoConfigByConfigNameAndClientConfigName(@Param("deviceId") long deviceId, @Param("configName") String configName, @Param("clientConfigName") String clientConfigName);

    void addDeviceConfig(@Param(value="config") DeviceConfig config);

    DeviceConfig getClientConfig(@Param("nemoId")long nemoId, @Param("configName")String configName, @Param("clientConfigName")String clientConfigName);

    void updateDeviceConfigById(@Param(value="config") DeviceConfig deviceConfig);

    void deleteUserNemoRelation(long nemoId);

    void addUserNemoRelation(@Param("userId") long userId, @Param("nemoId") long nemoId);

    void deleteDeviceConfigById(long id);

    List<BaseConfig> getDeviceConfigByCondition(@Param("id") long id, @Param("clientConfigName") String clientConfigName);

    void deleteDeviceConfigByConfigName(@Param("nemoId") long nemoId, @Param("clientConfigName") String clientConfigName, @Param("configName") String configName);
}
