package com.xylink.configserver.mapper.specialfeatue;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xylink.configserver.data.model.specialfeature.SpecialFeatureNemoEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/28 11:07
 */
@Mapper
public interface SpecialFeatureDeviceMapper extends BaseMapper<SpecialFeatureNemoEntity> {

    default List<SpecialFeatureNemoEntity> selectDeptFeature(List<Long> deptIds) {
        List<String> deptIdsStr = new ArrayList<>(deptIds.size());
        for (Long deptId : deptIds) {
            deptIdsStr.add(deptId.toString());
        }
        return selectList(Wrappers.<SpecialFeatureNemoEntity>lambdaQuery()
                .in(SpecialFeatureNemoEntity::getNemoSn, deptIdsStr)
                .eq(SpecialFeatureNemoEntity::getDataType, 1));
    }

}
