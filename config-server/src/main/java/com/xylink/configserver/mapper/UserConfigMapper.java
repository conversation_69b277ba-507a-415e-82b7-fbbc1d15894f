package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.UserConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserConfigMapper extends BaseMapper<UserConfig> {

    List<UserConfig> getUserConfig(@Param("userProfileId") long userProfileId, @Param("deviceType") int deviceType);

    List<UserConfig> getUserConfigs(@Param("userId") long userId, @Param("configName") String configName,
                                    @Param("deviceType") int deviceType, @Param("clientConfigName") String clientConfigName);

    void addUserConfig(@Param(value="config") UserConfig userConfigPO);

    void updateUserConfig(@Param(value="config") UserConfig userConfigPO);
}
