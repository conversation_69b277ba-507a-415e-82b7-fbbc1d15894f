package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.NemoFeature;
import com.xylink.configserver.data.model.SpecialFeatureNemo;
import com.xylink.configserver.data.model.SpecialNemoCustomizeFeature;
import com.xylink.configserver.data.model.SpecialNemoFeature;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SpecialNemoFeatureMapper extends BaseMapper<SpecialNemoFeature> {

    List<SpecialNemoFeature> getNemoFeatures(@Param("nemoSN") String nemoSN, @Param("appVersion") int appVersion, @Param("type") int type);

    SpecialNemoFeature getFeatureById(String featureId);

    SpecialFeatureNemo getSpecialNemo(@Param("featureId") String featureId, @Param("nemoSN") String nemoSN, @Param("checkStatus") boolean checkStatus);

    String addSpecialFeatureNemo(@Param("nemo") SpecialFeatureNemo nemo);

    List<SpecialNemoCustomizeFeature> getBySpecialFeatureId(String featureId);

    NemoFeature getNemoFeature(@Param("nemoSN") String nemoSN, @Param("featureId") String featureId);

    void updateNemoFeature(@Param("feature") NemoFeature nemoFeature);

    void addNemoFeature(@Param("feature") NemoFeature nemoFeature);

    void deleteNemoSpecialFeature(@Param("feature") SpecialFeatureNemo nemo);
}
