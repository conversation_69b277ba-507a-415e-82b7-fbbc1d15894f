package com.xylink.configserver.mapper;

import com.xylink.configserver.data.model.EnterpriseNemoFeature;
import com.xylink.configserver.data.model.EnterpriseNemoProfile;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnterpriseNemoProfileMapper {

    EnterpriseNemoProfile getEnterpriseProfileByEnterpriseId(String enterpriseId);

    void removeEnterpriseNemoProfileConfig(@Param("profileId") String profileId, @Param("configName") String configName, @Param("deviceSubType") int deviceSubType, @Param("clientConfigName") String clientConfigName);

    int getEnterpriseProfileHardCount(@Param("profileId") String profileId,@Param("deviceType") Integer deviceType);

    int getEnterpriseProfileDeviceBySubCount(@Param("profileId") String profileId,@Param("deviceSubType") Integer deviceSubType);

    void updateEnterpriseNemoProfile(@Param("profile") EnterpriseNemoProfile profile);

    void updateEnterpriseNemoProfileSome(@Param("profile") EnterpriseNemoProfile profile);

    void addEnterpriseNemoProfile(@Param("profile") EnterpriseNemoProfile profile);

    EnterpriseNemoProfile getEnterpriseProfileByProfileId(String profileId);

    List<EnterpriseNemoFeature> listByProfileId(String profileId);

    void updateEnterpriseNemoFeature(@Param("po") EnterpriseNemoFeature po);

    EnterpriseNemoFeature getEnterpriseNemoFeatures(@Param("featureId") String featureId, @Param("profileId") String profileId);

    void addEnterpriseNemoFeature(@Param("feature") EnterpriseNemoFeature feature);

    EnterpriseNemoProfile getEnterpriseProfileByCustomizedKey(String customizedKey);

}
