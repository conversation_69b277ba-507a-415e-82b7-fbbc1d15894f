package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.UserDeviceEntity;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

@Mapper
public interface NewUserDeviceServiceMapper extends BaseMapper<UserDeviceEntity> {

    @Select("${sql}")
    List<Map<String, Object>> selectCondition(@Param("sql") String condition);
}