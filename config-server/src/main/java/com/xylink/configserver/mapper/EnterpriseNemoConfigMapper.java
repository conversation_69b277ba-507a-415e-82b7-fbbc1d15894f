package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.EnterpriseNemoConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface EnterpriseNemoConfigMapper extends BaseMapper<EnterpriseNemoConfig> {

    List<EnterpriseNemoConfig> getEnterpriseNemoConfigByProfileId(@Param("profileId") String profileId);

    List<EnterpriseNemoConfig> getEnterpriseProfileConfig(@Param("enterpriseId") String enterpriseId, @Param("type") int type);

    List<EnterpriseNemoConfig> getEnterpriseDeviceConfigs(@Param("deviceSN") String deviceSN, @Param("type") int type);

    List<EnterpriseNemoConfig> getEnterpriseDeviceConfigByEnterpriseId(@Param("enterpriseId") String enterpriseId, @Param("type") int type);

    List<EnterpriseNemoConfig> getEnterpriseDeviceClientConfig(@Param("deviceSN") String deviceSN, @Param("clientConfigName")  String clientConfigName);

    EnterpriseNemoConfig getEnterpriseDeviceConfig(@Param("deviceSN") String deviceSN, @Param("configName") String configName, @Param("deviceType") int deviceType);

    List<EnterpriseNemoConfig> getEntDeviceConfigBySubType(@Param("enterpriseId") String enterpriseId, @Param("configName") String configName, @Param("subType") int subType);

    List<EnterpriseNemoConfig> getEntDeviceConfigByType(@Param("enterpriseId") String enterpriseId, @Param("configName") String configName, @Param("type") int type);

    List<EnterpriseNemoConfig> getEnterpriseCustomizedClientConfig(@Param("customizedKey") String customizedKey, @Param("configType") int configType, @Param("uiDisplayCustomization") String uiDisplayCustomization);

    List<EnterpriseNemoConfig> getProfileConfigsByProfileId(String profileId);

    Set<EnterpriseNemoConfig> getEnterpriseNemoConfigsByProfileId(String profileId);

    void saveOrUpdateEnterpriseNemoConfig(@Param(value="config")EnterpriseNemoConfig config);

    List<EnterpriseNemoConfig> getProfileConfigsByProfileIdAndConfigName(@Param("profileId") String profileId,@Param("configName") String configName);

}
