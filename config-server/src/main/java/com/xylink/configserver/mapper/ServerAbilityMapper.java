package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.ServerAbilityEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author: bin
 * @Date: 2021/11/18 17:28
 * @Version: 1.0
 */
@Mapper
public interface ServerAbilityMapper extends BaseMapper<ServerAbilityEntity> {
    /**
     * 查询分组值
     *
     * @return 返回不重复的分组值
     */
    List<Integer> selectCategoryNoDistinct();


    /**
     * 查询分组值
     *
     * @param category 当前分组
     * @return 分组十进制值的和
     */
    Long selectAndSumDecimalValue(@Param("category") int category);
}