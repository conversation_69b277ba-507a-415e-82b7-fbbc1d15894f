package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.NemoDefaultPlatformConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-05-06 17:22
 */
@Repository
public interface NemoDefaultPlatformMapper extends BaseMapper<NemoDefaultPlatformConfig> {
    /**
     * 根据条件获取唯一配置
     *
     * @param configPlatform
     * @param configName
     * @param clientConfigName
     * @param configType
     * @return
     */
    NemoDefaultPlatformConfig getUniqPlatformConfig(@Param("configPlatform") String configPlatform, @Param("configName") String configName, @Param("clientConfigName") String clientConfigName, @Param("configType") int configType);

    /**
     * 根据条件查询
     *
     * @param configPlatform
     * @param configType
     * @return
     */
    List<NemoDefaultPlatformConfig> getByPlatformAndConfigType(@Param("configPlatform") String configPlatform, @Param("configType") int configType);
}
