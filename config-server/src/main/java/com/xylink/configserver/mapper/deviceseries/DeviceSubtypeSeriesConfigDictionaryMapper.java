package com.xylink.configserver.mapper.deviceseries;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigDictionaryEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-18 21:25
 */
@Mapper
public interface DeviceSubtypeSeriesConfigDictionaryMapper extends BaseMapper<DeviceSubtypeSeriesConfigDictionaryEntity> {
    /**
     * 根据 type 获取配置字典
     *
     * @param dictionaryType
     * @return
     */
    List<DeviceSubtypeSeriesConfigDictionaryEntity> getConfigDictionaryByType(String dictionaryType);

}
