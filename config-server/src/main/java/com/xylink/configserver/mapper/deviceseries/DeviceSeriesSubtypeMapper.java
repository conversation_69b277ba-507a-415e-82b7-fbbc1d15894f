package com.xylink.configserver.mapper.deviceseries;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.deviceseries.DeviceSeriesSubtypeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-18 22:48
 */
@Mapper
public interface DeviceSeriesSubtypeMapper extends BaseMapper<DeviceSeriesSubtypeEntity> {
    /**
     * 获取终端所属系列
     *
     * @param subtype
     * @return
     */
    List<DeviceSeriesSubtypeEntity> getSeries(int subtype);

    /**
     * 更新终端对应的系列
     *
     * @param seriesId
     * @param subtype
     */
    void saveOrUpdateSeriesSubtypeMapping(@Param("seriesId") long seriesId, @Param("subtype") int subtype);

    /**
     * 获取系列下所有的终端
     *
     * @param seriesId
     * @return
     */
    List<DeviceSeriesSubtypeEntity> getBySeriesId(@Param("seriesId") long seriesId);

    /**
     * delete by subtype
     *
     * @param subtype
     */
    void deleteBySubtype(int subtype);

}
