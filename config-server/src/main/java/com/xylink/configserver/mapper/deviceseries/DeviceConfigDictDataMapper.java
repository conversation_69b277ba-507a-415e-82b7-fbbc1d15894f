package com.xylink.configserver.mapper.deviceseries;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.deviceseries.DeviceConfigDictDataEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-27 11:33
 */
@Mapper
public interface DeviceConfigDictDataMapper extends BaseMapper<DeviceConfigDictDataEntity> {
    /**
     * query by dictType
     *
     * @param dictType
     * @return
     */
    List<DeviceConfigDictDataEntity> getByDictType(@Param("dictType") String dictType);
}
