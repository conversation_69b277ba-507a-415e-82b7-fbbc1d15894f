package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xylink.configserver.data.model.RechargeConfig;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RechargeConfigMapper extends BaseMapper<RechargeConfig> {

    /**
     * 兼容达梦：批量插入或更新
     */
    default void bulkInsertOrUpdate(List<RechargeConfig> rechargeConfigs) {

        for (RechargeConfig rechargeConfig : rechargeConfigs) {
            RechargeConfig tableRechargeConfig =
                    this.selectOne(Wrappers.<RechargeConfig>lambdaQuery()
                                           .eq(RechargeConfig::getClientConfigName, rechargeConfig.getClientConfigName())
                                           .eq(RechargeConfig::getDeviceSn, rechargeConfig.getDeviceSn())
                                           .eq(RechargeConfig::getConfigName, rechargeConfig.getConfigName())
                                           .last("limit 1"));
            if (tableRechargeConfig != null) {
                tableRechargeConfig.setConfigValue(rechargeConfig.getConfigValue());
                tableRechargeConfig.setConfigExpireTime(rechargeConfig.getConfigExpireTime());
                tableRechargeConfig.setUpdateTime(rechargeConfig.getUpdateTime());
                this.updateById(tableRechargeConfig);
            } else {
                this.insert(rechargeConfig);
            }
        }

    }

}
