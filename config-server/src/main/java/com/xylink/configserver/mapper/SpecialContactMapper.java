package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.NemoDefaultConfig;
import com.xylink.configserver.data.model.SpecialContact;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SpecialContactMapper extends BaseMapper<NemoDefaultConfig> {

    List<SpecialContact> getEnterpriseSpecialContacts(@Param("enterpriseProfileId") String enterpriseProfileId,@Param("usage") int usage);

    List<SpecialContact> getNemoSpecialContacts(@Param("specialFeatureId") String specialFeatureId, @Param("usage") int usage);

    List<SpecialContact> getSpecialContactsByTarget(@Param("target")int target, @Param("usage")int usage);
}
