package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.xylink.configserver.data.model.LibraServerEndpointConfigEntity;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface LibraServerEndpointConfigMapper extends BaseMapper<LibraServerEndpointConfigEntity> {

    LibraServerEndpointConfigEntity findServerEndpointConfig(@Param("userProfileId") Long userProfileId, @Param(
            "deviceId") Long deviceId, @Param("configName") String configName);


    //<select id="findServerEndpointConfigBatch" resultType="com.xylink.configserver.data.model
    // .LibraServerEndpointConfigEntity">
    //SELECT
    //        *
    //FROM
    //        libra_server_endpoint_config
    //WHERE
    //config_name = #{configName}
    //AND (
    //        user_profile_id in ( ${userids} )
    //OR device_id in ( ${deviceids} )
    //        )
    //</select>
    default List<LibraServerEndpointConfigEntity> findServerEndpointConfigBatch(
            @Param("userids") List<Long> userIds,
            @Param("deviceids") List<Long> deviceIds,
            @Param("configName") String configName) {

        if (CollectionUtils.isEmpty(userIds) && CollectionUtils.isEmpty(deviceIds)) {
            return Lists.newArrayListWithExpectedSize(1);
        }
        return selectList(
                Wrappers.<LibraServerEndpointConfigEntity>lambdaQuery()
                        .eq(LibraServerEndpointConfigEntity::getConfigName, configName)
                        .and(i -> {
                            if (CollectionUtils.isNotEmpty(userIds)) {
                                i.in(LibraServerEndpointConfigEntity::getUserProfileId, userIds);
                            }
                            i.or();
                            if (CollectionUtils.isNotEmpty(deviceIds)) {
                                i.in(LibraServerEndpointConfigEntity::getDeviceId, deviceIds);
                            }
                        }));

    }

}
