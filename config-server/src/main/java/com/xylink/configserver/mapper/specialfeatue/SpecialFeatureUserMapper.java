package com.xylink.configserver.mapper.specialfeatue;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xylink.configserver.data.model.specialfeature.SpecialFeatureUserEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/8/28 11:07
 */
@Mapper
public interface SpecialFeatureUserMapper extends BaseMapper<SpecialFeatureUserEntity> {

    default List<SpecialFeatureUserEntity> selectDeptFeature(List<Long> deptIds) {
        return selectList(Wrappers.<SpecialFeatureUserEntity>lambdaQuery()
                .in(SpecialFeatureUserEntity::getUserProfileId, deptIds)
                .eq(SpecialFeatureUserEntity::getDataType, 1));
    }

}
