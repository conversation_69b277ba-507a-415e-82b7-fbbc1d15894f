package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.NemoDefaultConfig;
import com.xylink.configserver.data.model.NemoDefaultVersionConfig;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DefaultConfigMapper extends BaseMapper<NemoDefaultConfig> {

    List<NemoDefaultConfig> getAllDefaultConfig();

    List<NemoDefaultVersionConfig> getNemoDefaultConfigByType(int type);

    /**
     * 获取支持该类型的版本配置
     *
     * @param type
     * @param version
     * @return
     */
    List<NemoDefaultVersionConfig> getNemoDefaultConfigByTypeAndVersion(@Param("type") int type, @Param("version") int version);

    List<NemoDefaultConfig> selectListByCondition(@Param("configType") Integer configType, @Param("clientConfigName") String clientConfigName, @Param("configName") String configName);

    Integer tryUpdateByCondition(@Param("configName") String configName, @Param("configValue") String configValue, @Param("configType") Integer configType, @Param("clientConfigName") String clientConfigName);

}
