package com.xylink.configserver.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.EnterpriseNemo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EnterpriseNemoMapper extends BaseMapper<EnterpriseNemo> {

    EnterpriseNemo getEnterpriseNemo(String nemoSN);

    void updateEnterpriseNemo(@Param("nemo") EnterpriseNemo enterpriseNemoPO);

    void addEnterpriseNemo(@Param("nemo") EnterpriseNemo enterpriseNemoPO);
}
