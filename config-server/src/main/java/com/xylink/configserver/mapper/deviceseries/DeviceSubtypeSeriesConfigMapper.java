package com.xylink.configserver.mapper.deviceseries;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-01-18 21:49
 */
@Mapper
public interface DeviceSubtypeSeriesConfigMapper extends BaseMapper<DeviceSubtypeSeriesConfigEntity> {
    /**
     * 根据类型查询
     *
     * @param id
     * @param type
     * @return
     */
    List<DeviceSubtypeSeriesConfigEntity> getConfigByIdAndType(@Param("id") long id, @Param("type") String type);

    /**
     * 删除系列配置
     *
     * @param seriesId
     */
    void deleteSeriesConfigBySeriesId(long seriesId);

    /**
     * 删除终端配置
     *
     * @param subtype
     */
    void deleteSubtypeConfigBySubtype(int subtype);
}
