package com.xylink.configserver.exception;

import com.xylink.configserver.enums.ErrorStatus;

public class ServiceException extends BaseServiceException {

    private static final long serialVersionUID = 1L;

    public ServiceException(String message, ErrorStatus status) {
        super(message, status);
    }

    public ServiceException(String message, Throwable cause,
                            ErrorStatus status) {
        super(message, cause, status);
    }

    @Override
    public String toString() {
        return "ServiceException [getMessage()=" + getMessage()
                + ", getStatus()=" + getStatus() + ", getCause()=" + getCause()
                + "]";
    }
}
