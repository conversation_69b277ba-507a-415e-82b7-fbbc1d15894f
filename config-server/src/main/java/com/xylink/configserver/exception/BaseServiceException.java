package com.xylink.configserver.exception;

import com.xylink.configserver.enums.ErrorStatus;

public class BaseServiceException extends RuntimeException {
    private static final long serialVersionUID = -1137527703456674414L;
    private ErrorStatus status;

    public BaseServiceException(String message, ErrorStatus status) {
        super(message);
        this.status = status;
    }

    public BaseServiceException(String message, Throwable cause, ErrorStatus status) {
        super(message, cause);
        this.status = status;
    }

    public String getMessage() {
        String ret = "ErrorStatus: " + this.status;
        return ret + " " + super.getMessage();
    }

    public ErrorStatus getStatus() {
        return this.status;
    }

    public String getMetaMessage() {
        return super.getMessage();
    }
}
