package com.xylink.configserver.exception;

import com.xylink.configserver.enums.ErrorStatus;

public class BusinessException extends Exception {
    private static final long serialVersionUID = 1L;

    private ErrorStatus status;

    public BusinessException(String message, ErrorStatus status){
        super(message);
        this.status = status;
    }

    public BusinessException(String message, Throwable cause, ErrorStatus status){
        super(message, cause);
        this.status = status;

    }

    @Override
    public String getMessage()
    {
        String ret;
        ret = "ErrorStatus: " + status;
        return ret + " " + super.getMessage();
    }

    public ErrorStatus getStatus()
    {
        return status;
    }

}
