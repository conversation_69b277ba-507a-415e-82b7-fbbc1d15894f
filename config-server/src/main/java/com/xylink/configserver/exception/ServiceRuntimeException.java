package com.xylink.configserver.exception;

import com.xylink.configserver.enums.ErrorStatus;

public class ServiceRuntimeException extends RuntimeException {

    /**
     *
     */
    private ErrorStatus status;
    private static final long serialVersionUID = 1L;

    public ServiceRuntimeException(String message)
    {
        super(message);
    }

    public ServiceRuntimeException(String message, ErrorStatus status)
    {
        super(message);
        this.status = status;
    }

    public ServiceRuntimeException(Throwable cause)
    {
        super(cause);
    }

    public ServiceRuntimeException(Throwable cause, ErrorStatus status)
    {
        super(cause);
        this.status = status;
    }

    public ServiceRuntimeException(String message, Throwable cause)
    {
        super(message, cause);
    }

    public ServiceRuntimeException(String message, Throwable cause, ErrorStatus status)
    {
        super(message, cause);
        this.status = status;
    }

    public ServiceRuntimeException(String message, Throwable cause, boolean enableSuppression,
                                   boolean writableStackTrace)
    {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public ErrorStatus getStatus()
    {
        return status;
    }

    @Override
    public String getMessage()
    {
        String ret;
        ret = status != null ? "ErrorStatus: " + status : "";
        return ret + " " + super.getMessage();
    }
}
