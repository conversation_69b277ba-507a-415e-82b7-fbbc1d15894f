<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.SpecialNemoFeatureMapper">

    <select id="getNemoFeatures" resultType="SpecialNemoFeature">
        SELECT
            id,
            description,
            config,
            status,
            appversion,
            feature_type type,
            special_ui_display specialUI,
            enterprise_id enterpriseId
        FROM
            libra_special_feature
        WHERE (
            STATUS = 1
            OR id IN (
                    SELECT
                        feature_id
                    FROM
                        libra_special_feature_nemo
                    WHERE
                        STATUS = 1
                    AND nemo_sn = #{nemoSN}
                )
            )
        AND appVersion &lt;= #{appVersion}
        AND feature_type = #{type}
    </select>

    <select id="getFeatureById" resultType="SpecialNemoFeature">
        SELECT
            id,
            description,
            config,
            status,
            appversion,
            feature_type type,
            special_ui_display specialUI,
            enterprise_id enterpriseId
        FROM
            libra_special_feature
        WHERE id = #{featureId}
    </select>

    <select id="getSpecialNemo" resultType="SpecialFeatureNemo">
        SELECT
            id,
            feature_id featureId,
            status,
            nemo_sn nemoSN  from libra_special_feature_nemo
        WHERE
        feature_id = #{featureId} and nemo_sn=#{nemoSN}
        <if test="checkStatus">
            AND enabled = 1
        </if>
    </select>

    <insert id="addSpecialFeatureNemo" parameterType="SpecialFeatureNemo">
        <selectKey keyProperty="nemo.id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(),'-','')
        </selectKey>
        insert into libra_special_feature_nemo (
        id,
        feature_id,
        status,
        nemo_sn) values (
        #{nemo.id},
        #{nemo.featureId},
        #{nemo.stauts},
        #{nemo.nemoSN})
    </insert>

    <select id="getSpecialConfigsByFeatureById">
        SELECT
            id,
            feature_id featureId,
            status,
            nemo_sn nemoSN libra_special_feature_nemo
        WHERE
            feature_id = #{featureId}
    </select>

    <select id="getBySpecialFeatureId" resultType="SpecialNemoCustomizeFeature">
        SELECT
            id,
            customize_feature_id customizeFeatureId,
            special_feature_id specialFeatureId,
            target_number targetNumber,
            feature_status featureStatus,
            trial,
            main_shortcut hasMainShortCut
        FROM
            libra_special_nemo_customize_feature
        WHERE
            special_feature_id = #{featureId}
    </select>

    <select id="getNemoFeature" resultType="NemoFeature">
        SELECT
            id,
            feature_id featureId,
            nemo_sn nemoSN,
            feature_status featureStatus,
            target_number targetNumber,
            start_time startTime,
            expire_time expireTime,
            trial,
            statement_agreed statementAgreed,
            main_shortcut hasMainShortCut,
            display_name displayName
        FROM
            libra_nemo_feature
        WHERE
            nemo_sn = #{nemoSN} and feature_id = #{featureId}
    </select>

    <update id="updateNemoFeature" parameterType="NemoFeature">
        UPDATE libra_nemo_feature set feature_id = #{feature.featureId},nemo_sn=#{feature.nemoSN},feature_status = #{feature.featureStatus},
        target_number=#{feature.targetNumber},start_time=#{feature.startTime},expire_time=#{feature.expireTime},trial=#{feature.trial},statement_agreed=#{feature.statementAgreed},
        main_shortcut=#{feature.hasMainShortCut},display_name=#{feature.displayName} where id =#{id}
    </update>

    <insert id="addNemoFeature" parameterType="NemoFeature">
        <selectKey keyProperty="feature.id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(),'-','')
        </selectKey>
        INSERT INTO libra_nemo_feature(id,feature_id,nemo_sn,feature_status,target_number,start_time,expire_time,trial,statement_agreed,main_shortcut,display_name)
        values (#{feature.id},#{feature.featureId},#{feature.nemoSN},#{feature.featureStatus},#{feature.targetNumber},#{feature.startTime},#{feature.expireTime},#{feature.trial},
        #{feature.statementAgreed},#{feature.hasMainShortCut},#{feature.displayName})
    </insert>

    <delete id="deleteNemoSpecialFeature">
        delete from libra_special_feature_nemo where id = #{feature.id}
    </delete>
</mapper>
