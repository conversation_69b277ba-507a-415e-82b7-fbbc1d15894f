<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.deviceseries.DeviceSubtypeMapper">

    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU"/>
        <property name="flushInterval" value="600000"/>
        <property name="size" value="500"/>
        <property name="readOnly" value="false"/>
    </cache>

    <select id="page" resultType="com.xylink.configserver.data.model.deviceseries.DeviceSubtypeListDto">
        SELECT
        DISTINCT
        a.sub_type AS subtype,
        a.category AS displayName,
        a.category_display AS categoryDisplayName,
        a.type AS type,
        b.series_id AS seriesId,
        c.series_name AS seriesName
        FROM
        libra_device_subtype_model a
        LEFT JOIN libra_device_series_subtype b ON a.sub_type = b.sub_type
        LEFT JOIN libra_device_series c ON b.series_id = c.id
        <where>
            <if test="key != null">
                (LOCATE(#{key},a.category)>0 or LOCATE(#{key},a.category_display )>0 or
                LOCATE(#{key},c.series_name)>0)
            </if>
        </where>
    </select>

    <select id="getBySubtype" resultType="com.xylink.configserver.data.model.deviceseries.DeviceSubtypeDto">
        SELECT
        a.sub_type AS subtype,
        a.category AS displayName,
        a.category_display AS categoryDisplayName,
        a.type as type
        FROM
        libra_device_subtype_model a
        WHERE a.sub_type=#{subtype}
        LIMIT 1
    </select>

    <update id="updateSubtypeCategoryDisplayName">
        UPDATE libra_device_subtype_model
        SET category_display = #{categoryDisplay}
        WHERE
            sub_type = #{subtype}
    </update>

</mapper>
