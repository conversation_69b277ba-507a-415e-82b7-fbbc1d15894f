<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.CommonGroupConfigMapper">
    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="600000" />
        <property name="size" value="500" />
        <property name="readOnly" value="false" />
    </cache>

    <select id="getGroupConfigs" resultType="DeviceConfig">
        SELECT
        id,
        config_name configName,
        config_value configValue,
        client_config_name clientConfigName
        FROM
        libra_common_group_config
        where config_group = #{configGroup}
          and config_type = 0
    </select>

    <select id="getGroupConfigsByType" resultType="CommonGroupConfig">
        SELECT
            id,
            config_name configName,
            config_value configValue,
            client_config_name clientConfigName
        FROM
            libra_common_group_config
        where config_group = #{configGroup}
          and config_type = #{configType}
    </select>

    <insert id="insertList">
        insert into libra_common_group_config (config_name, config_value, config_type, product_family,
                                               client_config_name,
                                               config_group)
        values
        <foreach collection="commonGroupConfigs" item="item" separator=",">
            (#{item.configName,jdbcType=VARCHAR}, #{item.configValue,jdbcType=VARCHAR}, #{item.configType,jdbcType=INTEGER},
            #{item.productFamily,jdbcType=VARCHAR}, #{item.clientConfigName,jdbcType=VARCHAR}, #{item.configGroup,jdbcType=INTEGER})
        </foreach>
    </insert>
</mapper>
