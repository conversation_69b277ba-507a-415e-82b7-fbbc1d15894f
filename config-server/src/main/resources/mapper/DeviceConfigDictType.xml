<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.deviceseries.DeviceConfigDictDataMapper">
    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU"/>
        <property name="flushInterval" value="600000"/>
        <property name="size" value="500"/>
        <property name="readOnly" value="false"/>
    </cache>

    <select id="getByDictType" resultType="com.xylink.configserver.data.model.deviceseries.DeviceConfigDictDataEntity">
        SELECT
            *
        FROM
            libra_config_dict_data
        WHERE
            dict_type = #{dictType}
            AND status=0
            ORDER BY dict_sort
	</select>

</mapper>
