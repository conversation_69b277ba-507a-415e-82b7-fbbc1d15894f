<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.SpecialContactMapper">

    <select id="getEnterpriseSpecialContacts" resultType="SpecialContact">
        SELECT
            en.contact_status contactStatus,
            contact.id as id,
            contact.NAME as "name",
            contact.number as "number" ,
            contact.target as target,
            contact.contact_usage "usage",
            contact.contact_type contactType,
            contact.i18n_name i18nName
        FROM
            libra_special_contact contact
            JOIN libra_enterprise_special_contacts en ON contact.id = en.special_contact_id
            AND en.enterprise_profile_id = #{enterpriseProfileId}
            AND contact.contact_usage = #{usage}
    </select>

    <select id="getNemoSpecialContacts" resultType="SpecialContact">
        SELECT
            nemo.contact_status AS contactStatus,
            contact.id as id,
            contact.NAME as "name",
            contact.number as "number" ,
            contact.target as target,
            contact.contact_usage "usage",
            contact.contact_type contactType,
            contact.i18n_name i18nName
        FROM
            libra_special_contact contact
            JOIN libra_nemo_special_contacts nemo ON contact.id = nemo.special_contact_id
            AND nemo.special_feature_id = #{specialFeatureId}
            AND contact.contact_usage = #{usage}
    </select>

    <select id="getSpecialContactsByTarget" resultType="SpecialContact">
        SELECT
            contact.id as id,
            contact.NAME as "name",
            contact.number as "number" ,
            contact.target as target,
            contact.contact_usage "usage",
            contact.contact_type contactType,
            contact.i18n_name i18nName
        FROM libra_special_contact contact
        WHERE target = #{target}
        AND contact_usage = ${usage}

    </select>
</mapper>