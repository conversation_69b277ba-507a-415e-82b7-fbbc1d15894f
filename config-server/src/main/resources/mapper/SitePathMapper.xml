<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.SitePathMapper">
    <select id="getEnterpriseSitePaths" resultType="EnterpriseSitePath">
        SELECT
            id,
            location_part_of_sitecode locationOfSitecode,
            enterprise_part_of_sitecode enterpriseOfSitecode,
            net_tool_server netToolServer,
            disaplay_name disalayName,
            enterprise_id enterpriseId,
            detail,
            time_enable timeEnable,
            time_disable timeDisable,
            enabled,
            sublocation_part_of_sitecode sublocationOfSitecode,
            provider_part_of_sitecode provoderOfSitecode,
            network_type networkType
        FROM
            libra_enterprise_site_path
        where enterprise_id = #{enterpriseId}
          and enabled = 1
    </select>
</mapper>
