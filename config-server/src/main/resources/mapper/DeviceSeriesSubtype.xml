<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.deviceseries.DeviceSeriesSubtypeMapper">

    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU"/>
        <property name="flushInterval" value="600000"/>
        <property name="size" value="500"/>
        <property name="readOnly" value="false"/>
    </cache>

    <select id="getSeries" resultType="com.xylink.configserver.data.model.deviceseries.DeviceSeriesSubtypeEntity">
        SELECT
        *
        FROM
        libra_device_series_subtype
        WHERE sub_type=#{subtype}
    </select>


    <insert id="saveOrUpdateSeriesSubtypeMapping">
        INSERT INTO libra_device_series_subtype (series_id, sub_type )
        VALUES (#{seriesId},#{subtype})
        ON DUPLICATE KEY UPDATE sub_type=values(sub_type),series_id=values(series_id)
    </insert>

    <select id="getBySeriesId" resultType="com.xylink.configserver.data.model.deviceseries.DeviceSeriesSubtypeEntity">
     SELECT
	    *
     FROM
	 libra_device_series_subtype
     WHERE
	 series_id = #{seriesId}
    </select>

    <delete id="deleteBySubtype">
        delete from libra_device_series_subtype where sub_type = #{subtype}
    </delete>

</mapper>
