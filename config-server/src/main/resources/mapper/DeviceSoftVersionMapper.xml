<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.DeviceSoftVersionMapper">

    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="600000" />
        <property name="size" value="500" />
        <property name="readOnly" value="false" />
    </cache>

    <select id="getDeviceVersionBySn" resultType="DeviceSoftVersion">
        SELECT
            id,
            device_sn deviceSn,
            current_soft_version currentSoftVersion,
            first_soft_version firstSoftVersion,
            modify_time modiftTime
        FROM
            libra_device_version_record
            where device_sn=#{deviceSN}
    </select>
</mapper>
