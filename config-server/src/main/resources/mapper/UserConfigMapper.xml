<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.UserConfigMapper">

    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="600000" />
        <property name="size" value="500" />
        <property name="readOnly" value="false" />
    </cache>

    <select id="getUserConfig" resultType="UserConfig">
        SELECT
            id,
            user_profile_id userProfileId,
            config_name configName,
            config_value configValue,
            config_expire_time configExpireTime,
            client_config_name clientConfigName,
            device_type deviceType
        FROM
            libra_user_config where user_profile_id=#{userProfileId} and device_type=#{deviceType}
    </select>

    <select id="getUserConfigs" resultType="UserConfig">
        SELECT
            id,
            user_profile_id userProfileId,
            config_name configName,
            config_value configValue,
            config_expire_time configExpireTime,
            client_config_name clientConfigName,
            device_type deviceType
        FROM
            libra_user_config where user_profile_id=#{userId}
            and config_name = #{configName}
            and device_type = #{deviceType}
            <if test="clientConfigName!=null and clientConfigName!=''">
                AND client_config_name is null
            </if>
            <if test="clientConfigName != null" >
                and client_config_name = #{clientConfigName}
            </if>

    </select>

    <insert id="addUserConfig" parameterType="UserConfig" useGeneratedKeys="true" keyProperty="id">
        insert into libra_user_config(user_profile_id,config_name,config_value,config_expire_time,client_config_name,device_type)
        values (#{config.userProfileId},#{config.configName},#{config.configValue},#{config.configExpireTime},#{config.clientConfigName},
        #{config.deviceType})
    </insert>
    
    <update id="updateUserConfig">
        update libra_user_config set config_value=#{config.configValue}
        where id = #{config.id}
    </update>
</mapper>
