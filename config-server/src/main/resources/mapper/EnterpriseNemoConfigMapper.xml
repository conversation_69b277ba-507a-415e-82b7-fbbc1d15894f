<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.EnterpriseNemoConfigMapper">
    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="600000" />
        <property name="size" value="500" />
        <property name="readOnly" value="false" />
    </cache>

    <sql id="all_column">
        config.id,
        config.config_name configName,
        config.config_value configValue,
        config.client_config_name clientConfigName,
        config.base_config_type baseConfigType,
        config.config_type configType,
        config.enterprise_profile_id enterpriseProfileId
    </sql>

    <select id="getEnterpriseNemoConfigByProfileId" resultType="EnterpriseNemoConfig">
        SELECT
        <include refid="all_column"/>
        FROM
            libra_enterprise_nemo_config config
        WHERE
            enterprise_profile_id = #{profileId}
    </select>

    <select id="getEnterpriseProfileConfig" resultType="EnterpriseNemoConfig">
        SELECT
        <include refid="all_column"/>
        FROM
            libra_enterprise_nemo_config AS config
            JOIN libra_enterprise_nemo_profile AS p ON
            (config.enterprise_profile_id = p.id
            AND p.enterprise_id = #{enterpriseId}
            AND config.config_type = #{type})
    </select>

    <select id="getEnterpriseDeviceConfigs" resultType="EnterpriseNemoConfig">
        SELECT
        <include refid="all_column"/>
        FROM
            libra_enterprise_nemo_config AS config
            JOIN libra_enterprise_nemo AS nemo ON
            (config.enterprise_profile_id = nemo.enterprise_profile_id
            AND config.config_type = #{type}
            AND nemo.nemo_sn = #{deviceSN})
    </select>

    <select id="getEnterpriseDeviceConfigByEnterpriseId" resultType="EnterpriseNemoConfig">
        select
        <include refid="all_column"/>
        FROM
            libra_enterprise_nemo_config config
        join libra_enterprise_nemo_profile pro on config.enterprise_profile_id = pro.id
        WHERE  pro.enterprise_id =#{enterpriseId}
        and config.config_type = #{type}
    </select>

    <select id="getEnterpriseDeviceClientConfig" resultType="EnterpriseNemoConfig">
        SELECT
        <include refid="all_column"/>
        FROM
            libra_enterprise_nemo_config AS config
            JOIN libra_enterprise_nemo AS nemo ON config.enterprise_profile_id = nemo.enterprise_profile_id
            AND config.client_config_name = #{clientConfigName}
            AND nemo.nemo_sn = #{deviceSN}
    </select>

    <select id="getEnterpriseDeviceConfig" resultType="EnterpriseNemoConfig">
        SELECT
        <include refid="all_column"/>
        FROM
            libra_enterprise_nemo_config AS config
            JOIN libra_enterprise_nemo AS nemo ON config.enterprise_profile_id = nemo.enterprise_profile_id
            AND config.config_name = #{configName}
            AND (
                config.client_config_name IS NULL
                OR config.client_config_name = ''
            )
            AND config.config_type = #{deviceType}
            AND nemo.nemo_sn = #{deviceSN}
    </select>

    <select id="getEntDeviceConfigBySubType" resultType="EnterpriseNemoConfig">
        SELECT
        <include refid="all_column"/>
        FROM
            libra_enterprise_nemo_config AS config
            JOIN libra_enterprise_nemo_profile AS PROFILE ON config.enterprise_profile_id = PROFILE.id
            AND config.config_name = #{configName}
            AND (
                config.client_config_name IS NULL
                OR config.client_config_name = ''
            )
            AND config.config_type = #{subType}
            AND PROFILE.enterprise_id = #{enterpriseId}
    </select>
    
    <select id="getEntDeviceConfigByType" resultType="EnterpriseNemoConfig">
        SELECT
        <include refid="all_column"/>
        FROM
            libra_enterprise_nemo_config AS config
            JOIN libra_enterprise_nemo_profile AS PROFILE ON config.enterprise_profile_id = PROFILE.id
            AND config.config_name = #{configName}
            AND (
                config.client_config_name IS NULL
                OR config.client_config_name = ''
            )
            AND config.config_type = #{type}
            AND PROFILE.enterprise_id = #{enterpriseId}
    </select>

    <select id="getEnterpriseCustomizedClientConfig" resultType="EnterpriseNemoConfig">
        SELECT
        <include refid="all_column"/>
        FROM
            libra_enterprise_nemo_config AS config
            JOIN libra_enterprise_nemo_profile AS PROFILE ON config.enterprise_profile_id = PROFILE.id
        WHERE
            PROFILE.customized_key = #{customizedKey}
            AND config.client_config_name = #{uiDisplayCustomization}
            AND config.config_type = #{configType}
    </select>

    <select id="getProfileConfigsByProfileId" resultType="EnterpriseNemoConfig">
        select
        <include refid="all_column"/>
        from libra_enterprise_nemo_config config where enterprise_profile_id = #{profileId}
    </select>

    <select id="getEnterpriseNemoConfigsByProfileId" resultType="EnterpriseNemoConfig">
        select
        <include refid="all_column"/>
        from libra_enterprise_nemo_config config where enterprise_profile_id = #{profileId}
    </select>
    <insert id="saveOrUpdateEnterpriseNemoConfig" parameterType="EnterpriseNemoConfig">
        INSERT INTO libra_enterprise_nemo_config (id, config_name, config_value, enterprise_profile_id, config_type, client_config_name, base_config_type)
        VALUES (#{config.id},#{config.configName},#{config.configValue},#{config.enterpriseProfileId},#{config.configType},#{config.clientConfigName},#{config.baseConfigType})
        ON DUPLICATE KEY UPDATE config_value=values(config_value)
    </insert>

    <select id="getProfileConfigsByProfileIdAndConfigName" resultType="EnterpriseNemoConfig">
        select
        <include refid="all_column"/>
        from libra_enterprise_nemo_config config where enterprise_profile_id = #{profileId}
        <if test="configName!=null and configName!=''">
        and config_name = #{configName}
        </if>
    </select>
</mapper>
