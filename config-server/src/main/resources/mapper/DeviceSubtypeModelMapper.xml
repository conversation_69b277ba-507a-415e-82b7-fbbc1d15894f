<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.LibraDeviceSubtypeModelMapper">

    <select id="selectBySubtype" resultType="com.xylink.configserver.data.model.LibraDeviceSubtypeModel">
        select * from libra_device_subtype_model where sub_type = #{subtype}
    </select>

    <select id="selectAll" resultType="com.xylink.configserver.data.model.LibraDeviceSubtypeModel">
        select * from libra_device_subtype_model
    </select>

</mapper>
