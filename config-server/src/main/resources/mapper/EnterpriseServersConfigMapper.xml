<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.EnterpriseServersConfigMapper">

    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="60" />
        <property name="size" value="500" />
        <property name="readOnly" value="false" />
    </cache>

    <select id="getDefaultServerConfig" resultType="DefaultServerConfig">
        SELECT
            config_name configName,
            config_value configValue,
            value_type valueType
        FROM
            default_server_config
    </select>

    <select id="getDefaultServerConfigByConfigName" resultType="DefaultServerConfig">
        SELECT
            config_name configName,
            config_value configValue,
            value_type valueType
        FROM
            default_server_config
        WHERE config_name= #{configName}
    </select>


    <select id="getDeviceTypeServerConfigs" resultType="DefaultServerConfig">
        SELECT
            config_name configName,
            config_value configValue,
            value_type valueType
        FROM
            libra_devicetype_server_config
            where sub_type = #{subType}
    </select>

    <select id="getEnterpriseServerConfigs" resultType="DefaultServerConfig">
        SELECT
            config_name configName,
            config_value configValue,
            value_type valueType
        FROM
            libra_enterprise_server_config
            where enterprise_id = #{enterpriseId}
    </select>

    <select id="getDeviceServerConfig" resultType="DefaultServerConfig">
        SELECT
            config_name configName,
            config_value configValue,
            value_type valueType
        FROM
            libra_device_server_config
            where device_id = #{deviceId}
    </select>

    <insert id="addDefaultServerConfig" parameterType="java.util.Map">
        insert into default_server_config (config_name,config_value) VALUES
        <foreach collection="params.keys" item="key" separator=",">
            (#{key},#{params[${key}]})
        </foreach>
    </insert>

    <select id="getDefaultServerConfigPoByConfigName"
            resultType="com.xylink.configserver.data.model.DefaultServerConfig">
        SELECT
            *
        FROM
            default_server_config
        WHERE config_name= #{configName}
    </select>

    <insert id="insert" parameterType="com.xylink.configserver.data.model.DefaultServerConfig" useGeneratedKeys="true"
            keyProperty="id">
        insert into default_server_config (config_name,config_value,value_type) VALUES(#{configName},#{configValue},#{valueType})
    </insert>

    <update id="update" parameterType="com.xylink.configserver.data.model.DefaultServerConfig">
       update default_server_config
       <set>
           <if test="configName!=null">
               config_name = #{configName},
           </if>
           <if test="configValue!=null">
               config_value = #{configValue},
           </if>
           <if test="valueType!=null and valueType>0">
               value_type = #{valueType}
           </if>
       </set>
       where id=#{id}
    </update>

</mapper>
