<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.ThirdAppFeatureMapper">

    <select id="getAll" resultType="ThirdAppFeature">
        SELECT
            id,
            package_name packageName,
            feature_name featureName
        FROM
            libra_thirdapp_feature
    </select>

    <select id="getCustomizeFeatureById" resultType="CustomizeFeature">
        SELECT
            id,
            display_name,
            icon,
            url,
            feature_type,
            priority,
            nemo_number,
            source_type,
            STATUS,
            description,
            client_version,
            name_key,
            main_shortcut,
            device_sub_type,
            icon_pressed
        FROM
            libra_customize_feature
        WHERE
            id = #{id}
    </select>
</mapper>
