<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.OpenNemoMapper">

    <select id="getByNemoId" resultType="OpenNemo">
        SELECT
            id,
            nemo_id nemoId,
            config
        FROM
            libra_open_nemo
        WHERE nemo_id = #{nemoId}
    </select>
</mapper>
