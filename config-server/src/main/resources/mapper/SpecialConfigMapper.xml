<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.SpecialConfigMapper">

    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="600000" />
        <property name="size" value="500" />
        <property name="readOnly" value="false" />
    </cache>

    <select id="getSpecialConfig" resultType="SpecialConfig">
        SELECT
        	config.id id,
        	config.special_feature_id specialFeatureId,
        	config.config_name configName,
        	config.config_value configValue,
        	config.client_config_name clientConfigName
        FROM
        	libra_special_feature_config config
        	JOIN libra_special_feature_nemo AS nemo ON config.special_feature_id = nemo.feature_id
        	AND nemo.nemo_sn = #{deviceSN}
    </select>

    <select id="getSpecialConfigByUserId" resultType="SpecialConfig">
        SELECT
            config.id,
            config.special_feature_id specialFeatureId,
            config.config_name configName,
            config.config_value configValue,
            config.client_config_name clientConfigName
        FROM
            libra_special_feature_config config
            JOIN libra_special_feature_user AS fuser ON config.special_feature_id = fuser.feature_id
            AND fuser.user_profile_id = #{userProfileId}
    </select>

    <select id="getSpecialConfigsByFeatureById" resultType="SpecialConfig">
            SELECT
                config.id,
                config.special_feature_id specialFeatureId,
                config.config_name configName,
                config.config_value configValue,
                config.client_config_name clientConfigName
            FROM
                libra_special_feature_config config
                WHERE id = #{featureId}
        </select>

    <select id="getSpecialClientConfig" resultType="SpecialConfig">
        SELECT
            config.id,
            config.special_feature_id specialFeatureId,
            config.config_name configName,
            config.config_value configValue,
            config.client_config_name clientConfigName
        FROM
            libra_special_feature_config config
            JOIN libra_special_feature_nemo AS nemo ON config.special_feature_id = nemo.feature_id
            AND config.client_config_name = #{clientConfigName}
            AND nemo.nemo_sn = #{deviceSN}
    </select>

    <select id="getDeviceSpecialConfig" resultType="SpecialConfig">
        SELECT
            config.id,
            config.special_feature_id specialFeatureId,
            config.config_name configName,
            config.config_value configValue,
            config.client_config_name clientConfigName
        FROM
            libra_special_feature_config config
            JOIN libra_special_feature_nemo AS nemo ON config.special_feature_id = nemo.feature_id
            AND config.config_name = #{configName}
            AND config.client_config_name IS NULL
            AND nemo.nemo_sn = #{nemoSN}
    </select>

    <select id="getSpecialConfigByConfigNameAndClientName" resultType="SpecialConfig">
        SELECT
            config.id,
            config.special_feature_id specialFeatureId,
            config.config_name configName,
            config.config_value configValue,
            config.client_config_name clientConfigName
        FROM
            libra_special_feature_config config
            JOIN libra_special_feature_nemo AS nemo ON config.special_feature_id = nemo.feature_id
            AND config.config_name = #{configName}
            AND config.client_config_name = #{clientConfigName}
            AND nemo.nemo_sn = #{deviceSN}
            AND config.special_feature_id != #{specialFeatureId}
    </select>

    <select id="getSpecialConfigByCondition" resultType="com.xylink.configserver.data.model.SpecialConfig">

        select  * from libra_special_feature_config config
        <if test="deviceSN != null and deviceSN != ''">
            JOIN libra_special_feature_nemo AS nemo ON config.special_feature_id = nemo.feature_id
            and nemo.nemo_sn = #{deviceSN}
            <if test="clientConfigName != null and clientConfigName != ''">
                and client_config_name = #{clientConfigName,jdbcType=VARCHAR}
            </if>
        </if>
        <if test="userProfileId != null and userProfileId != ''">
            JOIN libra_special_feature_user AS fuser ON config.special_feature_id = fuser.feature_id
            and fuser.user_profile_id = #{userProfileId,jdbcType=VARCHAR}
            <if test="clientConfigName != null and clientConfigName != ''">
                and client_config_name = #{clientConfigName,jdbcType=VARCHAR}
            </if>
        </if>

    </select>
</mapper>
