<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.NemoDefaultBrandConfigMapper">

    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="600000" />
        <property name="size" value="500" />
        <property name="readOnly" value="false" />
    </cache>

    <select id="getDefaultBrandConfigByBrandAndType" resultType="NemoDefaultBrandConfig">
        SELECT
            id,
            config_name configName,
            config_value configValue,
            config_brand configBrand,
            config_type configType,
            client_config_name clientConfigName,
            base_config_type baseConfigType,
            brand_model brandModel
        FROM
            libra_nemo_default_brand_config AS config
        WHERE
            config.config_brand = #{brand}
            AND config_type = #{type}
            AND brand_model = #{brandModel}
    </select>

    <select id="getDefaultBrandConfigAll" resultType="NemoDefaultBrandConfig">
        SELECT
            id,
            config_name configName,
            config_value configValue,
            config_brand configBrand,
            config_type configType,
            client_config_name clientConfigName,
            base_config_type baseConfigType,
            brand_model brandModel
        FROM
            libra_nemo_default_brand_config AS config
    </select>

    <select id="getDefaultBrandConfigUnique" resultType="NemoDefaultBrandConfig">
        SELECT
            config.id,
            config.config_name configName,
            config.config_value configValue,
            config.config_brand configBrand,
            config.config_type configType,
            config.client_config_name clientConfigName,
            config.base_config_type baseConfigType,
            config.brand_model brandModel
        FROM
            libra_nemo_default_brand_config AS config
        WHERE
            config.config_brand = #{config.configBrand}
            AND config_name = #{config.configName}
            AND config_type = #{config.configType}
            AND client_config_name = #{config.clientConfigName}
            AND brand_model = #{config.brandModel}
    </select>

    <insert id="saveBrandConfig" parameterType="NemoDefaultBrandConfig" useGeneratedKeys="true" keyProperty="id">
        insert into libra_nemo_default_brand_config (config_name,config_value,config_brand,config_type,client_config_name,base_config_type,brand_model)
        values (#{config.configName},#{config.configValue},#{config.configBrand},#{config.configType},#{config.clientConfigName},#{config.baseConfigType},#{config.brandModel})
    </insert>

    <update id="updateBrandConfig">
        update libra_nemo_default_brand_config set config_value = #{config.configValue} where id = #{config.id}
    </update>

    <delete id="deleteBrandConfig">
        delete from libra_nemo_default_brand_config where id = #{id}
    </delete>

</mapper>
