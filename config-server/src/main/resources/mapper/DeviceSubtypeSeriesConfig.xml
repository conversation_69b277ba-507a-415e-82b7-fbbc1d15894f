<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.deviceseries.DeviceSubtypeSeriesConfigMapper">

    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU"/>
        <property name="flushInterval" value="600000"/>
        <property name="size" value="500"/>
        <property name="readOnly" value="false"/>
    </cache>

    <select id="getConfigByIdAndType"
            resultType="com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigEntity">
        SELECT
        *
        FROM
        libra_device_subtype_series_config
        <where>
            <if test="type=='SERIES'">
                series_id = #{id}
            </if>
            <if test="type=='SUBTYPE'">
                sub_type = #{id}
            </if>
        </where>
    </select>

    <delete id="deleteSeriesConfigBySeriesId">
        delete from libra_device_subtype_series_config where series_id = #{seriesId}
    </delete>

    <delete id="deleteSubtypeConfigBySubtype">
        delete from libra_device_subtype_series_config where sub_type = #{subtype}
    </delete>

</mapper>
