<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.BoxEnterpriseEntityMapper">
  <resultMap id="BaseResultMap" type="com.xylink.configserver.data.entity.BoxEnterpriseEntity">
    <!--@mbg.generated-->
    <!--@Table libra_box_enterprise-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="model_list" jdbcType="VARCHAR" property="modelList" />
    <result column="dummy_admin_account" jdbcType="VARCHAR" property="dummyAdminAccount" />
    <result column="app_vendor" jdbcType="VARCHAR" property="appVendor" />
    <result column="invitation_sms" jdbcType="LONGVARCHAR" property="invitationSms" />
    <result column="sub_type" jdbcType="INTEGER" property="subType" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, package_name, model_list, dummy_admin_account, app_vendor, invitation_sms, 
    sub_type
  </sql>
</mapper>