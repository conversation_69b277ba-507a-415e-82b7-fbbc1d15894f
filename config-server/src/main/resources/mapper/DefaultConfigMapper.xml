<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.DefaultConfigMapper">

    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="600000" />
        <property name="size" value="500" />
        <property name="readOnly" value="false" />
    </cache>

    <select id="getAllDefaultConfig" resultType="NemoDefaultConfig">
        SELECT
            id,
            config_name configName,
            config_value configValue,
            client_config_name clientConfigName,
            base_config_type baseConfigType,
            config_type configType,
            product_family productFamily
        FROM
            libra_default_config
    </select>

    <select id="getNemoDefaultConfigByType" resultType="NemoDefaultVersionConfig">
        SELECT
            id,
            config_name configName,
            config_value configValue,
            config_version_list configVersionList,
            config_type configType,
            client_config_name clientConfigName,
            base_config_type baseConfigType
        FROM
            libra_nemo_default_version_config
            where config_type = #{type}
    </select>

    <select id="getNemoDefaultConfigByTypeAndVersion"
            resultType="com.xylink.configserver.data.model.NemoDefaultVersionConfig">
        SELECT
            id,
            config_name configName,
            config_value configValue,
            config_version_list configVersionList,
            config_type configType,
            client_config_name clientConfigName,
            base_config_type baseConfigType
        FROM
            libra_nemo_default_version_config
            where config_type = #{type}
            and config_version_list &lt;= #{version}
    </select>

    <select id="selectListByCondition" resultType="com.xylink.configserver.data.model.NemoDefaultConfig">
        select * from libra_default_config where config_type = #{configType,jdbcType=VARCHAR}
        <if test="clientConfigName != null and clientConfigName != ''">
            and client_config_name = #{clientConfigName,jdbcType=VARCHAR}
        </if>
        <if test="configName != null and configName != ''">and config_name = #{configName,jdbcType=VARCHAR}</if>
        ;
    </select>

    <update id="tryUpdateByCondition">
        update ainemo.libra_default_config set config_value = #{configValue} where config_name = #{configName} and config_type = #{configType} and client_config_name = #{clientConfigName}
    </update>

</mapper>