<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.deviceseries.DeviceSubtypeSeriesConfigDictionaryDataMapper">
    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU"/>
        <property name="flushInterval" value="600000"/>
        <property name="size" value="500"/>
        <property name="readOnly" value="false"/>
    </cache>

    <select id="getByConfigIdAndSpecial"
            resultType="com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigDictionaryDataEntity">
        SELECT
            *
        FROM
            libra_device_subtype_series_config_dictionary_data
        WHERE
            STATUS = 0
            AND config_id = #{configId}
            AND (
            special is null
            OR special = #{special})
    </select>

    <select id="getBySeriesId"
            resultType="com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigDictionaryDataEntity">
        SELECT
            d.*
        FROM
            libra_device_series a,
            libra_device_subtype_series_config b,
            libra_device_subtype_series_config_dictionary c,
            libra_device_subtype_series_config_dictionary_data d
        WHERE
            a.id = b.series_id
            AND b.config_id = c.id
            AND c.id = d.config_id
            AND c.STATUS = 0
            AND d.STATUS = 0
            AND (
            d.special is null
            OR d.special = a.special)
            AND a.id=#{seriesId}
    </select>
</mapper>
