<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.UserDeviceMapper">

    <select id="getGWDeviceExpiredTimestamp" resultType="Long">
        select expired_timestamp from libra_gw_device where sk = #{securityKey}
    </select>

    <select id="getUserDeviceBySk" resultType="UserDevice">
        SELECT
            id,
            device_type type,
            user_profile_id userProfileID,
            sub_type subType,
            device_sn deviceSN,
            bind_timestamp bindTimestamp,
            enterprise_id enterpriseId,
            device_expire_time expirationTime,
            device_sk securityKey
        FROM
            libra_user_device
        WHERE
            device_sk = #{securityKey}
    </select>

    <select id="getUserDeviceByDevSn" resultType="UserDevice">
        SELECT
            id,
            device_type type,
            sub_type subType,
            user_profile_id userProfileID,
            device_sn deviceSN,
            bind_timestamp bindTimestamp,
             enterprise_id enterpriseId,
            device_expire_time expirationTime,
            device_sk securityKey
        FROM
            libra_user_device
        WHERE
            device_sn = #{deviceSn}
    </select>

    <select id="getEnterpriseNemoBySn" resultType="string">
        SELECT
            nemo_sn
        FROM
            libra_enterprise_nemo
        WHERE
            nemo_sn = #{nemoSN} union
            select nemo_sn from libra_nemo_model where nemo_sn = #{nemoSN} and model like 'NE%'
    </select>

    <select id="getEnBoxPOBySn" resultType="string">
        select device_sn from libra_en_box where device_sn=#{nemoSN}
    </select>

    <select id="getNemoFirstBindTime" resultType="Long">
        select first_bind_timestamp from libra_nemo_first_bindtime where nemo_sn = #{deviceSN}
    </select>

    <select id="getUserProfileEnterpriseByUserId" resultType="string">
        select enterprise_id from libra_user_profile where id = #{userProfileId}
    </select>
    
    <select id="getUserDeviceByNemoId" resultType="UserDevice">
        SELECT
            id,
            device_type type,
            user_profile_id userProfileID,
            sub_type subType,
            device_sn deviceSN,
            bind_timestamp bindTimestamp,
            device_expire_time expirationTime,
            enterprise_id enterpriseId,
            device_sk securityKey
        FROM
            libra_user_device
        WHERE
            id = #{nemoId}
    </select>

    <select id="getCommunityUserIds" resultType="long">
        select user_profile_id userid from libra_community_user where community_of_one_nemo_id = #{id}
    </select>

    <select id="getNemoNumberByDeviceId" resultType="long">
        select number from libra_nemo_number where device_id = #{deviceId}
    </select>

    <select id="getGwDeviceBySkAndType" resultType="GwDevice">
        SELECT
            id,
            sn,
            number,
            sk securityKey,
            created_timestamp createdTimestamp,
            gw_type type,
            max_in_count maxInCount,
            expired_timestamp expiredTimestamp,
            pwd,
            enterprise_id enterpriseId,
            is_native isNative
        FROM
            libra_gw_device
        WHERE
            sk = #{sk} and gw_type=#{name}
    </select>

    <select id="getInUseUserDeviceByDevSnAndTypes" resultType="UserDevice">
        SELECT
            id,
            enterprise_id enterpriseId,
            user_profile_id userProfileID,
            device_sn deviceSN,
            device_type type,
            sub_type,
            device_sk securityKey
        FROM
            libra_user_device
        where device_sn = #{deviceSn}
        and device_type in (${types})
    </select>

    <select id="getUserProfile2Device" resultType="UserDevice">
        SELECT
            id userProfileID,
            enterprise_id enterpriseId
        FROM
            libra_user_profile
        WHERE
            id = #{userId}
    </select>


    <select id="getUserProfileIds" resultType="UserProfile">
        SELECT
            id,user_phone cellPhone
        FROM
            libra_user_profile
        WHERE
            user_phone in ( ${telphones} )
    </select>

    <select id="getDeviceIds" resultType="UserDevice">
        SELECT
            device_id id, number nemoNumber
        FROM
            libra_nemo_number
        WHERE
            number in ( ${numbers} )
    </select>

    <select id="getUserProfileByEnterpriseId" resultType="UserProfile">
        select id,user_display_name displayName ,enterprise_id enterpriseId from libra_user_profile where enterprise_id = #{enterpriseId}
    </select>

    <select id="getUserById" resultType="UserPO">
        SELECT
            id,
            user_password password,
            user_password_salt passwordSalt,
            user_password_create_time passwordCreateTime,
            user_password_expire_time passwordExpireTime,
            locked locked
        FROM
            libra_user where id = #{userId}
    </select>

    <update id="passwordExpireTime">
        update libra_user set user_password_expire_time = #{passwordExpireTime} where id = #{id}
    </update>

    <select id="getEnterpriseDevices" resultType="UserDevice">
        SELECT
            id,
            device_type type,
            user_profile_id userProfileID,
            sub_type subType,
            device_sn deviceSN,
            bind_timestamp bindTimestamp,
            enterprise_id enterpriseId,
            device_expire_time expirationTime,
            device_sk securityKey
        FROM
            libra_user_device
        WHERE
            enterprise_id = #{enterpriseId}
        AND device_type = #{deviceType}
        AND device_sn is not null
    </select>

    <select id="getEnterpriseNemos" resultType="UserDevice">
        SELECT
            device.id id,
            device.device_type type,
            device.user_profile_id userProfileID,
            device.sub_type subType,
            device.device_sn deviceSN,
            device.device_sk securityKey
        FROM
            libra_user_device AS device
            JOIN libra_enterprise_nemo AS enemo ON device.device_sn = enemo.nemo_sn
            AND device.device_type = 2
            AND device.in_use = true
            AND enemo.enterprise_profile_id = #{enterpriseProfileId}
    </select>


    <select id="getEnterpriseProfileNemosByType" resultType="UserDevice">
        SELECT
            device.id id,
            device.device_type type,
            device.user_profile_id userProfileID,
            device.sub_type subType,
            device.device_sn deviceSN,
            device.device_sk securityKey
        FROM
            libra_user_device as device
            join libra_enterprise_nemo_profile as pro on device.enterprise_id = pro.enterprise_id
            AND pro.id = #{enterpriseProfileId}
            AND device.device_type =  #{deviceTypeOrSubType}
            AND device.in_use = true
            AND device.id > #{startId} order by id asc
            limit #{limit}
    </select>

    <select id="getEnterpriseProfileNemosBySubType" resultType="UserDevice">
        SELECT
            device.id id,
            device.device_type type,
            device.user_profile_id userProfileID,
            device.sub_type subType,
            device.device_sn deviceSN,
            device.device_sk securityKey
        FROM
            libra_user_device as device
            join libra_enterprise_nemo_profile as pro on device.enterprise_id = pro.enterprise_id
            AND pro.id = #{enterpriseProfileId}
            AND device.sub_type =  #{deviceTypeOrSubType}
            AND device.in_use = true
            AND device.id > #{startId} order by id asc
            limit #{limit}
    </select>

    <select id="batchGetDeviceListByIdList" resultType="UserDevice">
        SELECT id id, device_type type, user_profile_id userProfileId, sub_type subType, device_sn deviceSN, device_sk securityKey, device_display_name displayName
               FROM libra_user_device WHERE id IN
        <foreach collection="deviceIdList" item="snItem" open="(" close=")" separator=",">
            #{snItem}
        </foreach>
    </select>


</mapper>
