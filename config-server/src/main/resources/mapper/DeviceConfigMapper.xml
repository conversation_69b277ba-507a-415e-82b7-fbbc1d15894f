<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.DeviceConfigMapper">

    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="600000" />
        <property name="size" value="500" />
        <property name="readOnly" value="false" />
    </cache>

    <select id="getDeviceConfig" resultType="DeviceConfig">
        SELECT
            id,
            nemo_id nemoId,
            config_name configName,
            config_value configValue,
            client_config_name clientConfigName,
            config_expire_time configExpireTime
        FROM
            libra_nemo_config
            where nemo_id = #{deviceId}
    </select>

    <select id="getDeviceClientConfigs" resultType="DeviceConfig">
        SELECT
            id,
            nemo_id nemoId,
            config_name configName,
            config_value configValue,
            client_config_name clientConfigName,
            config_expire_time configExpireTime
        FROM
            libra_nemo_config
            where nemo_id = #{deviceId} and client_config_name = #{clientConfigName}
    </select>

    <select id="getNemoConfig" resultType="DeviceConfig">
        SELECT
            id,
            nemo_id nemoId,
            config_name configName,
            config_value configValue,
            client_config_name clientConfigName,
             config_expire_time configExpireTime
        FROM
            libra_nemo_config
        where
        nemo_id= #{deviceId}
        and config_name=#{configName}
        and (
            client_config_name='common'
            or (
                client_config_name=''
                or client_config_name is null
            )
        )
    </select>

    <select id="getNemoConfigByConfigNameAndClientConfigName" resultType="DeviceConfig">
        SELECT
        id,
        nemo_id nemoId,
        config_name configName,
        config_value configValue,
        client_config_name clientConfigName,
        config_expire_time configExpireTime
        FROM
        libra_nemo_config
        where
        nemo_id= #{deviceId}
        and config_name=#{configName}
        <choose>
            <when test="clientConfigName!=null and clientConfigName!=''">
                and client_config_name = #{clientConfigName}
            </when>
            <otherwise>
                and (
                client_config_name='common'
                or (
                client_config_name=''
                or client_config_name is null
                )
                )
            </otherwise>
        </choose>
    </select>

    <select id="getClientConfig" resultType="DeviceConfig">
        SELECT
            id,
            nemo_id nemoId,
            config_name configName,
            config_value configValue,
            client_config_name clientConfigName,
             config_expire_time configExpireTime
        FROM
            libra_nemo_config
            where nemo_id = #{nemoId} and client_config_name = #{clientConfigName} and config_name = #{configName}
    </select>

    <insert id="addDeviceConfig" parameterType="DeviceConfig" useGeneratedKeys="true" keyProperty="id">
        insert into libra_nemo_config (nemo_id,config_name,config_value,client_config_name,config_expire_time)
        values (#{config.nemoId},#{config.configName},#{config.configValue},#{config.clientConfigName},#{config.configExpireTime})
    </insert>

    <update id="updateDeviceConfigById" parameterType="DeviceConfig">
        update libra_nemo_config set config_value = #{config.configValue} where id = #{config.id}
    </update>

    <delete id="deleteUserNemoRelation">
        delete from libra_user_nemo_relation where nemo_id = #{nemoId}
    </delete>

    <insert id="addUserNemoRelation" useGeneratedKeys="true" keyProperty="id">
        insert into libra_user_nemo_relation (user_profile_id,nemo_id) values (#{userId},#{nemoId})
    </insert>

    <delete id="deleteDeviceConfigById">
        delete from libra_nemo_config where id = #{id}
    </delete>

    <select id="getDeviceConfigByCondition" resultType="com.xylink.configserver.data.model.BaseConfig">

        select * from libra_nemo_config where  nemo_id = #{id}
        <if test="clientConfigName == null or  clientConfigName == 'common'">
            and (client_config_name is null  or  client_config_name = 'common' or  client_config_name = '')
        </if>
        <if test="clientConfigName != null and clientConfigName != ''">
            and client_config_name = #{clientConfigName}
        </if>

    </select>

    <delete id="deleteDeviceConfigByConfigName">
        delete from libra_nemo_config where nemo_id = #{nemoId} and config_name = #{configName} and client_config_name = #{clientConfigName}
    </delete>
</mapper>
