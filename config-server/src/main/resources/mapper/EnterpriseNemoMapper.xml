<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.EnterpriseNemoMapper">

    <select id="getEnterpriseNemo" resultType="EnterpriseNemo">
        SELECT
            id,
            nemo_sn nemoSN,
            enterprise_profile_id enterpriseProfileId,
            conference_number_id conferenceNumberId,
            create_time createTime
        FROM
            libra_enterprise_nemo
        WHERE
            nemo_sn = #{nemoSN}
    </select>

    <update id="updateEnterpriseNemo" parameterType="EnterpriseNemo">
        update libra_enterprise_nemo set enterprise_profile_id = #{nemo.enterpriseProfileId} where id = #{nemo.id}
    </update>

    <insert id="addEnterpriseNemo" parameterType="EnterpriseNemo">
        <selectKey keyProperty="nemo.id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(),'-','')
        </selectKey>
        insert into libra_enterprise_nemo (
        id,
        nemo_sn,
        enterprise_profile_id,
        create_time
        )VALUES
        (#{nemo.id},
        #{nemo.nemoSN},
        #{nemo.enterpriseProfileId},
        #{nemo.createTime})
    </insert>
</mapper>
