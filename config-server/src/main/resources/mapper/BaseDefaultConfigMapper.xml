<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.BaseDefaultConfigMapper">

    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU" />
        <property name="flushInterval" value="600000" />
        <property name="size" value="500" />
        <property name="readOnly" value="false" />
    </cache>

    <select id="getAllBaseDefaultConfig" resultType="com.xylink.configserver.data.model.NemoBaseDefaultConfig">
        SELECT
            id,
            config_name configName,
            config_value configValue,
            client_config_name clientConfigName,
            base_config_type baseConfigType,
            config_type configType,
            product_family productFamily
        FROM
            libra_base_default_config
    </select>

    <select id="selectListByCondition" resultType="com.xylink.configserver.data.model.NemoBaseDefaultConfig">
        select * from libra_base_default_config where config_type = #{configType,jdbcType=VARCHAR}
        <if test="clientConfigName != null and clientConfigName != ''">
            and client_config_name = #{clientConfigName,jdbcType=VARCHAR}
        </if>
        <if test="configName != null and configName != ''">and config_name = #{configName,jdbcType=VARCHAR}</if>
        ;
    </select>

    <insert id="insertList">
        insert into libra_base_default_config (config_name, config_value, config_type, product_family,
                                              client_config_name,
                                              base_config_type)
        values
        <foreach collection="nemoBaseDefaultConfigs" item="item" separator=",">
            (#{item.configName,jdbcType=VARCHAR}, #{item.configValue,jdbcType=VARCHAR}, #{item.configType,jdbcType=INTEGER},
            #{item.productFamily,jdbcType=VARCHAR}, #{item.clientConfigName,jdbcType=VARCHAR}, #{item.baseConfigType,jdbcType=INTEGER})
        </foreach>
    </insert>
</mapper>