<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.EnterpriseNemoProfileMapper">

    <select id="getEnterpriseProfileByEnterpriseId" resultType="EnterpriseNemoProfile">
        SELECT
            id,
            display_name displayName,
            enterprise_id enterpriseId,
            special_ui_display specialUI,
            special_nemo_binding specialNemoBinding,
            need_authentication needAuthentication,
            customized_key customizedKey,
            enhance_password enhancePassword,
            password_expire_interval passwordExpireInterval,
            join_enterprise joinEnterprise,
            special_client_login specialClientLogin
        FROM
            libra_enterprise_nemo_profile where enterprise_id = #{enterpriseId} limit 1
    </select>

    <select id="getEnterpriseProfileByProfileId" resultType="EnterpriseNemoProfile">
        SELECT
            id,
            display_name displayName,
            enterprise_id enterpriseId,
            special_ui_display specialUI,
            special_nemo_binding specialNemoBinding,
            need_authentication needAuthentication,
            customized_key customizedKey,
            enhance_password enhancePassword,
            password_expire_interval passwordExpireInterval,
            join_enterprise joinEnterprise,
            special_client_login specialClientLogin
        FROM
            libra_enterprise_nemo_profile where id = #{profileId}
    </select>

    <delete id="removeEnterpriseNemoProfileConfig">
        DELETE
        FROM
            libra_enterprise_nemo_config
        WHERE
            enterprise_profile_id = #{profileId}
            AND config_name = #{configName}
            AND  config_type = #{deviceSubType}
            AND client_config_name = #{clientConfigName}
    </delete>

    <select id="getEnterpriseProfileHardCount" resultType="int">
        SELECT
            count(1)
        FROM
            libra_user_device AS device
            JOIN libra_enterprise_nemo_profile AS pro ON device.enterprise_id = pro.enterprise_id
        WHERE
            device.device_type = #{deviceType}
            AND device.in_use = true
            AND pro.id = #{profileId}
    </select>

    <select id="getEnterpriseProfileDeviceBySubCount" resultType="int">
        SELECT
            count( 1 )
        FROM
            libra_user_device AS device
            JOIN libra_enterprise_nemo_profile AS pro ON device.enterprise_id = pro.enterprise_id
        WHERE
            device.sub_type = #{deviceSubType}
            AND device.in_use = true
            AND pro.id = #{profileId}
    </select>

    <insert id="addEnterpriseNemoProfile" parameterType="EnterpriseNemoProfile">
        <selectKey keyProperty="profile.id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(),'-','')
        </selectKey>
        INSERT INTO libra_enterprise_nemo_profile (
            id,
            display_name,
            enterprise_id,
            special_ui_display,
            special_nemo_binding,
            need_authentication,
            enhance_password,
            password_expire_interval,
            customized_key,
            join_enterprise,
            special_client_login
            )
        VALUES
            (#{profile.id},
            #{profile.displayName},
            #{profile.enterpriseId},
            #{profile.specialUI},
            #{profile.specialNemoBinding},
            #{profile.needAuthentication},
            #{profile.enhancePassword},
            #{profile.passwordExpireInterval},
            #{profile.customizedKey},
            #{profile.joinEnterprise},
            #{profile.specialClientLogin}
            )
    </insert>

    <update id="updateEnterpriseNemoProfile">
        update libra_enterprise_nemo_profile set display_name=#{profile.displayName},special_nemo_binding=#{profile.specialNemoBinding},special_ui_display=#{profile.specialUI},
        password_expire_interval = #{profile.passwordExpireInterval} where id = #{profile.id}
    </update>

    <update id="updateEnterpriseNemoProfileSome">
        update libra_enterprise_nemo_profile set customized_key= #{profile.customizedKey},special_client_login= #{profile.specialClientLogin} where id = #{profile.id}
    </update>

    <select id="listByProfileId" resultType="EnterpriseNemoFeature">
        SELECT
            id,
            feature_id featureId,
            feature_status featureStatus,
            enterprise_profile_id profileId,
            main_shortcut hasMainShortCut
        FROM
            libra_enterprise_nemo_feature
        WHERE
            enterprise_profile_id = #{profileId}
    </select>

    <update id="updateEnterpriseNemoFeature" parameterType="EnterpriseNemoFeature">
        update libra_enterprise_nemo_feature set feature_status = #{po.featureStatus},main_shortcut=#{po.hasMainShortCut} where id = #{po.id}
    </update>

    <select id="getEnterpriseNemoFeatures" resultType="EnterpriseNemoFeature">
        SELECT
            id,
            feature_id featureId,
            feature_status featureStatus,
            enterprise_profile_id profileId,
            main_shortcut hasMainShortCut
        FROM
            libra_enterprise_nemo_feature
        WHERE where feature_id = #{featureId} and enterprise_profile_id = #{profileId}
    </select>

    <insert id="addEnterpriseNemoFeature" parameterType="EnterpriseNemoFeature">
        <selectKey keyProperty="feature.id" order="BEFORE" resultType="java.lang.String">
            select replace(uuid(),'-','')
        </selectKey>
        insert into libra_enterprise_nemo_feature(
        id,
        feature_id,
        feature_status,
        enterprise_profile_id,
        main_shortcut
        ) values (
        #{feature.id},
        #{feature.featureId},
        #{feature.featureStatus},
        #{feature.profileId},
        #{feature.hasMainShortCut})
    </insert>

    <select id="getEnterpriseProfileByCustomizedKey" resultType="EnterpriseNemoProfile">
        SELECT
            id,
            display_name displayName,
            enterprise_id enterpriseId,
            special_ui_display specialUI,
            special_nemo_binding specialNemoBinding,
            need_authentication needAuthentication,
            customized_key customizedKey,
            enhance_password enhancePassword,
            password_expire_interval passwordExpireInterval,
            join_enterprise joinEnterprise,
            special_client_login specialClientLogin
        FROM
            libra_enterprise_nemo_profile where customized_key = #{customizedKey} limit 1
    </select>
</mapper>
