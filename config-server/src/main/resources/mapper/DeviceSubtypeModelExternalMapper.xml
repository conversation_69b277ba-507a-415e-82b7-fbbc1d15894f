<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.DeviceSubtypeModelExternalMapper">
  <resultMap id="BaseResultMap" type="com.xylink.configserver.data.entity.DeviceSubtypeModelExternalEntity">
    <!--@mbg.generated-->
    <!--@Table libra_device_subtype_model_external-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="subtype" jdbcType="INTEGER" property="subtype" />
    <result column="multi_image_avc" jdbcType="VARCHAR" property="multiImageAvc" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, subtype, multi_image_avc
  </sql>
</mapper>