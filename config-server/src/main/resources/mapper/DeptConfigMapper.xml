<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.DeptConfigMapper">

    <update id="updateConfigValueById">
        UPDATE libra_dept_config SET config_value = #{configValue}, update_time =#{updateTime} WHERE id = #{id}
    </update>

    <select id="findConfigListByDeptIdList" resultType="DeptConfigPO">
        SELECT * FROM libra_dept_config WHERE enterprise_id = #{enterpriseId}
        <if test=" deptIdList!=null and deptIdList.size()>0 ">
            AND dept_id IN
            <foreach collection="deptIdList" item="deptIdItem" open="(" separator="," close=")">
                #{deptIdItem}
            </foreach>
        </if>
        <if test=" configName!=null and configName!='' ">
            AND config_name = #{configName}
        </if>
    </select>

</mapper>