<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.DeviceSubtypeEntityMapper">
  <resultMap id="BaseResultMap" type="com.xylink.configserver.data.entity.DeviceSubtypeEntity">
    <!--@mbg.generated-->
    <!--@Table libra_device_subtype-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="sub_type" jdbcType="SMALLINT" property="subType" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="display_model" jdbcType="VARCHAR" property="displayModel" />
    <result column="reference_model" jdbcType="SMALLINT" property="referenceModel" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sub_type, model, package_name, display_model, reference_model
  </sql>
</mapper>