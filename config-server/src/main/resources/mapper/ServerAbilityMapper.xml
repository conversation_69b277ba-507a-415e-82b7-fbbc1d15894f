<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.ServerAbilityMapper">
    <select id="selectCategoryNoDistinct" resultType="java.lang.Integer">
        select distinct category from libra_server_ability
    </select>

    <select id="selectAndSumDecimalValue" resultType="java.lang.Long">
        select sum(decimal_value) from libra_server_ability where category = #{category} and state = 1
    </select>
</mapper>