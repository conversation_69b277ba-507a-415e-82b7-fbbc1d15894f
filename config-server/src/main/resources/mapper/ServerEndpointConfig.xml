<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.LibraServerEndpointConfigMapper">
    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU"/>
        <property name="flushInterval" value="600000"/>
        <property name="size" value="500"/>
        <property name="readOnly" value="false"/>
    </cache>

    <select id="findServerEndpointConfig" resultType="com.xylink.configserver.data.model.LibraServerEndpointConfigEntity">
        SELECT
            *
        FROM
            libra_server_endpoint_config
        WHERE
            config_name = #{configName}
          AND (
                user_profile_id = #{userProfileId}
              OR device_id = #{deviceId}
              )
	</select>

    <!-- <select id="findServerEndpointConfigBatch" resultType="com.xylink.configserver.data.model.LibraServerEndpointConfigEntity"> -->
    <!--     SELECT -->
    <!--         * -->
    <!--     FROM -->
    <!--         libra_server_endpoint_config -->
    <!--     WHERE -->
    <!--         config_name = #{configName} -->
    <!--       AND ( -->
    <!--             user_profile_id in ( ${userids} ) -->
    <!--           OR device_id in ( ${deviceids} ) -->
    <!--           ) -->
    <!-- </select> -->

</mapper>
