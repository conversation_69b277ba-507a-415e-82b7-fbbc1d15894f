<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.NemoDefaultPlatformMapper">

    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU"/>
        <property name="flushInterval" value="600000"/>
        <property name="size" value="500"/>
        <property name="readOnly" value="false"/>
    </cache>

    <select id="getUniqPlatformConfig" resultType="com.xylink.configserver.data.model.NemoDefaultPlatformConfig">
        SELECT
            id,
            config_platform,
            config_type,
            config_name,
            config_value,
            client_config_name
        FROM
            libra_nemo_default_platform_config
        WHERE
             config_platform = #{configPlatform}
             AND config_name = #{configName}
             AND client_config_name = #{clientConfigName}
             AND config_type = #{configType}
   </select>


    <select id="getByPlatformAndConfigType" resultType="com.xylink.configserver.data.model.NemoDefaultPlatformConfig">
        SELECT
            id,
            config_platform,
            config_type,
            config_name,
            config_value,
            client_config_name
        FROM
            libra_nemo_default_platform_config
        WHERE
             config_platform = #{configPlatform}
             AND config_type = #{configType}
   </select>

</mapper>
