<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.BasicCertInfoMapper">
  <resultMap id="BaseResultMap" type="com.xylink.configserver.data.entity.BasicCertInfo">
    <!--@mbg.generated-->
    <!--@Table basic_cert_info-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="cert_name" jdbcType="VARCHAR" property="certName" />
    <result column="sign_author" jdbcType="VARCHAR" property="signAuthor" />
    <result column="cert_sn" jdbcType="VARCHAR" property="certSn" />
    <result column="start_time" jdbcType="LONGVARCHAR" property="startTime" />
    <result column="end_time" jdbcType="LONGVARCHAR" property="endTime" />
    <result column="cert_state" jdbcType="INTEGER" property="certState" />
    <result column="cert_Content" jdbcType="LONGVARCHAR" property="certContent" />
    <result column="cert_sign" jdbcType="VARCHAR" property="certSign" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, cert_name, sign_author, cert_sn, start_time, end_time, cert_state, cert_Content, 
    cert_sign, create_time, update_time
  </sql>
</mapper>