<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xylink.configserver.mapper.deviceseries.DeviceSubtypeSeriesConfigDictionaryMapper">

    <cache type="com.xylink.configserver.mapper.cache.MybatisRedisCache">
        <property name="eviction" value="LRU"/>
        <property name="flushInterval" value="600000"/>
        <property name="size" value="500"/>
        <property name="readOnly" value="false"/>
    </cache>

    <select id="getConfigDictionaryByType"
            resultType="com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigDictionaryEntity">
        SELECT
        *
        FROM
            libra_device_subtype_series_config_dictionary
        WHERE
            dictionary_type = #{dictionaryType}
            AND status=0
        ORDER BY
            config_order
    </select>

</mapper>
