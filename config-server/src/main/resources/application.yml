server:
  port: 18069
  servlet:
    context-path: /api/rest
  undertow:
    accesslog:
      enabled: true
      dir: home/logs/config-server
      pattern: "%h %t \"%r\" %s %b %D %{i,X-Real-IP}"
      prefix: localhost_access_log.
spring:
  application:
    name: "config-server"
  main:
    allow-circular-references: true
  datasource:
    dynamic:
      datasource:
        master:
          username: AINEMO
          password: Da?548!YZ
          driver-class-name: com.kingbase8.Driver
          url: "****************************************************************************************************************************************************************************************************************************************"

        slave_1:
          username: AINEMO
          password: Da?548!YZ
          driver-class-name: com.kingbase8.Driver
          url: "****************************************************************************************************************************************************************************************************************************************"

      hikari:
        max-pool-size: 100
        min-idle: 20

  redis:
    access:
      host: **************
      port: 6379
      username: redisrw
      password: rwQeDQhWBMUU1&W%rL#CUgh8*M#MBG
      timeout: 1000
      sentinel:
        enable: false
        master:
        nodes: **************
        password: S4FYhwPSLJk!7YwdfH2JB#wY25Bjsu
    config:
      host: **************
      port: 6379
      username: redisrw
      password: rwQeDQhWBMUU1&W%rL#CUgh8*M#MBG
      timeout: 1000
      sentinel:
        enable: false
        master:
        nodes: **************
        password: S4FYhwPSLJk!7YwdfH2JB#wY25Bjsu
    craft:
      host: **************
      port: 6379
      username: redisrw
      password: rwQeDQhWBMUU1&W%rL#CUgh8*M#MBG
      timeout: 1000
      sentinel:
        enable: false
        master:
        nodes: **************
        password: S4FYhwPSLJk!7YwdfH2JB#wY25Bjsu

  kafka:
    bootstrap:
      servers: **************:9093
    producer:
      retries: 0
      batch-size: 4096
      linger: 1
      buffer-memory: 40960
      deviceConfig:
        topic: device_config_topic
      messageServer:
        topic: message-server
  messages:
    basename: i18n/messages

common:
  startShowJoinEnterpriseCodeTime: 1576684800000
  restApiInternalPrefix: "http://**************:11111"
  netJudgeStrategy:
  connectionTestTimeout: 3000
  en:
    isDev: false
  message:
    queue: "msgserver/messages"
  id:
    trace:
      workId: 1
    centerId: 1

business:
  service-inspection:
    enable: true

rest:
  ReadTimeout: 3000
  ConnectTimeout: 3000

#mybatis plus 设置
mybatis-plus:
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.xylink.configserver.data.model
  mapper-locations: classpath:mapper/*.xml
  configuration:
    cache-enabled: false
    map-underscore-to-camel-case: true

logging:
  level:
    com.xylink.configserver.mapper: debug

jasypt:
  encryptor:
    password: vw6i3yrvxFmb1OFz
    algorithm: PBEWithHMACSHA512AndAES_256
xylink:
  redis:
    mode: single
    cluster:
      limit: false
      hosts: **************
      password: rwQeDQhWBMUU1&W%rL#CUgh8*M#MBG
      max-redirects: 3
      user: redisrw


sea:
  monitor:
    enabled: false
    endpoint: clientconfig
    uri: http://127.0.0.1:18499/opentsdb/put
