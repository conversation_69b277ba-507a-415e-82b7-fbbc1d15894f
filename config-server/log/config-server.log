2025-06-05 16:23:00,313 [main] WARN  [] o.s.boot.logging.logback.LogbackLoggingSystem - Ignoring 'logback.configurationFile' system property. Please use 'logging.config' instead.
2025-06-05 16:23:00,374 [main] INFO  [] com.xylink.configserver.ConfigServerApplication - Starting ConfigServerApplication using Java 1.8.0_312 on bindeMacBook-Pro.local with PID 60260 (/Users/<USER>/IdeaProjects/client-config-center/config-server/target/classes started by bin in /Users/<USER>/IdeaProjects/client-config-center)
2025-06-05 16:23:00,374 [main] INFO  [] com.xylink.configserver.ConfigServerApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-05 16:23:00,897 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:23:00,898 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 16:23:00,925 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
2025-06-05 16:23:01,104 [main] INFO  [] c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-06-05 16:23:01,105 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-06-05 16:23:01,106 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-06-05 16:23:01,106 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-06-05 16:23:01,106 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-05 16:23:01,107 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-06-05 16:23:01,107 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-06-05 16:23:01,107 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-05 16:23:01,246 [main] INFO  [] c.u.j.filter.DefaultLazyPropertyFilter - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-06-05 16:23:01,252 [main] INFO  [] c.u.j.resolver.DefaultLazyPropertyResolver - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-06-05 16:23:01,253 [main] INFO  [] c.u.j.detector.DefaultLazyPropertyDetector - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-06-05 16:23:01,258 [main] INFO  [] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'encryptorConfig' of type [com.xylink.configserver.configuration.EncryptorConfig$$EnhancerBySpringCGLIB$$a8358123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 16:23:01,421 [main] INFO  [] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jasyptStringEncryptor' of type [org.jasypt.encryption.pbe.PooledPBEStringEncryptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 16:23:01,424 [main] INFO  [] c.u.j.encryptor.DefaultLazyEncryptor - Found Custom Encryptor Bean org.jasypt.encryption.pbe.PooledPBEStringEncryptor@36cc9385 with name: jasyptStringEncryptor
2025-06-05 16:23:01,455 [main] INFO  [] org.jasypt.encryption.pbe.StandardPBEByteEncryptor - decrypt by 196 failed, try 194
2025-06-05 16:23:01,463 [main] INFO  [] org.jasypt.encryption.pbe.StandardPBEByteEncryptor - decrypt by 196 failed, try 194
2025-06-05 16:23:01,468 [main] INFO  [] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 16:23:01,470 [main] INFO  [] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$8b96e560] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 16:23:01,477 [main] INFO  [] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 16:23:01,615 [main] WARN  [] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-05 16:23:01,627 [main] INFO  [] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-05 16:23:01,627 [main] INFO  [] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1192 ms
2025-06-05 16:23:01,722 [main] INFO  [] com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-05 16:23:01,837 [main] INFO  [] com.zaxxer.hikari.pool.PoolBase - master - Driver does not support get/set network timeout for connections. (com.oscar.jdbc.OscarJdbc2Connection.getNetworkTimeout()I)
2025-06-05 16:23:01,855 [main] INFO  [] com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-05 16:23:01,857 [main] INFO  [] com.zaxxer.hikari.HikariDataSource - slave_1 - Starting...
2025-06-05 16:23:01,913 [main] INFO  [] com.zaxxer.hikari.pool.PoolBase - slave_1 - Driver does not support get/set network timeout for connections. (com.oscar.jdbc.OscarJdbc2Connection.getNetworkTimeout()I)
2025-06-05 16:23:01,920 [main] INFO  [] com.zaxxer.hikari.HikariDataSource - slave_1 - Start completed.
2025-06-05 16:23:01,920 [main] INFO  [] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-05 16:23:01,921 [main] INFO  [] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [slave_1] success
2025-06-05 16:23:01,921 [main] INFO  [] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-05 16:23:02,094 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.deviceseries.DeviceConfigDictDataEntity"
2025-06-05 16:23:02,123 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.deviceseries.DeviceSeriesSubtypeEntity"
2025-06-05 16:23:02,134 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.DeviceSoftVersion"
2025-06-05 16:23:02,147 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.xylink.configserver.data.model.DeviceSubtypeModelV2".
2025-06-05 16:23:02,147 [main] WARN  [] c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.xylink.configserver.data.model.DeviceSubtypeModelV2 ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-05 16:23:02,169 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigEntity"
2025-06-05 16:23:02,180 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigDictionaryEntity"
2025-06-05 16:23:02,192 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigDictionaryDataEntity"
2025-06-05 16:23:02,246 [main] WARN  [] c.b.mybatisplus.core.injector.AbstractMethod - [com.xylink.configserver.mapper.EnterpriseServersConfigMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-06-05 16:23:02,248 [main] WARN  [] c.b.mybatisplus.core.injector.AbstractMethod - [com.xylink.configserver.mapper.EnterpriseServersConfigMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-06-05 16:23:02,414 [main] INFO  [] c.g.s.m.boot.autoconfigure.SeaMonitorAutoConfigure - init sea monitor filter bean
2025-06-05 16:23:02,588 [main] INFO  [] c.x.c.service.common.CommonVariableService - ==========>[Running]==========>[Variable]:CommonVariableService(multiEnterprise=false, enableDeptConfig=false)
2025-06-05 16:23:02,898 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.deviceseries.DeviceSeriesEntity"
2025-06-05 16:23:03,118 [main] INFO  [] com.xylink.configserver.util.PrivateCloud - This is private cloud.
2025-06-05 16:23:03,120 [main] INFO  [] c.x.configserver.configuration.CommonConfiguration - SnowflakeIdGenerator [com.xylink.configserver.util.SnowflakeIdGenerator@7d484fcd]
2025-06-05 16:23:03,616 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.deviceseries.DeviceConfigDictTypeEntity"
2025-06-05 16:23:03,653 [main] INFO  [] c.g.seaframework.monitor.filter.SeaMonitorFilter - Sea Monitor Filter init
2025-06-05 16:23:03,654 [main] INFO  [] c.g.seaframework.monitor.util.SeaPropertiesUtil - load properties [sea.monitor.config] not exist
2025-06-05 16:23:03,675 [main] INFO  [] c.g.seaframework.core.loader.EnhancedServiceLoader - load Configuration[typesafe] extension by class[com.github.seaframework.core.config.support.TypeSafeConfiguration]
2025-06-05 16:23:03,677 [main] INFO  [] com.github.seaframework.monitor.SeaMonitor - SeaMonitor is disable due to no app name in resource file sea.monitor.properties
2025-06-05 16:23:03,681 [main] INFO  [] org.xnio - XNIO version 3.8.16.Final
2025-06-05 16:23:03,685 [main] INFO  [] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-05 16:23:03,706 [main] INFO  [] org.jboss.threads - JBoss Threads version 3.1.0.Final
2025-06-05 16:23:03,724 [main] INFO  [] io.undertow - starting server: Undertow - 2.2.35.Final
2025-06-05 16:23:03,750 [main] INFO  [] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port(s) 8080 (http) with context path '/api/rest'
2025-06-05 16:23:03,751 [main] INFO  [] c.u.j.caching.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-06-05 16:23:03,752 [main] INFO  [] c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-06-05 16:23:03,752 [main] INFO  [] c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-06-05 16:23:03,752 [main] INFO  [] c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-06-05 16:23:03,752 [main] INFO  [] c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
2025-06-05 16:23:03,752 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-05 16:23:03,752 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-06-05 16:23:03,752 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-06-05 16:23:03,752 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-06-05 16:23:03,760 [main] INFO  [] com.xylink.configserver.ConfigServerApplication - Started ConfigServerApplication in 3.689 seconds (JVM running for 4.221)
2025-06-05 16:23:03,762 [main] INFO  [] c.g.s.m.b.a.l.SpringApplicationStartListener - init sea-monitor-boot-starter in application event
2025-06-05 16:23:03,762 [main] INFO  [] c.g.s.m.b.a.l.SpringApplicationStartListener - sea monitor is null or enable is null
2025-06-05 16:23:03,786 [pool-9-thread-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==>  Preparing: SELECT id,device_type AS type,user_profile_id,device_display_name AS displayName,device_sn,device_sk AS securityKey,device_expire_time AS expirationTime,in_use,device_presense AS presence,fingerprint,bind_timestamp,avatar,hardware_sn_unique,device_category AS category,sub_type,enterprise_id,update_time,gateway_device_id FROM libra_user_device WHERE id=?
2025-06-05 16:23:03,793 [pool-9-thread-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==> Parameters: 0(Integer)
2025-06-05 16:23:03,806 [pool-9-thread-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - <==      Total: 0
2025-06-05 16:23:03,822 [pool-9-thread-2] INFO  [] org.apache.kafka.clients.admin.AdminClientConfig - AdminClientConfig values: 
	auto.include.jmx.reporter = true
	bootstrap.servers = [**************:9093]
	client.dns.lookup = use_all_dns_ips
	client.id = 
	connections.max.idle.ms = 300000
	default.api.timeout.ms = 2000
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 2000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS

2025-06-05 16:23:03,852 [pool-9-thread-2] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka version: 3.6.1
2025-06-05 16:23:03,852 [pool-9-thread-2] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka commitId: 5e3c2b738d253ff5
2025-06-05 16:23:03,852 [pool-9-thread-2] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka startTimeMs: 1749111783851
2025-06-05 16:23:05,819 [scheduling-1] ERROR [] o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
java.util.concurrent.CompletionException: java.util.concurrent.TimeoutException
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:292)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:308)
	at java.util.concurrent.CompletableFuture.orApply(CompletableFuture.java:1385)
	at java.util.concurrent.CompletableFuture$OrApply.tryFire(CompletableFuture.java:1364)
	at java.util.concurrent.CompletableFuture$CoCompletion.tryFire(CompletableFuture.java:1034)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at com.xylink.configserver.util.CompletableFutureUtil.lambda$timeoutAfter$1(CompletableFutureUtil.java:138)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.TimeoutException: null
	... 8 common frames omitted
2025-06-05 16:23:05,833 [scheduling-1] DEBUG [] c.x.c.m.LibraConfigTypeValueLimitMapper.selectAll - ==>  Preparing: select * from libra_config_type_value_limit
2025-06-05 16:23:05,833 [scheduling-1] DEBUG [] c.x.c.m.LibraConfigTypeValueLimitMapper.selectAll - ==> Parameters: 
2025-06-05 16:23:05,848 [scheduling-1] DEBUG [] c.x.c.m.LibraConfigTypeValueLimitMapper.selectAll - <==      Total: 0
2025-06-05 16:23:05,848 [scheduling-1] INFO  [] c.x.c.service.impl.ConfigValueHandleServiceImpl - 配置项缓存已更新！共更新0项
2025-06-05 16:23:05,849 [scheduling-1] DEBUG [] c.x.c.m.LibraDeviceSubtypeModelMapper.selectAll - ==>  Preparing: select * from libra_device_subtype_model
2025-06-05 16:23:05,849 [scheduling-1] DEBUG [] c.x.c.m.LibraDeviceSubtypeModelMapper.selectAll - ==> Parameters: 
2025-06-05 16:23:05,853 [kafka-admin-client-thread | adminclient-1] INFO  [] org.apache.kafka.clients.admin.KafkaAdminClient - [AdminClient clientId=adminclient-1] Disconnecting from -1 and revoking 1 node assignment(s) because the node is taking too long to become ready.
2025-06-05 16:23:05,854 [kafka-admin-client-thread | adminclient-1] INFO  [] org.apache.kafka.clients.NetworkClient - [AdminClient clientId=adminclient-1] Client requested disconnect from node -1
2025-06-05 16:23:05,858 [kafka-admin-client-thread | adminclient-1] INFO  [] o.a.k.clients.admin.internals.AdminMetadataManager - [AdminClient clientId=adminclient-1] Metadata update failed
org.apache.kafka.common.errors.TimeoutException: Timed out waiting for a node assignment. Call: fetchMetadata
2025-06-05 16:23:05,960 [scheduling-1] DEBUG [] c.x.c.m.LibraDeviceSubtypeModelMapper.selectAll - <==      Total: 97
2025-06-05 16:23:05,960 [kafka-admin-client-thread | adminclient-1] INFO  [] org.apache.kafka.common.utils.AppInfoParser - App info kafka.admin.client for adminclient-1 unregistered
2025-06-05 16:23:05,960 [kafka-admin-client-thread | adminclient-1] INFO  [] o.a.k.clients.admin.internals.AdminMetadataManager - [AdminClient clientId=adminclient-1] Metadata update failed
org.apache.kafka.common.errors.TimeoutException: The AdminClient thread has exited. Call: fetchMetadata
2025-06-05 16:23:05,960 [kafka-admin-client-thread | adminclient-1] INFO  [] org.apache.kafka.clients.admin.KafkaAdminClient - [AdminClient clientId=adminclient-1] Timed out 1 remaining operation(s) during close.
2025-06-05 16:23:05,960 [scheduling-1] INFO  [] c.x.c.s.impl.DeviceSubtypeModelCacheServiceImpl - refresh libra device subtype model from database.
2025-06-05 16:23:05,961 [scheduling-1] INFO  [] c.x.c.service.impl.DefaultConfigServiceImpl - refresh default config from database.
2025-06-05 16:23:05,961 [kafka-admin-client-thread | adminclient-1] INFO  [] org.apache.kafka.common.metrics.Metrics - Metrics scheduler closed
2025-06-05 16:23:05,961 [kafka-admin-client-thread | adminclient-1] INFO  [] org.apache.kafka.common.metrics.Metrics - Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-06-05 16:23:05,962 [kafka-admin-client-thread | adminclient-1] INFO  [] org.apache.kafka.common.metrics.Metrics - Metrics reporters closed
2025-06-05 16:23:05,962 [scheduling-1] DEBUG [] c.x.c.m.DefaultConfigMapper.getAllDefaultConfig - ==>  Preparing: SELECT id, config_name configName, config_value configValue, client_config_name clientConfigName, base_config_type baseConfigType, config_type configType, product_family productFamily FROM libra_default_config
2025-06-05 16:23:05,962 [scheduling-1] DEBUG [] c.x.c.m.DefaultConfigMapper.getAllDefaultConfig - ==> Parameters: 
2025-06-05 16:23:25,044 [scheduling-1] DEBUG [] c.x.c.m.DefaultConfigMapper.getAllDefaultConfig - <==      Total: 2225
2025-06-05 16:23:25,048 [scheduling-1] DEBUG [] c.x.c.m.B.getAllBaseDefaultConfig - ==>  Preparing: SELECT id, config_name configName, config_value configValue, client_config_name clientConfigName, base_config_type baseConfigType, config_type configType, product_family productFamily FROM libra_base_default_config
2025-06-05 16:23:25,050 [scheduling-1] DEBUG [] c.x.c.m.B.getAllBaseDefaultConfig - ==> Parameters: 
2025-06-05 16:23:26,574 [scheduling-1] DEBUG [] c.x.c.m.B.getAllBaseDefaultConfig - <==      Total: 3942
2025-06-05 16:23:26,613 [scheduling-1] DEBUG [] c.x.c.m.E.getEnterpriseNemoConfigByProfileId - ==>  Preparing: SELECT config.id, config.config_name configName, config.config_value configValue, config.client_config_name clientConfigName, config.base_config_type baseConfigType, config.config_type configType, config.enterprise_profile_id enterpriseProfileId FROM libra_enterprise_nemo_config config WHERE enterprise_profile_id = ?
2025-06-05 16:23:26,614 [scheduling-1] DEBUG [] c.x.c.m.E.getEnterpriseNemoConfigByProfileId - ==> Parameters: default(String)
2025-06-05 16:23:27,191 [scheduling-1] DEBUG [] c.x.c.m.E.getEnterpriseNemoConfigByProfileId - <==      Total: 99
2025-06-05 16:23:27,207 [scheduling-1] DEBUG [] c.x.c.mapper.ThirdAppFeatureMapper.getAll - ==>  Preparing: SELECT id, package_name packageName, feature_name featureName FROM libra_thirdapp_feature
2025-06-05 16:23:27,208 [scheduling-1] DEBUG [] c.x.c.mapper.ThirdAppFeatureMapper.getAll - ==> Parameters: 
2025-06-05 16:23:27,213 [scheduling-1] DEBUG [] c.x.c.mapper.ThirdAppFeatureMapper.getAll - <==      Total: 0
2025-06-05 16:24:03,771 [pool-9-thread-3] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==>  Preparing: SELECT id,device_type AS type,user_profile_id,device_display_name AS displayName,device_sn,device_sk AS securityKey,device_expire_time AS expirationTime,in_use,device_presense AS presence,fingerprint,bind_timestamp,avatar,hardware_sn_unique,device_category AS category,sub_type,enterprise_id,update_time,gateway_device_id FROM libra_user_device WHERE id=?
2025-06-05 16:24:03,773 [pool-9-thread-3] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==> Parameters: 0(Integer)
2025-06-05 16:24:03,780 [pool-9-thread-3] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - <==      Total: 0
2025-06-05 16:24:03,787 [pool-9-thread-4] INFO  [] org.apache.kafka.clients.admin.AdminClientConfig - AdminClientConfig values: 
	auto.include.jmx.reporter = true
	bootstrap.servers = [**************:9093]
	client.dns.lookup = use_all_dns_ips
	client.id = 
	connections.max.idle.ms = 300000
	default.api.timeout.ms = 2000
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 2000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS

2025-06-05 16:24:03,794 [pool-9-thread-4] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka version: 3.6.1
2025-06-05 16:24:03,794 [pool-9-thread-4] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka commitId: 5e3c2b738d253ff5
2025-06-05 16:24:03,794 [pool-9-thread-4] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka startTimeMs: 1749111843794
2025-06-05 16:24:05,791 [scheduling-1] ERROR [] o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
java.util.concurrent.CompletionException: java.util.concurrent.TimeoutException
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:292)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:308)
	at java.util.concurrent.CompletableFuture.orApply(CompletableFuture.java:1385)
	at java.util.concurrent.CompletableFuture$OrApply.tryFire(CompletableFuture.java:1364)
	at java.util.concurrent.CompletableFuture$CoCompletion.tryFire(CompletableFuture.java:1034)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at com.xylink.configserver.util.CompletableFutureUtil.lambda$timeoutAfter$1(CompletableFutureUtil.java:138)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.TimeoutException: null
	... 8 common frames omitted
2025-06-05 16:24:05,799 [kafka-admin-client-thread | adminclient-2] INFO  [] o.a.k.clients.admin.internals.AdminMetadataManager - [AdminClient clientId=adminclient-2] Metadata update failed
org.apache.kafka.common.errors.TimeoutException: Timed out waiting to send the call. Call: fetchMetadata
2025-06-05 16:24:06,802 [kafka-admin-client-thread | adminclient-2] INFO  [] org.apache.kafka.common.utils.AppInfoParser - App info kafka.admin.client for adminclient-2 unregistered
2025-06-05 16:24:06,803 [kafka-admin-client-thread | adminclient-2] INFO  [] o.a.k.clients.admin.internals.AdminMetadataManager - [AdminClient clientId=adminclient-2] Metadata update failed
org.apache.kafka.common.errors.TimeoutException: Timed out waiting to send the call. Call: fetchMetadata
2025-06-05 16:24:06,803 [kafka-admin-client-thread | adminclient-2] INFO  [] org.apache.kafka.clients.admin.KafkaAdminClient - [AdminClient clientId=adminclient-2] Timed out 1 remaining operation(s) during close.
2025-06-05 16:24:06,816 [kafka-admin-client-thread | adminclient-2] INFO  [] org.apache.kafka.common.metrics.Metrics - Metrics scheduler closed
2025-06-05 16:24:06,816 [kafka-admin-client-thread | adminclient-2] INFO  [] org.apache.kafka.common.metrics.Metrics - Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-06-05 16:24:06,816 [kafka-admin-client-thread | adminclient-2] INFO  [] org.apache.kafka.common.metrics.Metrics - Metrics reporters closed
2025-06-05 16:25:03,770 [pool-9-thread-5] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==>  Preparing: SELECT id,device_type AS type,user_profile_id,device_display_name AS displayName,device_sn,device_sk AS securityKey,device_expire_time AS expirationTime,in_use,device_presense AS presence,fingerprint,bind_timestamp,avatar,hardware_sn_unique,device_category AS category,sub_type,enterprise_id,update_time,gateway_device_id FROM libra_user_device WHERE id=?
2025-06-05 16:25:03,775 [pool-9-thread-5] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==> Parameters: 0(Integer)
2025-06-05 16:25:03,783 [pool-9-thread-5] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - <==      Total: 0
2025-06-05 16:25:03,789 [pool-9-thread-6] INFO  [] org.apache.kafka.clients.admin.AdminClientConfig - AdminClientConfig values: 
	auto.include.jmx.reporter = true
	bootstrap.servers = [**************:9093]
	client.dns.lookup = use_all_dns_ips
	client.id = 
	connections.max.idle.ms = 300000
	default.api.timeout.ms = 2000
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 2000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS

2025-06-05 16:25:03,800 [pool-9-thread-6] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka version: 3.6.1
2025-06-05 16:25:03,801 [pool-9-thread-6] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka commitId: 5e3c2b738d253ff5
2025-06-05 16:25:03,801 [pool-9-thread-6] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka startTimeMs: 1749111903800
2025-06-05 16:25:05,791 [scheduling-1] ERROR [] o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
java.util.concurrent.CompletionException: java.util.concurrent.TimeoutException
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:292)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:308)
	at java.util.concurrent.CompletableFuture.orApply(CompletableFuture.java:1385)
	at java.util.concurrent.CompletableFuture$OrApply.tryFire(CompletableFuture.java:1364)
	at java.util.concurrent.CompletableFuture$CoCompletion.tryFire(CompletableFuture.java:1034)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at com.xylink.configserver.util.CompletableFutureUtil.lambda$timeoutAfter$1(CompletableFutureUtil.java:138)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.TimeoutException: null
	... 8 common frames omitted
2025-06-05 16:25:05,807 [kafka-admin-client-thread | adminclient-3] INFO  [] o.a.k.clients.admin.internals.AdminMetadataManager - [AdminClient clientId=adminclient-3] Metadata update failed
org.apache.kafka.common.errors.TimeoutException: Timed out waiting to send the call. Call: fetchMetadata
2025-06-05 16:25:06,808 [kafka-admin-client-thread | adminclient-3] INFO  [] org.apache.kafka.common.utils.AppInfoParser - App info kafka.admin.client for adminclient-3 unregistered
2025-06-05 16:25:06,811 [kafka-admin-client-thread | adminclient-3] INFO  [] o.a.k.clients.admin.internals.AdminMetadataManager - [AdminClient clientId=adminclient-3] Metadata update failed
org.apache.kafka.common.errors.TimeoutException: Timed out waiting to send the call. Call: fetchMetadata
2025-06-05 16:25:06,811 [kafka-admin-client-thread | adminclient-3] INFO  [] org.apache.kafka.clients.admin.KafkaAdminClient - [AdminClient clientId=adminclient-3] Timed out 1 remaining operation(s) during close.
2025-06-05 16:25:06,820 [kafka-admin-client-thread | adminclient-3] INFO  [] org.apache.kafka.common.metrics.Metrics - Metrics scheduler closed
2025-06-05 16:25:06,820 [kafka-admin-client-thread | adminclient-3] INFO  [] org.apache.kafka.common.metrics.Metrics - Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-06-05 16:25:06,820 [kafka-admin-client-thread | adminclient-3] INFO  [] org.apache.kafka.common.metrics.Metrics - Metrics reporters closed
2025-06-05 16:25:07,498 [XNIO-2 task-1] INFO  [] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 16:25:07,498 [XNIO-2 task-1] INFO  [] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 16:25:07,500 [XNIO-2 task-1] INFO  [] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-05 16:25:07,503 [XNIO-2 task-1] ERROR [] io.undertow.request - UT005023: Exception handling request to /api/rest/v3/nemo/20732/nemoconfig
java.lang.NumberFormatException: For input string: ""
	at java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	at java.lang.Integer.parseInt(Integer.java:592)
	at java.lang.Integer.parseInt(Integer.java:615)
	at com.xylink.configserver.filter.SecurityFilter.doFilter(SecurityFilter.java:33)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1423)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.lang.Thread.run(Thread.java:748)
2025-06-05 16:25:28,278 [XNIO-2 task-1] ERROR [] io.undertow.request - UT005023: Exception handling request to /api/rest/v3/nemo/20732/nemoconfig
java.lang.NumberFormatException: For input string: ""
	at java.lang.NumberFormatException.forInputString(NumberFormatException.java:65)
	at java.lang.Integer.parseInt(Integer.java:592)
	at java.lang.Integer.parseInt(Integer.java:615)
	at com.xylink.configserver.filter.SecurityFilter.doFilter(SecurityFilter.java:33)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.lang.Thread.run(Thread.java:748)
2025-06-05 16:26:03,780 [pool-9-thread-7] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==>  Preparing: SELECT id,device_type AS type,user_profile_id,device_display_name AS displayName,device_sn,device_sk AS securityKey,device_expire_time AS expirationTime,in_use,device_presense AS presence,fingerprint,bind_timestamp,avatar,hardware_sn_unique,device_category AS category,sub_type,enterprise_id,update_time,gateway_device_id FROM libra_user_device WHERE id=?
2025-06-05 16:26:03,783 [pool-9-thread-7] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==> Parameters: 0(Integer)
2025-06-05 16:26:03,807 [pool-9-thread-7] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - <==      Total: 0
2025-06-05 16:26:03,834 [pool-9-thread-8] INFO  [] org.apache.kafka.clients.admin.AdminClientConfig - AdminClientConfig values: 
	auto.include.jmx.reporter = true
	bootstrap.servers = [**************:9093]
	client.dns.lookup = use_all_dns_ips
	client.id = 
	connections.max.idle.ms = 300000
	default.api.timeout.ms = 2000
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 2000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS

2025-06-05 16:26:03,841 [pool-9-thread-8] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka version: 3.6.1
2025-06-05 16:26:03,842 [pool-9-thread-8] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka commitId: 5e3c2b738d253ff5
2025-06-05 16:26:03,842 [pool-9-thread-8] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka startTimeMs: 1749111963841
2025-06-05 16:26:05,842 [scheduling-1] ERROR [] o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
java.util.concurrent.CompletionException: java.util.concurrent.TimeoutException
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:292)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:308)
	at java.util.concurrent.CompletableFuture.orApply(CompletableFuture.java:1385)
	at java.util.concurrent.CompletableFuture$OrApply.tryFire(CompletableFuture.java:1364)
	at java.util.concurrent.CompletableFuture$CoCompletion.tryFire(CompletableFuture.java:1034)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at com.xylink.configserver.util.CompletableFutureUtil.lambda$timeoutAfter$1(CompletableFutureUtil.java:138)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.TimeoutException: null
	... 8 common frames omitted
2025-06-05 16:26:05,852 [kafka-admin-client-thread | adminclient-4] INFO  [] o.a.k.clients.admin.internals.AdminMetadataManager - [AdminClient clientId=adminclient-4] Metadata update failed
org.apache.kafka.common.errors.TimeoutException: Timed out waiting to send the call. Call: fetchMetadata
2025-06-05 16:26:06,847 [kafka-admin-client-thread | adminclient-4] INFO  [] org.apache.kafka.common.utils.AppInfoParser - App info kafka.admin.client for adminclient-4 unregistered
2025-06-05 16:26:06,848 [kafka-admin-client-thread | adminclient-4] INFO  [] o.a.k.clients.admin.internals.AdminMetadataManager - [AdminClient clientId=adminclient-4] Metadata update failed
org.apache.kafka.common.errors.TimeoutException: Timed out waiting to send the call. Call: fetchMetadata
2025-06-05 16:26:06,848 [kafka-admin-client-thread | adminclient-4] INFO  [] org.apache.kafka.clients.admin.KafkaAdminClient - [AdminClient clientId=adminclient-4] Timed out 1 remaining operation(s) during close.
2025-06-05 16:26:06,859 [kafka-admin-client-thread | adminclient-4] INFO  [] org.apache.kafka.common.metrics.Metrics - Metrics scheduler closed
2025-06-05 16:26:06,859 [kafka-admin-client-thread | adminclient-4] INFO  [] org.apache.kafka.common.metrics.Metrics - Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-06-05 16:26:06,859 [kafka-admin-client-thread | adminclient-4] INFO  [] org.apache.kafka.common.metrics.Metrics - Metrics reporters closed
2025-06-05 16:26:14,083 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - ==>  Preparing: SELECT id, device_type type, user_profile_id userProfileID, sub_type subType, device_sn deviceSN, bind_timestamp bindTimestamp, enterprise_id enterpriseId, device_expire_time expirationTime, device_sk securityKey FROM libra_user_device WHERE device_sk = ?
2025-06-05 16:26:14,083 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - ==> Parameters: f2bd2f12993182d9b81db852e120ff251973eb511da(String)
2025-06-05 16:26:14,087 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - <==      Total: 0
2025-06-05 16:27:03,768 [pool-9-thread-9] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==>  Preparing: SELECT id,device_type AS type,user_profile_id,device_display_name AS displayName,device_sn,device_sk AS securityKey,device_expire_time AS expirationTime,in_use,device_presense AS presence,fingerprint,bind_timestamp,avatar,hardware_sn_unique,device_category AS category,sub_type,enterprise_id,update_time,gateway_device_id FROM libra_user_device WHERE id=?
2025-06-05 16:27:03,770 [pool-9-thread-9] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==> Parameters: 0(Integer)
2025-06-05 16:27:03,775 [pool-9-thread-9] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - <==      Total: 0
2025-06-05 16:27:03,782 [pool-9-thread-10] INFO  [] org.apache.kafka.clients.admin.AdminClientConfig - AdminClientConfig values: 
	auto.include.jmx.reporter = true
	bootstrap.servers = [**************:9093]
	client.dns.lookup = use_all_dns_ips
	client.id = 
	connections.max.idle.ms = 300000
	default.api.timeout.ms = 2000
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 2000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS

2025-06-05 16:27:03,784 [pool-9-thread-10] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka version: 3.6.1
2025-06-05 16:27:03,784 [pool-9-thread-10] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka commitId: 5e3c2b738d253ff5
2025-06-05 16:27:03,784 [pool-9-thread-10] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka startTimeMs: 1749112023784
2025-06-05 16:27:04,825 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - ==>  Preparing: SELECT id, device_type type, user_profile_id userProfileID, sub_type subType, device_sn deviceSN, bind_timestamp bindTimestamp, enterprise_id enterpriseId, device_expire_time expirationTime, device_sk securityKey FROM libra_user_device WHERE device_sk = ?
2025-06-05 16:27:04,825 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - ==> Parameters: 9fd71bae55a8f1bf7329a1903e938aac1973efcc2dd(String)
2025-06-05 16:27:04,829 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - <==      Total: 1
2025-06-05 16:27:07,670 [kafka-admin-client-thread | adminclient-5] INFO  [] org.apache.kafka.clients.admin.KafkaAdminClient - [AdminClient clientId=adminclient-5] Forcing a hard I/O thread shutdown. Requests in progress will be aborted.
2025-06-05 16:27:07,671 [kafka-admin-client-thread | adminclient-5] INFO  [] org.apache.kafka.common.utils.AppInfoParser - App info kafka.admin.client for adminclient-5 unregistered
2025-06-05 16:27:07,671 [kafka-admin-client-thread | adminclient-5] INFO  [] o.a.k.clients.admin.internals.AdminMetadataManager - [AdminClient clientId=adminclient-5] Metadata update failed
org.apache.kafka.common.errors.TimeoutException: Timed out waiting to send the call. Call: fetchMetadata
2025-06-05 16:27:07,671 [kafka-admin-client-thread | adminclient-5] INFO  [] org.apache.kafka.clients.admin.KafkaAdminClient - [AdminClient clientId=adminclient-5] Timed out 2 remaining operation(s) during close.
2025-06-05 16:27:07,676 [kafka-admin-client-thread | adminclient-5] INFO  [] org.apache.kafka.common.metrics.Metrics - Metrics scheduler closed
2025-06-05 16:27:07,677 [kafka-admin-client-thread | adminclient-5] INFO  [] org.apache.kafka.common.metrics.Metrics - Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-06-05 16:27:07,677 [kafka-admin-client-thread | adminclient-5] INFO  [] org.apache.kafka.common.metrics.Metrics - Metrics reporters closed
2025-06-05 16:27:11,808 [scheduling-1] ERROR [] o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
java.util.concurrent.CompletionException: java.util.concurrent.TimeoutException
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:292)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:308)
	at java.util.concurrent.CompletableFuture.orApply(CompletableFuture.java:1385)
	at java.util.concurrent.CompletableFuture$OrApply.tryFire(CompletableFuture.java:1364)
	at java.util.concurrent.CompletableFuture$CoCompletion.tryFire(CompletableFuture.java:1034)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at com.xylink.configserver.util.CompletableFutureUtil.lambda$timeoutAfter$1(CompletableFutureUtil.java:138)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.TimeoutException: null
	... 8 common frames omitted
2025-06-05 16:27:11,827 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getNemoFirstBindTime - ==>  Preparing: select first_bind_timestamp from libra_nemo_first_bindtime where nemo_sn = ?
2025-06-05 16:27:11,827 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getNemoFirstBindTime - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:27:11,830 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getNemoFirstBindTime - <==      Total: 1
2025-06-05 16:27:11,838 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getDeviceVersionBySn - ==>  Preparing: SELECT id, device_sn deviceSn, current_soft_version currentSoftVersion, first_soft_version firstSoftVersion, modify_time modiftTime FROM libra_device_version_record where device_sn=?
2025-06-05 16:27:11,839 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getDeviceVersionBySn - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:27:11,845 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getDeviceVersionBySn - <==      Total: 1
2025-06-05 16:27:11,849 [XNIO-2 task-1] INFO  [] c.x.c.service.impl.DeviceConfigServiceImpl - current nemo soft version :1030804
2025-06-05 16:27:11,849 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==>  Preparing: SELECT id, config_name configName, config_value configValue, config_version_list configVersionList, config_type configType, client_config_name clientConfigName, base_config_type baseConfigType FROM libra_nemo_default_version_config where config_type = ?
2025-06-05 16:27:11,850 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==> Parameters: 8(Integer)
2025-06-05 16:27:11,854 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - <==      Total: 0
2025-06-05 16:27:11,857 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==>  Preparing: SELECT id, config_name configName, config_value configValue, config_version_list configVersionList, config_type configType, client_config_name clientConfigName, base_config_type baseConfigType FROM libra_nemo_default_version_config where config_type = ?
2025-06-05 16:27:11,857 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==> Parameters: 8307(Integer)
2025-06-05 16:27:11,861 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - <==      Total: 0
2025-06-05 16:27:11,865 [XNIO-2 task-1] INFO  [] c.x.c.service.impl.DeviceConfigServiceImpl - get user device current version config succ :VA2503F504404E11
2025-06-05 16:27:11,868 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==>  Preparing: select config.id, config.config_name configName, config.config_value configValue, config.client_config_name clientConfigName, config.base_config_type baseConfigType, config.config_type configType, config.enterprise_profile_id enterpriseProfileId FROM libra_enterprise_nemo_config config join libra_enterprise_nemo_profile pro on config.enterprise_profile_id = pro.id WHERE pro.enterprise_id =? and config.config_type = ?
2025-06-05 16:27:11,869 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==> Parameters: default_enterprise(String), 8(Integer)
2025-06-05 16:27:11,913 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - <==      Total: 8
2025-06-05 16:27:11,919 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==>  Preparing: select config.id, config.config_name configName, config.config_value configValue, config.client_config_name clientConfigName, config.base_config_type baseConfigType, config.config_type configType, config.enterprise_profile_id enterpriseProfileId FROM libra_enterprise_nemo_config config join libra_enterprise_nemo_profile pro on config.enterprise_profile_id = pro.id WHERE pro.enterprise_id =? and config.config_type = ?
2025-06-05 16:27:11,919 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==> Parameters: default_enterprise(String), 8307(Integer)
2025-06-05 16:27:11,927 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - <==      Total: 0
2025-06-05 16:27:11,935 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.SpecialConfigMapper.getSpecialConfig - ==>  Preparing: SELECT config.id id, config.special_feature_id specialFeatureId, config.config_name configName, config.config_value configValue, config.client_config_name clientConfigName FROM libra_special_feature_config config JOIN libra_special_feature_nemo AS nemo ON config.special_feature_id = nemo.feature_id AND nemo.nemo_sn = ?
2025-06-05 16:27:11,935 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.SpecialConfigMapper.getSpecialConfig - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:27:11,941 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.SpecialConfigMapper.getSpecialConfig - <==      Total: 0
2025-06-05 16:27:11,946 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.DeviceConfigMapper.getDeviceConfig - ==>  Preparing: SELECT id, nemo_id nemoId, config_name configName, config_value configValue, client_config_name clientConfigName, config_expire_time configExpireTime FROM libra_nemo_config where nemo_id = ?
2025-06-05 16:27:11,947 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.DeviceConfigMapper.getDeviceConfig - ==> Parameters: 20732(Long)
2025-06-05 16:27:11,979 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.DeviceConfigMapper.getDeviceConfig - <==      Total: 7
2025-06-05 16:27:11,997 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.RechargeConfigMapper.selectByMap - ==>  Preparing: SELECT id,config_type,device_sn,config_expire_time,create_time,update_time,config_name,config_value,client_config_name FROM libra_recharge_config WHERE device_sn = ?
2025-06-05 16:27:11,997 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.RechargeConfigMapper.selectByMap - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:27:12,002 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.RechargeConfigMapper.selectByMap - <==      Total: 0
2025-06-05 16:27:25,753 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:27:25,754 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:27:25,754 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:27:25,754 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:27:25,754 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:27:25,754 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:27:25,756 [pool-9-thread-1] ERROR [] com.xylink.configserver.service.impl.Checker - inspection error: type-REDIS
org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: Read timed out
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:292)
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.getConnection(JedisConnectionFactory.java:515)
	at org.springframework.data.redis.core.RedisConnectionUtils.fetchConnection(RedisConnectionUtils.java:193)
	at org.springframework.data.redis.core.RedisConnectionUtils.doGetConnection(RedisConnectionUtils.java:144)
	at org.springframework.data.redis.core.RedisConnectionUtils.getConnection(RedisConnectionUtils.java:105)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:211)
	at org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:191)
	at org.springframework.data.redis.core.AbstractOperations.execute(AbstractOperations.java:97)
	at org.springframework.data.redis.core.DefaultValueOperations.get(DefaultValueOperations.java:54)
	at com.xylink.configserver.service.impl.RedisChecker.check(ServiceHealthServiceImpl.java:179)
	at com.xylink.configserver.service.impl.Checker.doCheck(ServiceHealthServiceImpl.java:136)
	at com.xylink.configserver.service.impl.Checker.lambda$check$0(ServiceHealthServiceImpl.java:118)
	at java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1640)
	at java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketTimeoutException: Read timed out
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:205)
	at redis.clients.jedis.util.RedisInputStream.readByte(RedisInputStream.java:43)
	at redis.clients.jedis.Protocol.process(Protocol.java:165)
	at redis.clients.jedis.Protocol.read(Protocol.java:230)
	at redis.clients.jedis.Connection.readProtocolWithCheckingBroken(Connection.java:352)
	at redis.clients.jedis.Connection.getStatusCodeReply(Connection.java:270)
	at redis.clients.jedis.BinaryJedis.auth(BinaryJedis.java:2729)
	at redis.clients.jedis.BinaryJedis.initializeFromClientConfig(BinaryJedis.java:97)
	at redis.clients.jedis.BinaryJedis.<init>(BinaryJedis.java:87)
	at redis.clients.jedis.Jedis.<init>(Jedis.java:56)
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.createJedis(JedisConnectionFactory.java:302)
	at org.springframework.data.redis.connection.jedis.JedisConnectionFactory.fetchJedisConnector(JedisConnectionFactory.java:286)
	... 16 common frames omitted
Caused by: java.net.SocketTimeoutException: Read timed out
	at java.net.SocketInputStream.socketRead0(Native Method)
	at java.net.SocketInputStream.socketRead(SocketInputStream.java:116)
	at java.net.SocketInputStream.read(SocketInputStream.java:171)
	at java.net.SocketInputStream.read(SocketInputStream.java:141)
	at java.net.SocketInputStream.read(SocketInputStream.java:127)
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:199)
	... 27 common frames omitted
2025-06-05 16:27:25,757 [XNIO-2 task-1] ERROR [] c.x.c.configuration.GlobalExceptionHandler - Exception
java.lang.NullPointerException: null
	at com.xylink.configserver.util.I18nNemoConfig.getLocalizedValue(I18nNemoConfig.java:39)
	at com.xylink.configserver.process.impl.CarouselImagesProcessor.processNemoConfig(CarouselImagesProcessor.java:23)
	at com.xylink.configserver.service.impl.NemoConfigHelperImpl.process(NemoConfigHelperImpl.java:42)
	at com.xylink.configserver.service.impl.NemoConfigServiceImpl.getNemoConfigs(NemoConfigServiceImpl.java:128)
	at com.xylink.configserver.service.impl.NemoConfigServiceImpl.getExternalNemoConfigs(NemoConfigServiceImpl.java:77)
	at com.xylink.configserver.service.impl.NemoConfigServiceImpl$$FastClassBySpringCGLIB$$cd11c5c8.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.xylink.configserver.service.impl.NemoConfigServiceImpl$$EnhancerBySpringCGLIB$$171f1264.getExternalNemoConfigs(<generated>)
	at com.xylink.configserver.controller.NemoConfigController.getNemoConfigs(NemoConfigController.java:37)
	at com.xylink.configserver.controller.NemoConfigController$$FastClassBySpringCGLIB$$f57b9326.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.xylink.configserver.controller.NemoConfigController$$EnhancerBySpringCGLIB$$238e40b7.getNemoConfigs(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:497)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:584)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.seaframework.monitor.filter.SeaMonitorFilter.doFilter(SeaMonitorFilter.java:76)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.xylink.configserver.filter.SecurityFilter.doFilter(SecurityFilter.java:42)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.lang.Thread.run(Thread.java:748)
2025-06-05 16:28:03,779 [pool-9-thread-2] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==>  Preparing: SELECT id,device_type AS type,user_profile_id,device_display_name AS displayName,device_sn,device_sk AS securityKey,device_expire_time AS expirationTime,in_use,device_presense AS presence,fingerprint,bind_timestamp,avatar,hardware_sn_unique,device_category AS category,sub_type,enterprise_id,update_time,gateway_device_id FROM libra_user_device WHERE id=?
2025-06-05 16:28:03,780 [pool-9-thread-2] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==> Parameters: 0(Integer)
2025-06-05 16:28:03,793 [pool-9-thread-2] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - <==      Total: 0
2025-06-05 16:28:03,811 [pool-9-thread-3] INFO  [] org.apache.kafka.clients.admin.AdminClientConfig - AdminClientConfig values: 
	auto.include.jmx.reporter = true
	bootstrap.servers = [**************:9093]
	client.dns.lookup = use_all_dns_ips
	client.id = 
	connections.max.idle.ms = 300000
	default.api.timeout.ms = 2000
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 2000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS

2025-06-05 16:28:03,813 [pool-9-thread-3] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka version: 3.6.1
2025-06-05 16:28:03,813 [pool-9-thread-3] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka commitId: 5e3c2b738d253ff5
2025-06-05 16:28:03,813 [pool-9-thread-3] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka startTimeMs: 1749112083813
2025-06-05 16:28:05,815 [kafka-admin-client-thread | adminclient-6] INFO  [] o.a.k.clients.admin.internals.AdminMetadataManager - [AdminClient clientId=adminclient-6] Metadata update failed
org.apache.kafka.common.errors.TimeoutException: Timed out waiting to send the call. Call: fetchMetadata
2025-06-05 16:28:05,815 [scheduling-1] ERROR [] o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
java.util.concurrent.CompletionException: java.util.concurrent.TimeoutException
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:292)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:308)
	at java.util.concurrent.CompletableFuture.orApply(CompletableFuture.java:1385)
	at java.util.concurrent.CompletableFuture$OrApply.tryFire(CompletableFuture.java:1364)
	at java.util.concurrent.CompletableFuture$CoCompletion.tryFire(CompletableFuture.java:1034)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at com.xylink.configserver.util.CompletableFutureUtil.lambda$timeoutAfter$1(CompletableFutureUtil.java:138)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.TimeoutException: null
	... 8 common frames omitted
2025-06-05 16:28:05,815 [scheduling-1] INFO  [] c.x.c.service.impl.DefaultConfigServiceImpl - refresh default config from database.
2025-06-05 16:28:05,838 [scheduling-1] DEBUG [] c.x.c.m.DefaultConfigMapper.getAllDefaultConfig - ==>  Preparing: SELECT id, config_name configName, config_value configValue, client_config_name clientConfigName, base_config_type baseConfigType, config_type configType, product_family productFamily FROM libra_default_config
2025-06-05 16:28:05,838 [scheduling-1] DEBUG [] c.x.c.m.DefaultConfigMapper.getAllDefaultConfig - ==> Parameters: 
2025-06-05 16:28:06,814 [kafka-admin-client-thread | adminclient-6] INFO  [] org.apache.kafka.common.utils.AppInfoParser - App info kafka.admin.client for adminclient-6 unregistered
2025-06-05 16:28:06,814 [kafka-admin-client-thread | adminclient-6] INFO  [] o.a.k.clients.admin.internals.AdminMetadataManager - [AdminClient clientId=adminclient-6] Metadata update failed
org.apache.kafka.common.errors.TimeoutException: Timed out waiting to send the call. Call: fetchMetadata
2025-06-05 16:28:06,814 [kafka-admin-client-thread | adminclient-6] INFO  [] org.apache.kafka.clients.admin.KafkaAdminClient - [AdminClient clientId=adminclient-6] Timed out 1 remaining operation(s) during close.
2025-06-05 16:28:06,815 [kafka-admin-client-thread | adminclient-6] INFO  [] org.apache.kafka.common.metrics.Metrics - Metrics scheduler closed
2025-06-05 16:28:06,815 [kafka-admin-client-thread | adminclient-6] INFO  [] org.apache.kafka.common.metrics.Metrics - Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-06-05 16:28:06,815 [kafka-admin-client-thread | adminclient-6] INFO  [] org.apache.kafka.common.metrics.Metrics - Metrics reporters closed
2025-06-05 16:28:22,833 [scheduling-1] DEBUG [] c.x.c.m.DefaultConfigMapper.getAllDefaultConfig - <==      Total: 2225
2025-06-05 16:28:22,834 [scheduling-1] DEBUG [] c.x.c.m.B.getAllBaseDefaultConfig - ==>  Preparing: SELECT id, config_name configName, config_value configValue, client_config_name clientConfigName, base_config_type baseConfigType, config_type configType, product_family productFamily FROM libra_base_default_config
2025-06-05 16:28:22,834 [scheduling-1] DEBUG [] c.x.c.m.B.getAllBaseDefaultConfig - ==> Parameters: 
2025-06-05 16:28:24,297 [scheduling-1] DEBUG [] c.x.c.m.B.getAllBaseDefaultConfig - <==      Total: 3942
2025-06-05 16:28:24,309 [scheduling-1] DEBUG [] c.x.c.m.E.getEnterpriseNemoConfigByProfileId - ==>  Preparing: SELECT config.id, config.config_name configName, config.config_value configValue, config.client_config_name clientConfigName, config.base_config_type baseConfigType, config.config_type configType, config.enterprise_profile_id enterpriseProfileId FROM libra_enterprise_nemo_config config WHERE enterprise_profile_id = ?
2025-06-05 16:28:24,309 [scheduling-1] DEBUG [] c.x.c.m.E.getEnterpriseNemoConfigByProfileId - ==> Parameters: default(String)
2025-06-05 16:28:24,763 [scheduling-1] DEBUG [] c.x.c.m.E.getEnterpriseNemoConfigByProfileId - <==      Total: 99
2025-06-05 16:28:53,221 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - ==>  Preparing: SELECT id, device_type type, user_profile_id userProfileID, sub_type subType, device_sn deviceSN, bind_timestamp bindTimestamp, enterprise_id enterpriseId, device_expire_time expirationTime, device_sk securityKey FROM libra_user_device WHERE device_sk = ?
2025-06-05 16:28:53,226 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - ==> Parameters: 9fd71bae55a8f1bf7329a1903e938aac1973efcc2dd(String)
2025-06-05 16:28:53,238 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - <==      Total: 1
2025-06-05 16:28:58,778 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getNemoFirstBindTime - ==>  Preparing: select first_bind_timestamp from libra_nemo_first_bindtime where nemo_sn = ?
2025-06-05 16:28:58,778 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getNemoFirstBindTime - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:28:58,796 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getNemoFirstBindTime - <==      Total: 1
2025-06-05 16:28:58,805 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getDeviceVersionBySn - ==>  Preparing: SELECT id, device_sn deviceSn, current_soft_version currentSoftVersion, first_soft_version firstSoftVersion, modify_time modiftTime FROM libra_device_version_record where device_sn=?
2025-06-05 16:28:58,805 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getDeviceVersionBySn - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:28:58,813 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getDeviceVersionBySn - <==      Total: 1
2025-06-05 16:28:58,823 [XNIO-2 task-1] INFO  [] c.x.c.service.impl.DeviceConfigServiceImpl - current nemo soft version :1030804
2025-06-05 16:28:58,823 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==>  Preparing: SELECT id, config_name configName, config_value configValue, config_version_list configVersionList, config_type configType, client_config_name clientConfigName, base_config_type baseConfigType FROM libra_nemo_default_version_config where config_type = ?
2025-06-05 16:28:58,823 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==> Parameters: 8(Integer)
2025-06-05 16:28:58,831 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - <==      Total: 0
2025-06-05 16:28:58,854 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==>  Preparing: SELECT id, config_name configName, config_value configValue, config_version_list configVersionList, config_type configType, client_config_name clientConfigName, base_config_type baseConfigType FROM libra_nemo_default_version_config where config_type = ?
2025-06-05 16:28:58,854 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==> Parameters: 8307(Integer)
2025-06-05 16:28:58,862 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - <==      Total: 0
2025-06-05 16:28:58,870 [XNIO-2 task-1] INFO  [] c.x.c.service.impl.DeviceConfigServiceImpl - get user device current version config succ :VA2503F504404E11
2025-06-05 16:28:58,871 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==>  Preparing: select config.id, config.config_name configName, config.config_value configValue, config.client_config_name clientConfigName, config.base_config_type baseConfigType, config.config_type configType, config.enterprise_profile_id enterpriseProfileId FROM libra_enterprise_nemo_config config join libra_enterprise_nemo_profile pro on config.enterprise_profile_id = pro.id WHERE pro.enterprise_id =? and config.config_type = ?
2025-06-05 16:28:58,871 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==> Parameters: default_enterprise(String), 8(Integer)
2025-06-05 16:28:58,962 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - <==      Total: 8
2025-06-05 16:28:58,971 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==>  Preparing: select config.id, config.config_name configName, config.config_value configValue, config.client_config_name clientConfigName, config.base_config_type baseConfigType, config.config_type configType, config.enterprise_profile_id enterpriseProfileId FROM libra_enterprise_nemo_config config join libra_enterprise_nemo_profile pro on config.enterprise_profile_id = pro.id WHERE pro.enterprise_id =? and config.config_type = ?
2025-06-05 16:28:58,972 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==> Parameters: default_enterprise(String), 8307(Integer)
2025-06-05 16:28:58,978 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - <==      Total: 0
2025-06-05 16:28:58,985 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.SpecialConfigMapper.getSpecialConfig - ==>  Preparing: SELECT config.id id, config.special_feature_id specialFeatureId, config.config_name configName, config.config_value configValue, config.client_config_name clientConfigName FROM libra_special_feature_config config JOIN libra_special_feature_nemo AS nemo ON config.special_feature_id = nemo.feature_id AND nemo.nemo_sn = ?
2025-06-05 16:28:58,986 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.SpecialConfigMapper.getSpecialConfig - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:28:58,992 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.SpecialConfigMapper.getSpecialConfig - <==      Total: 0
2025-06-05 16:28:59,001 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.DeviceConfigMapper.getDeviceConfig - ==>  Preparing: SELECT id, nemo_id nemoId, config_name configName, config_value configValue, client_config_name clientConfigName, config_expire_time configExpireTime FROM libra_nemo_config where nemo_id = ?
2025-06-05 16:28:59,001 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.DeviceConfigMapper.getDeviceConfig - ==> Parameters: 20732(Long)
2025-06-05 16:28:59,070 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.DeviceConfigMapper.getDeviceConfig - <==      Total: 7
2025-06-05 16:28:59,078 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.RechargeConfigMapper.selectByMap - ==>  Preparing: SELECT id,config_type,device_sn,config_expire_time,create_time,update_time,config_name,config_value,client_config_name FROM libra_recharge_config WHERE device_sn = ?
2025-06-05 16:28:59,078 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.RechargeConfigMapper.selectByMap - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:28:59,085 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.RechargeConfigMapper.selectByMap - <==      Total: 0
2025-06-05 16:29:01,989 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:29:01,989 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:29:01,989 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:29:01,989 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:29:01,989 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:29:01,989 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:29:14,666 [pool-9-thread-4] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==>  Preparing: SELECT id,device_type AS type,user_profile_id,device_display_name AS displayName,device_sn,device_sk AS securityKey,device_expire_time AS expirationTime,in_use,device_presense AS presence,fingerprint,bind_timestamp,avatar,hardware_sn_unique,device_category AS category,sub_type,enterprise_id,update_time,gateway_device_id FROM libra_user_device WHERE id=?
2025-06-05 16:29:14,666 [scheduling-1] ERROR [] o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
java.util.concurrent.CompletionException: java.util.concurrent.TimeoutException
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:292)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:308)
	at java.util.concurrent.CompletableFuture.orApply(CompletableFuture.java:1385)
	at java.util.concurrent.CompletableFuture$OrApply.tryFire(CompletableFuture.java:1364)
	at java.util.concurrent.CompletableFuture$CoCompletion.tryFire(CompletableFuture.java:1034)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at com.xylink.configserver.util.CompletableFutureUtil.lambda$timeoutAfter$1(CompletableFutureUtil.java:138)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.TimeoutException: null
	... 8 common frames omitted
2025-06-05 16:29:14,667 [pool-9-thread-4] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==> Parameters: 0(Integer)
2025-06-05 16:29:33,003 [pool-9-thread-4] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - <==      Total: 0
2025-06-05 16:30:06,215 [pool-9-thread-5] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==>  Preparing: SELECT id,device_type AS type,user_profile_id,device_display_name AS displayName,device_sn,device_sk AS securityKey,device_expire_time AS expirationTime,in_use,device_presense AS presence,fingerprint,bind_timestamp,avatar,hardware_sn_unique,device_category AS category,sub_type,enterprise_id,update_time,gateway_device_id FROM libra_user_device WHERE id=?
2025-06-05 16:30:06,217 [pool-9-thread-5] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==> Parameters: 0(Integer)
2025-06-05 16:30:08,601 [scheduling-1] ERROR [] o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
java.util.concurrent.CompletionException: java.util.concurrent.TimeoutException
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:292)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:308)
	at java.util.concurrent.CompletableFuture.orApply(CompletableFuture.java:1385)
	at java.util.concurrent.CompletableFuture$OrApply.tryFire(CompletableFuture.java:1364)
	at java.util.concurrent.CompletableFuture$CoCompletion.tryFire(CompletableFuture.java:1034)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at com.xylink.configserver.util.CompletableFutureUtil.lambda$timeoutAfter$1(CompletableFutureUtil.java:138)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.TimeoutException: null
	... 8 common frames omitted
2025-06-05 16:30:12,890 [pool-9-thread-5] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - <==      Total: 0
2025-06-05 16:30:26,936 [XNIO-2 task-1] ERROR [] c.x.c.configuration.GlobalExceptionHandler - Exception
java.lang.NullPointerException: null
	at com.xylink.configserver.util.I18nNemoConfig.getLocalizedValue(I18nNemoConfig.java:39)
	at com.xylink.configserver.process.impl.CarouselImagesProcessor.processNemoConfig(CarouselImagesProcessor.java:23)
	at com.xylink.configserver.service.impl.NemoConfigHelperImpl.process(NemoConfigHelperImpl.java:42)
	at com.xylink.configserver.service.impl.NemoConfigServiceImpl.getNemoConfigs(NemoConfigServiceImpl.java:128)
	at com.xylink.configserver.service.impl.NemoConfigServiceImpl.getExternalNemoConfigs(NemoConfigServiceImpl.java:77)
	at com.xylink.configserver.service.impl.NemoConfigServiceImpl$$FastClassBySpringCGLIB$$cd11c5c8.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.xylink.configserver.service.impl.NemoConfigServiceImpl$$EnhancerBySpringCGLIB$$171f1264.getExternalNemoConfigs(<generated>)
	at com.xylink.configserver.controller.NemoConfigController.getNemoConfigs(NemoConfigController.java:37)
	at com.xylink.configserver.controller.NemoConfigController$$FastClassBySpringCGLIB$$f57b9326.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.xylink.configserver.controller.NemoConfigController$$EnhancerBySpringCGLIB$$238e40b7.getNemoConfigs(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:497)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:584)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.seaframework.monitor.filter.SeaMonitorFilter.doFilter(SeaMonitorFilter.java:76)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.xylink.configserver.filter.SecurityFilter.doFilter(SecurityFilter.java:42)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.lang.Thread.run(Thread.java:748)
2025-06-05 16:30:36,832 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - ==>  Preparing: SELECT id, device_type type, user_profile_id userProfileID, sub_type subType, device_sn deviceSN, bind_timestamp bindTimestamp, enterprise_id enterpriseId, device_expire_time expirationTime, device_sk securityKey FROM libra_user_device WHERE device_sk = ?
2025-06-05 16:30:36,833 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - ==> Parameters: 9fd71bae55a8f1bf7329a1903e938aac1973efcc2dd(String)
2025-06-05 16:30:36,853 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - <==      Total: 1
2025-06-05 16:30:36,870 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getNemoFirstBindTime - ==>  Preparing: select first_bind_timestamp from libra_nemo_first_bindtime where nemo_sn = ?
2025-06-05 16:30:36,870 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getNemoFirstBindTime - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:30:36,878 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getNemoFirstBindTime - <==      Total: 1
2025-06-05 16:30:36,887 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getDeviceVersionBySn - ==>  Preparing: SELECT id, device_sn deviceSn, current_soft_version currentSoftVersion, first_soft_version firstSoftVersion, modify_time modiftTime FROM libra_device_version_record where device_sn=?
2025-06-05 16:30:36,887 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getDeviceVersionBySn - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:30:36,898 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getDeviceVersionBySn - <==      Total: 1
2025-06-05 16:30:36,904 [XNIO-2 task-1] INFO  [] c.x.c.service.impl.DeviceConfigServiceImpl - current nemo soft version :1030804
2025-06-05 16:30:36,904 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==>  Preparing: SELECT id, config_name configName, config_value configValue, config_version_list configVersionList, config_type configType, client_config_name clientConfigName, base_config_type baseConfigType FROM libra_nemo_default_version_config where config_type = ?
2025-06-05 16:30:36,904 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==> Parameters: 8(Integer)
2025-06-05 16:30:36,909 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - <==      Total: 0
2025-06-05 16:30:36,913 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==>  Preparing: SELECT id, config_name configName, config_value configValue, config_version_list configVersionList, config_type configType, client_config_name clientConfigName, base_config_type baseConfigType FROM libra_nemo_default_version_config where config_type = ?
2025-06-05 16:30:36,915 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==> Parameters: 8307(Integer)
2025-06-05 16:30:36,919 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - <==      Total: 0
2025-06-05 16:30:36,926 [XNIO-2 task-1] INFO  [] c.x.c.service.impl.DeviceConfigServiceImpl - get user device current version config succ :VA2503F504404E11
2025-06-05 16:30:36,927 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==>  Preparing: select config.id, config.config_name configName, config.config_value configValue, config.client_config_name clientConfigName, config.base_config_type baseConfigType, config.config_type configType, config.enterprise_profile_id enterpriseProfileId FROM libra_enterprise_nemo_config config join libra_enterprise_nemo_profile pro on config.enterprise_profile_id = pro.id WHERE pro.enterprise_id =? and config.config_type = ?
2025-06-05 16:30:36,927 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==> Parameters: default_enterprise(String), 8(Integer)
2025-06-05 16:30:36,975 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - <==      Total: 8
2025-06-05 16:30:36,980 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==>  Preparing: select config.id, config.config_name configName, config.config_value configValue, config.client_config_name clientConfigName, config.base_config_type baseConfigType, config.config_type configType, config.enterprise_profile_id enterpriseProfileId FROM libra_enterprise_nemo_config config join libra_enterprise_nemo_profile pro on config.enterprise_profile_id = pro.id WHERE pro.enterprise_id =? and config.config_type = ?
2025-06-05 16:30:36,981 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==> Parameters: default_enterprise(String), 8307(Integer)
2025-06-05 16:30:36,985 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - <==      Total: 0
2025-06-05 16:30:36,990 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.SpecialConfigMapper.getSpecialConfig - ==>  Preparing: SELECT config.id id, config.special_feature_id specialFeatureId, config.config_name configName, config.config_value configValue, config.client_config_name clientConfigName FROM libra_special_feature_config config JOIN libra_special_feature_nemo AS nemo ON config.special_feature_id = nemo.feature_id AND nemo.nemo_sn = ?
2025-06-05 16:30:36,990 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.SpecialConfigMapper.getSpecialConfig - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:30:36,999 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.SpecialConfigMapper.getSpecialConfig - <==      Total: 0
2025-06-05 16:30:37,006 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.DeviceConfigMapper.getDeviceConfig - ==>  Preparing: SELECT id, nemo_id nemoId, config_name configName, config_value configValue, client_config_name clientConfigName, config_expire_time configExpireTime FROM libra_nemo_config where nemo_id = ?
2025-06-05 16:30:37,007 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.DeviceConfigMapper.getDeviceConfig - ==> Parameters: 20732(Long)
2025-06-05 16:30:37,068 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.DeviceConfigMapper.getDeviceConfig - <==      Total: 7
2025-06-05 16:30:37,073 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.RechargeConfigMapper.selectByMap - ==>  Preparing: SELECT id,config_type,device_sn,config_expire_time,create_time,update_time,config_name,config_value,client_config_name FROM libra_recharge_config WHERE device_sn = ?
2025-06-05 16:30:37,073 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.RechargeConfigMapper.selectByMap - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:30:37,077 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.RechargeConfigMapper.selectByMap - <==      Total: 0
2025-06-05 16:30:38,762 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:30:38,762 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:30:38,762 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:30:38,762 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:30:38,762 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:30:38,762 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:36:47,965 [master housekeeper] WARN  [] com.zaxxer.hikari.pool.HikariPool - master - Thread starvation or clock leap detected (housekeeper delta=6m4s679ms).
2025-06-05 16:36:47,966 [slave_1 housekeeper] WARN  [] com.zaxxer.hikari.pool.HikariPool - slave_1 - Thread starvation or clock leap detected (housekeeper delta=6m4s679ms).
2025-06-05 16:36:47,967 [XNIO-2 task-1] ERROR [] c.x.c.configuration.GlobalExceptionHandler - Exception
java.lang.NullPointerException: null
	at com.xylink.configserver.util.I18nNemoConfig.getLocalizedValue(I18nNemoConfig.java:39)
	at com.xylink.configserver.process.impl.CarouselImagesProcessor.processNemoConfig(CarouselImagesProcessor.java:23)
	at com.xylink.configserver.service.impl.NemoConfigHelperImpl.process(NemoConfigHelperImpl.java:42)
	at com.xylink.configserver.service.impl.NemoConfigServiceImpl.getNemoConfigs(NemoConfigServiceImpl.java:128)
	at com.xylink.configserver.service.impl.NemoConfigServiceImpl.getExternalNemoConfigs(NemoConfigServiceImpl.java:77)
	at com.xylink.configserver.service.impl.NemoConfigServiceImpl$$FastClassBySpringCGLIB$$cd11c5c8.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386)
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703)
	at com.xylink.configserver.service.impl.NemoConfigServiceImpl$$EnhancerBySpringCGLIB$$171f1264.getExternalNemoConfigs(<generated>)
	at com.xylink.configserver.controller.NemoConfigController.getNemoConfigs(NemoConfigController.java:37)
	at com.xylink.configserver.controller.NemoConfigController$$FastClassBySpringCGLIB$$f57b9326.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.adapter.MethodBeforeAdviceInterceptor.invoke(MethodBeforeAdviceInterceptor.java:58)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.xylink.configserver.controller.NemoConfigController$$EnhancerBySpringCGLIB$$238e40b7.getNemoConfigs(<generated>)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:903)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:809)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:497)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:584)
	at io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)
	at com.github.seaframework.monitor.filter.SeaMonitorFilter.doFilter(SeaMonitorFilter.java:76)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at com.xylink.configserver.filter.SecurityFilter.doFilter(SecurityFilter.java:42)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)
	at io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)
	at io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)
	at io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)
	at io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)
	at io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)
	at io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)
	at io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)
	at io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)
	at io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)
	at io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)
	at io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)
	at io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)
	at io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:275)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$100(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:134)
	at io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:131)
	at io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)
	at io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)
	at io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:255)
	at io.undertow.servlet.handlers.ServletInitialHandler.access$000(ServletInitialHandler.java:79)
	at io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:100)
	at io.undertow.server.Connectors.executeRootHandler(Connectors.java:395)
	at io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:852)
	at org.jboss.threads.ContextClassLoaderSavingRunnable.run(ContextClassLoaderSavingRunnable.java:35)
	at org.jboss.threads.EnhancedQueueExecutor.safeRun(EnhancedQueueExecutor.java:2019)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.doRunTask(EnhancedQueueExecutor.java:1558)
	at org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1449)
	at org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)
	at java.lang.Thread.run(Thread.java:748)
2025-06-05 16:36:47,971 [pool-9-thread-6] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==>  Preparing: SELECT id,device_type AS type,user_profile_id,device_display_name AS displayName,device_sn,device_sk AS securityKey,device_expire_time AS expirationTime,in_use,device_presense AS presence,fingerprint,bind_timestamp,avatar,hardware_sn_unique,device_category AS category,sub_type,enterprise_id,update_time,gateway_device_id FROM libra_user_device WHERE id=?
2025-06-05 16:36:47,971 [pool-9-thread-6] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==> Parameters: 0(Integer)
2025-06-05 16:36:47,976 [pool-9-thread-6] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - <==      Total: 0
2025-06-05 16:36:47,981 [pool-9-thread-7] INFO  [] org.apache.kafka.clients.admin.AdminClientConfig - AdminClientConfig values: 
	auto.include.jmx.reporter = true
	bootstrap.servers = [**************:9093]
	client.dns.lookup = use_all_dns_ips
	client.id = 
	connections.max.idle.ms = 300000
	default.api.timeout.ms = 2000
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 2000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS

2025-06-05 16:36:47,982 [pool-9-thread-7] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka version: 3.6.1
2025-06-05 16:36:47,982 [pool-9-thread-7] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka commitId: 5e3c2b738d253ff5
2025-06-05 16:36:47,982 [pool-9-thread-7] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka startTimeMs: *************
2025-06-05 16:36:48,028 [SpringApplicationShutdownHook] INFO  [] io.undertow - stopping server: Undertow - 2.2.35.Final
2025-06-05 16:36:48,030 [SpringApplicationShutdownHook] INFO  [] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-05 16:36:48,042 [SpringApplicationShutdownHook] INFO  [] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-05 16:36:48,042 [SpringApplicationShutdownHook] INFO  [] com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-05 16:36:51,650 [main] WARN  [] o.s.boot.logging.logback.LogbackLoggingSystem - Ignoring 'logback.configurationFile' system property. Please use 'logging.config' instead.
2025-06-05 16:36:51,720 [main] INFO  [] com.xylink.configserver.ConfigServerApplication - Starting ConfigServerApplication using Java 1.8.0_312 on bindeMacBook-Pro.local with PID 64638 (/Users/<USER>/IdeaProjects/client-config-center/config-server/target/classes started by bin in /Users/<USER>/IdeaProjects/client-config-center)
2025-06-05 16:36:51,721 [main] INFO  [] com.xylink.configserver.ConfigServerApplication - No active profile set, falling back to 1 default profile: "default"
2025-06-05 16:36:52,260 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-05 16:36:52,262 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-05 16:36:52,287 [main] INFO  [] o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-06-05 16:36:52,455 [main] INFO  [] c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor - Post-processing PropertySource instances
2025-06-05 16:36:52,456 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-06-05 16:36:52,456 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-06-05 16:36:52,456 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-06-05 16:36:52,457 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-05 16:36:52,457 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-06-05 16:36:52,458 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-06-05 16:36:52,458 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-05 16:36:52,589 [main] INFO  [] c.u.j.filter.DefaultLazyPropertyFilter - Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-06-05 16:36:52,594 [main] INFO  [] c.u.j.resolver.DefaultLazyPropertyResolver - Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-06-05 16:36:52,595 [main] INFO  [] c.u.j.detector.DefaultLazyPropertyDetector - Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-06-05 16:36:52,599 [main] INFO  [] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'encryptorConfig' of type [com.xylink.configserver.configuration.EncryptorConfig$$EnhancerBySpringCGLIB$$a8358123] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 16:36:52,739 [main] INFO  [] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'jasyptStringEncryptor' of type [org.jasypt.encryption.pbe.PooledPBEStringEncryptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 16:36:52,741 [main] INFO  [] c.u.j.encryptor.DefaultLazyEncryptor - Found Custom Encryptor Bean org.jasypt.encryption.pbe.PooledPBEStringEncryptor@36cc9385 with name: jasyptStringEncryptor
2025-06-05 16:36:52,771 [main] INFO  [] org.jasypt.encryption.pbe.StandardPBEByteEncryptor - decrypt by 196 failed, try 194
2025-06-05 16:36:52,778 [main] INFO  [] org.jasypt.encryption.pbe.StandardPBEByteEncryptor - decrypt by 196 failed, try 194
2025-06-05 16:36:52,782 [main] INFO  [] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 16:36:52,785 [main] INFO  [] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$EnhancerBySpringCGLIB$$8b96e560] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 16:36:52,791 [main] INFO  [] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.DsHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-06-05 16:36:52,939 [main] WARN  [] io.undertow.websockets.jsr - UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-06-05 16:36:52,950 [main] INFO  [] io.undertow.servlet - Initializing Spring embedded WebApplicationContext
2025-06-05 16:36:52,950 [main] INFO  [] o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1159 ms
2025-06-05 16:36:53,033 [main] INFO  [] com.zaxxer.hikari.HikariDataSource - master - Starting...
2025-06-05 16:36:53,114 [main] INFO  [] com.zaxxer.hikari.pool.PoolBase - master - Driver does not support get/set network timeout for connections. (com.oscar.jdbc.OscarJdbc2Connection.getNetworkTimeout()I)
2025-06-05 16:36:53,122 [main] INFO  [] com.zaxxer.hikari.HikariDataSource - master - Start completed.
2025-06-05 16:36:53,124 [main] INFO  [] com.zaxxer.hikari.HikariDataSource - slave_1 - Starting...
2025-06-05 16:36:53,169 [main] INFO  [] com.zaxxer.hikari.pool.PoolBase - slave_1 - Driver does not support get/set network timeout for connections. (com.oscar.jdbc.OscarJdbc2Connection.getNetworkTimeout()I)
2025-06-05 16:36:53,177 [main] INFO  [] com.zaxxer.hikari.HikariDataSource - slave_1 - Start completed.
2025-06-05 16:36:53,177 [main] INFO  [] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2025-06-05 16:36:53,178 [main] INFO  [] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [slave_1] success
2025-06-05 16:36:53,178 [main] INFO  [] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-06-05 16:36:53,347 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.deviceseries.DeviceConfigDictDataEntity"
2025-06-05 16:36:53,372 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.deviceseries.DeviceSeriesSubtypeEntity"
2025-06-05 16:36:53,385 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.DeviceSoftVersion"
2025-06-05 16:36:53,397 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.xylink.configserver.data.model.DeviceSubtypeModelV2".
2025-06-05 16:36:53,397 [main] WARN  [] c.b.mybatisplus.core.injector.DefaultSqlInjector - class com.xylink.configserver.data.model.DeviceSubtypeModelV2 ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-06-05 16:36:53,428 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigEntity"
2025-06-05 16:36:53,439 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigDictionaryEntity"
2025-06-05 16:36:53,449 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.deviceseries.DeviceSubtypeSeriesConfigDictionaryDataEntity"
2025-06-05 16:36:53,486 [main] WARN  [] c.b.mybatisplus.core.injector.AbstractMethod - [com.xylink.configserver.mapper.EnterpriseServersConfigMapper.insert] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Insert]
2025-06-05 16:36:53,487 [main] WARN  [] c.b.mybatisplus.core.injector.AbstractMethod - [com.xylink.configserver.mapper.EnterpriseServersConfigMapper.update] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.Update]
2025-06-05 16:36:53,662 [main] INFO  [] c.g.s.m.boot.autoconfigure.SeaMonitorAutoConfigure - init sea monitor filter bean
2025-06-05 16:36:53,826 [main] INFO  [] c.x.c.service.common.CommonVariableService - ==========>[Running]==========>[Variable]:CommonVariableService(multiEnterprise=false, enableDeptConfig=false)
2025-06-05 16:36:54,132 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.deviceseries.DeviceSeriesEntity"
2025-06-05 16:36:54,353 [main] INFO  [] com.xylink.configserver.util.PrivateCloud - This is private cloud.
2025-06-05 16:36:54,355 [main] INFO  [] c.x.configserver.configuration.CommonConfiguration - SnowflakeIdGenerator [com.xylink.configserver.util.SnowflakeIdGenerator@7d484fcd]
2025-06-05 16:36:54,852 [main] WARN  [] c.b.mybatisplus.core.metadata.TableInfoHelper - This primary key of "id" is primitive !不建议如此请使用包装类 in Class: "com.xylink.configserver.data.model.deviceseries.DeviceConfigDictTypeEntity"
2025-06-05 16:36:54,889 [main] INFO  [] c.g.seaframework.monitor.filter.SeaMonitorFilter - Sea Monitor Filter init
2025-06-05 16:36:54,890 [main] INFO  [] c.g.seaframework.monitor.util.SeaPropertiesUtil - load properties [sea.monitor.config] not exist
2025-06-05 16:36:54,909 [main] INFO  [] c.g.seaframework.core.loader.EnhancedServiceLoader - load Configuration[typesafe] extension by class[com.github.seaframework.core.config.support.TypeSafeConfiguration]
2025-06-05 16:36:54,911 [main] INFO  [] com.github.seaframework.monitor.SeaMonitor - SeaMonitor is disable due to no app name in resource file sea.monitor.properties
2025-06-05 16:36:54,915 [main] INFO  [] org.xnio - XNIO version 3.8.16.Final
2025-06-05 16:36:54,918 [main] INFO  [] org.xnio.nio - XNIO NIO Implementation Version 3.8.16.Final
2025-06-05 16:36:54,939 [main] INFO  [] org.jboss.threads - JBoss Threads version 3.1.0.Final
2025-06-05 16:36:54,958 [main] INFO  [] io.undertow - starting server: Undertow - 2.2.35.Final
2025-06-05 16:36:54,985 [main] INFO  [] o.s.boot.web.embedded.undertow.UndertowWebServer - Undertow started on port(s) 8080 (http) with context path '/api/rest'
2025-06-05 16:36:54,986 [main] INFO  [] c.u.j.caching.RefreshScopeRefreshedEventListener - Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-06-05 16:36:54,986 [main] INFO  [] c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemProperties refreshed
2025-06-05 16:36:54,986 [main] INFO  [] c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source systemEnvironment refreshed
2025-06-05 16:36:54,986 [main] INFO  [] c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source random refreshed
2025-06-05 16:36:54,986 [main] INFO  [] c.u.j.c.CachingDelegateEncryptablePropertySource - Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
2025-06-05 16:36:54,986 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Converting PropertySource server.ports [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-06-05 16:36:54,986 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-06-05 16:36:54,986 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-06-05 16:36:54,986 [main] INFO  [] c.u.j.EncryptablePropertySourceConverter - Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-06-05 16:36:54,995 [main] INFO  [] com.xylink.configserver.ConfigServerApplication - Started ConfigServerApplication in 3.782 seconds (JVM running for 4.505)
2025-06-05 16:36:54,996 [main] INFO  [] c.g.s.m.b.a.l.SpringApplicationStartListener - init sea-monitor-boot-starter in application event
2025-06-05 16:36:54,997 [main] INFO  [] c.g.s.m.b.a.l.SpringApplicationStartListener - sea monitor is null or enable is null
2025-06-05 16:36:55,015 [pool-9-thread-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==>  Preparing: SELECT id,device_type AS type,user_profile_id,device_display_name AS displayName,device_sn,device_sk AS securityKey,device_expire_time AS expirationTime,in_use,device_presense AS presence,fingerprint,bind_timestamp,avatar,hardware_sn_unique,device_category AS category,sub_type,enterprise_id,update_time,gateway_device_id FROM libra_user_device WHERE id=?
2025-06-05 16:36:55,022 [pool-9-thread-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - ==> Parameters: 0(Integer)
2025-06-05 16:36:55,042 [pool-9-thread-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.selectById - <==      Total: 0
2025-06-05 16:36:55,059 [pool-9-thread-2] INFO  [] org.apache.kafka.clients.admin.AdminClientConfig - AdminClientConfig values: 
	auto.include.jmx.reporter = true
	bootstrap.servers = [**************:9093]
	client.dns.lookup = use_all_dns_ips
	client.id = 
	connections.max.idle.ms = 300000
	default.api.timeout.ms = 2000
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 2000
	retries = 2147483647
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.2
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS

2025-06-05 16:36:55,085 [pool-9-thread-2] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka version: 3.6.1
2025-06-05 16:36:55,085 [pool-9-thread-2] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka commitId: 5e3c2b738d253ff5
2025-06-05 16:36:55,086 [pool-9-thread-2] INFO  [] org.apache.kafka.common.utils.AppInfoParser - Kafka startTimeMs: 1749112615084
2025-06-05 16:36:57,055 [scheduling-1] ERROR [] o.s.s.support.TaskUtils$LoggingErrorHandler - Unexpected error occurred in scheduled task
java.util.concurrent.CompletionException: java.util.concurrent.TimeoutException
	at java.util.concurrent.CompletableFuture.encodeThrowable(CompletableFuture.java:292)
	at java.util.concurrent.CompletableFuture.completeThrowable(CompletableFuture.java:308)
	at java.util.concurrent.CompletableFuture.orApply(CompletableFuture.java:1385)
	at java.util.concurrent.CompletableFuture$OrApply.tryFire(CompletableFuture.java:1364)
	at java.util.concurrent.CompletableFuture$CoCompletion.tryFire(CompletableFuture.java:1034)
	at java.util.concurrent.CompletableFuture.postComplete(CompletableFuture.java:488)
	at java.util.concurrent.CompletableFuture.completeExceptionally(CompletableFuture.java:1990)
	at com.xylink.configserver.util.CompletableFutureUtil.lambda$timeoutAfter$1(CompletableFutureUtil.java:138)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.access$201(ScheduledThreadPoolExecutor.java:180)
	at java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:293)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.util.concurrent.TimeoutException: null
	... 8 common frames omitted
2025-06-05 16:36:57,068 [scheduling-1] DEBUG [] c.x.c.m.LibraConfigTypeValueLimitMapper.selectAll - ==>  Preparing: select * from libra_config_type_value_limit
2025-06-05 16:36:57,068 [scheduling-1] DEBUG [] c.x.c.m.LibraConfigTypeValueLimitMapper.selectAll - ==> Parameters: 
2025-06-05 16:36:57,083 [scheduling-1] DEBUG [] c.x.c.m.LibraConfigTypeValueLimitMapper.selectAll - <==      Total: 0
2025-06-05 16:36:57,084 [scheduling-1] INFO  [] c.x.c.service.impl.ConfigValueHandleServiceImpl - 配置项缓存已更新！共更新0项
2025-06-05 16:36:57,084 [scheduling-1] DEBUG [] c.x.c.m.LibraDeviceSubtypeModelMapper.selectAll - ==>  Preparing: select * from libra_device_subtype_model
2025-06-05 16:36:57,084 [scheduling-1] DEBUG [] c.x.c.m.LibraDeviceSubtypeModelMapper.selectAll - ==> Parameters: 
2025-06-05 16:36:57,089 [kafka-admin-client-thread | adminclient-1] INFO  [] o.a.k.clients.admin.internals.AdminMetadataManager - [AdminClient clientId=adminclient-1] Metadata update failed
org.apache.kafka.common.errors.TimeoutException: Timed out waiting to send the call. Call: fetchMetadata
2025-06-05 16:36:57,181 [scheduling-1] DEBUG [] c.x.c.m.LibraDeviceSubtypeModelMapper.selectAll - <==      Total: 97
2025-06-05 16:36:57,181 [scheduling-1] INFO  [] c.x.c.s.impl.DeviceSubtypeModelCacheServiceImpl - refresh libra device subtype model from database.
2025-06-05 16:36:57,182 [scheduling-1] INFO  [] c.x.c.service.impl.DefaultConfigServiceImpl - refresh default config from database.
2025-06-05 16:36:57,182 [scheduling-1] DEBUG [] c.x.c.m.DefaultConfigMapper.getAllDefaultConfig - ==>  Preparing: SELECT id, config_name configName, config_value configValue, client_config_name clientConfigName, base_config_type baseConfigType, config_type configType, product_family productFamily FROM libra_default_config
2025-06-05 16:36:57,182 [scheduling-1] DEBUG [] c.x.c.m.DefaultConfigMapper.getAllDefaultConfig - ==> Parameters: 
2025-06-05 16:36:58,090 [kafka-admin-client-thread | adminclient-1] INFO  [] org.apache.kafka.common.utils.AppInfoParser - App info kafka.admin.client for adminclient-1 unregistered
2025-06-05 16:36:58,090 [kafka-admin-client-thread | adminclient-1] INFO  [] o.a.k.clients.admin.internals.AdminMetadataManager - [AdminClient clientId=adminclient-1] Metadata update failed
org.apache.kafka.common.errors.TimeoutException: Timed out waiting to send the call. Call: fetchMetadata
2025-06-05 16:36:58,090 [kafka-admin-client-thread | adminclient-1] INFO  [] org.apache.kafka.clients.admin.KafkaAdminClient - [AdminClient clientId=adminclient-1] Timed out 1 remaining operation(s) during close.
2025-06-05 16:36:58,092 [kafka-admin-client-thread | adminclient-1] INFO  [] org.apache.kafka.common.metrics.Metrics - Metrics scheduler closed
2025-06-05 16:36:58,093 [kafka-admin-client-thread | adminclient-1] INFO  [] org.apache.kafka.common.metrics.Metrics - Closing reporter org.apache.kafka.common.metrics.JmxReporter
2025-06-05 16:36:58,093 [kafka-admin-client-thread | adminclient-1] INFO  [] org.apache.kafka.common.metrics.Metrics - Metrics reporters closed
2025-06-05 16:37:05,494 [XNIO-2 task-1] INFO  [] io.undertow.servlet - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-05 16:37:05,494 [XNIO-2 task-1] INFO  [] org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-05 16:37:05,496 [XNIO-2 task-1] INFO  [] org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-05 16:37:05,517 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - ==>  Preparing: SELECT id, device_type type, user_profile_id userProfileID, sub_type subType, device_sn deviceSN, bind_timestamp bindTimestamp, enterprise_id enterpriseId, device_expire_time expirationTime, device_sk securityKey FROM libra_user_device WHERE device_sk = ?
2025-06-05 16:37:05,517 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - ==> Parameters: 9fd71bae55a8f1bf7329a1903e938aac1973efcc2dd(String)
2025-06-05 16:37:05,523 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - <==      Total: 0
2025-06-05 16:37:10,143 [scheduling-1] DEBUG [] c.x.c.m.DefaultConfigMapper.getAllDefaultConfig - <==      Total: 2225
2025-06-05 16:37:10,143 [scheduling-1] DEBUG [] c.x.c.m.B.getAllBaseDefaultConfig - ==>  Preparing: SELECT id, config_name configName, config_value configValue, client_config_name clientConfigName, base_config_type baseConfigType, config_type configType, product_family productFamily FROM libra_base_default_config
2025-06-05 16:37:10,144 [scheduling-1] DEBUG [] c.x.c.m.B.getAllBaseDefaultConfig - ==> Parameters: 
2025-06-05 16:37:13,626 [scheduling-1] DEBUG [] c.x.c.m.B.getAllBaseDefaultConfig - <==      Total: 3942
2025-06-05 16:37:13,637 [scheduling-1] DEBUG [] c.x.c.m.E.getEnterpriseNemoConfigByProfileId - ==>  Preparing: SELECT config.id, config.config_name configName, config.config_value configValue, config.client_config_name clientConfigName, config.base_config_type baseConfigType, config.config_type configType, config.enterprise_profile_id enterpriseProfileId FROM libra_enterprise_nemo_config config WHERE enterprise_profile_id = ?
2025-06-05 16:37:13,637 [scheduling-1] DEBUG [] c.x.c.m.E.getEnterpriseNemoConfigByProfileId - ==> Parameters: default(String)
2025-06-05 16:37:14,777 [scheduling-1] DEBUG [] c.x.c.m.E.getEnterpriseNemoConfigByProfileId - <==      Total: 99
2025-06-05 16:37:14,794 [scheduling-1] DEBUG [] c.x.c.mapper.ThirdAppFeatureMapper.getAll - ==>  Preparing: SELECT id, package_name packageName, feature_name featureName FROM libra_thirdapp_feature
2025-06-05 16:37:14,795 [scheduling-1] DEBUG [] c.x.c.mapper.ThirdAppFeatureMapper.getAll - ==> Parameters: 
2025-06-05 16:37:14,806 [scheduling-1] DEBUG [] c.x.c.mapper.ThirdAppFeatureMapper.getAll - <==      Total: 0
2025-06-05 16:37:35,864 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - ==>  Preparing: SELECT id, device_type type, user_profile_id userProfileID, sub_type subType, device_sn deviceSN, bind_timestamp bindTimestamp, enterprise_id enterpriseId, device_expire_time expirationTime, device_sk securityKey FROM libra_user_device WHERE device_sk = ?
2025-06-05 16:37:35,871 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - ==> Parameters: 3977931bd30cae9c28e3bd37ed1e1a701973f391a39(String)
2025-06-05 16:37:35,880 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getUserDeviceBySk - <==      Total: 1
2025-06-05 16:37:35,934 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getNemoFirstBindTime - ==>  Preparing: select first_bind_timestamp from libra_nemo_first_bindtime where nemo_sn = ?
2025-06-05 16:37:35,934 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getNemoFirstBindTime - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:37:35,938 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.UserDeviceMapper.getNemoFirstBindTime - <==      Total: 1
2025-06-05 16:37:35,943 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getDeviceVersionBySn - ==>  Preparing: SELECT id, device_sn deviceSn, current_soft_version currentSoftVersion, first_soft_version firstSoftVersion, modify_time modiftTime FROM libra_device_version_record where device_sn=?
2025-06-05 16:37:35,943 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getDeviceVersionBySn - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:37:35,947 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getDeviceVersionBySn - <==      Total: 1
2025-06-05 16:37:35,950 [XNIO-2 task-1] INFO  [] c.x.c.service.impl.DeviceConfigServiceImpl - current nemo soft version :1030804
2025-06-05 16:37:35,950 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==>  Preparing: SELECT id, config_name configName, config_value configValue, config_version_list configVersionList, config_type configType, client_config_name clientConfigName, base_config_type baseConfigType FROM libra_nemo_default_version_config where config_type = ?
2025-06-05 16:37:35,951 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==> Parameters: 8(Integer)
2025-06-05 16:37:35,966 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - <==      Total: 0
2025-06-05 16:37:35,970 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==>  Preparing: SELECT id, config_name configName, config_value configValue, config_version_list configVersionList, config_type configType, client_config_name clientConfigName, base_config_type baseConfigType FROM libra_nemo_default_version_config where config_type = ?
2025-06-05 16:37:35,970 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - ==> Parameters: 8307(Integer)
2025-06-05 16:37:35,974 [XNIO-2 task-1] DEBUG [] c.x.c.m.D.getNemoDefaultConfigByType - <==      Total: 0
2025-06-05 16:37:35,977 [XNIO-2 task-1] INFO  [] c.x.c.service.impl.DeviceConfigServiceImpl - get user device current version config succ :VA2503F504404E11
2025-06-05 16:37:35,982 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==>  Preparing: select config.id, config.config_name configName, config.config_value configValue, config.client_config_name clientConfigName, config.base_config_type baseConfigType, config.config_type configType, config.enterprise_profile_id enterpriseProfileId FROM libra_enterprise_nemo_config config join libra_enterprise_nemo_profile pro on config.enterprise_profile_id = pro.id WHERE pro.enterprise_id =? and config.config_type = ?
2025-06-05 16:37:35,982 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==> Parameters: default_enterprise(String), 8(Integer)
2025-06-05 16:37:36,026 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - <==      Total: 8
2025-06-05 16:37:36,029 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==>  Preparing: select config.id, config.config_name configName, config.config_value configValue, config.client_config_name clientConfigName, config.base_config_type baseConfigType, config.config_type configType, config.enterprise_profile_id enterpriseProfileId FROM libra_enterprise_nemo_config config join libra_enterprise_nemo_profile pro on config.enterprise_profile_id = pro.id WHERE pro.enterprise_id =? and config.config_type = ?
2025-06-05 16:37:36,030 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - ==> Parameters: default_enterprise(String), 8307(Integer)
2025-06-05 16:37:36,035 [XNIO-2 task-1] DEBUG [] c.x.c.m.E.getEnterpriseDeviceConfigByEnterpriseId - <==      Total: 0
2025-06-05 16:37:36,044 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.SpecialConfigMapper.getSpecialConfig - ==>  Preparing: SELECT config.id id, config.special_feature_id specialFeatureId, config.config_name configName, config.config_value configValue, config.client_config_name clientConfigName FROM libra_special_feature_config config JOIN libra_special_feature_nemo AS nemo ON config.special_feature_id = nemo.feature_id AND nemo.nemo_sn = ?
2025-06-05 16:37:36,044 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.SpecialConfigMapper.getSpecialConfig - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:37:36,053 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.SpecialConfigMapper.getSpecialConfig - <==      Total: 0
2025-06-05 16:37:36,063 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.DeviceConfigMapper.getDeviceConfig - ==>  Preparing: SELECT id, nemo_id nemoId, config_name configName, config_value configValue, client_config_name clientConfigName, config_expire_time configExpireTime FROM libra_nemo_config where nemo_id = ?
2025-06-05 16:37:36,063 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.DeviceConfigMapper.getDeviceConfig - ==> Parameters: 20732(Long)
2025-06-05 16:37:36,110 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.DeviceConfigMapper.getDeviceConfig - <==      Total: 7
2025-06-05 16:37:36,138 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.RechargeConfigMapper.selectByMap - ==>  Preparing: SELECT id,config_type,device_sn,config_expire_time,create_time,update_time,config_name,config_value,client_config_name FROM libra_recharge_config WHERE device_sn = ?
2025-06-05 16:37:36,138 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.RechargeConfigMapper.selectByMap - ==> Parameters: VA2503F504404E11(String)
2025-06-05 16:37:36,141 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.RechargeConfigMapper.selectByMap - <==      Total: 0
2025-06-05 16:37:40,472 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:37:40,472 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:37:40,472 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:37:40,472 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:37:40,472 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:37:40,472 [XNIO-2 task-1] WARN  [] c.x.configserver.service.impl.FeatureListStoreImpl - Version is not in correct format, maybe debug: 12
2025-06-05 16:37:42,089 [XNIO-2 task-1] DEBUG [] c.x.configserver.mapper.OpenNemoMapper.getByNemoId - ==>  Preparing: SELECT id, nemo_id nemoId, config FROM libra_open_nemo WHERE nemo_id = ?
2025-06-05 16:37:42,089 [XNIO-2 task-1] DEBUG [] c.x.configserver.mapper.OpenNemoMapper.getByNemoId - ==> Parameters: 20732(Long)
2025-06-05 16:37:42,092 [XNIO-2 task-1] DEBUG [] c.x.configserver.mapper.OpenNemoMapper.getByNemoId - <==      Total: 0
2025-06-05 16:37:42,097 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.DeviceConfigMapper.getNemoConfig - ==>  Preparing: SELECT id, nemo_id nemoId, config_name configName, config_value configValue, client_config_name clientConfigName, config_expire_time configExpireTime FROM libra_nemo_config where nemo_id= ? and config_name=? and ( client_config_name='common' or ( client_config_name='' or client_config_name is null ) )
2025-06-05 16:37:42,097 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.DeviceConfigMapper.getNemoConfig - ==> Parameters: 20732(Long), nemoType(String)
2025-06-05 16:37:42,104 [XNIO-2 task-1] DEBUG [] c.x.c.mapper.DeviceConfigMapper.getNemoConfig - <==      Total: 0
2025-06-05 16:37:54,058 [SpringApplicationShutdownHook] INFO  [] io.undertow - stopping server: Undertow - 2.2.35.Final
2025-06-05 16:37:54,060 [SpringApplicationShutdownHook] INFO  [] io.undertow.servlet - Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-06-05 16:37:54,072 [SpringApplicationShutdownHook] INFO  [] c.b.dynamic.datasource.DynamicRoutingDataSource - dynamic-datasource start closing ....
2025-06-05 16:37:54,072 [SpringApplicationShutdownHook] INFO  [] com.zaxxer.hikari.HikariDataSource - master - Shutdown initiated...
2025-06-05 16:37:54,164 [SpringApplicationShutdownHook] INFO  [] com.zaxxer.hikari.HikariDataSource - master - Shutdown completed.
2025-06-05 16:37:54,165 [SpringApplicationShutdownHook] INFO  [] com.zaxxer.hikari.HikariDataSource - slave_1 - Shutdown initiated...
