<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.xylink</groupId>
    <artifactId>client-config-center</artifactId>
    <packaging>pom</packaging>
    <version>0.0.1</version>
    <modules>
        <module>config-server</module>
        <module>config-client</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.xylink</groupId>
                <artifactId>dependencies-bom-5.2</artifactId>
                <version>2.7.18-20250926-SNAPSHOT</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <maven-compiler-plugin.version>3.6.2</maven-compiler-plugin.version>
        <maven-resources-plugin.version>3.1.0</maven-resources-plugin.version>
        <maven-source-plugin.version>3.0.1</maven-source-plugin.version>
        <maven-surefire-plugin.version>2.22.0</maven-surefire-plugin.version>
        <maven-assembly-plugin.version>3.1.0</maven-assembly-plugin.version>
        <dockerfile-maven-plugin.version>1.4.10</dockerfile-maven-plugin.version>

        <config.client.version>0.0.1-SNAPSHOT</config.client.version>

        <spring-boot.version>2.7.12</spring-boot.version>
        <apache.commons-lang3.version>3.3.2</apache.commons-lang3.version>
        <mybatis-plus.version>3.5.3.1</mybatis-plus.version>

        <!--私有镜像仓库-->
        <docker.registry>harbor.xylink.com</docker.registry>
        <!--镜像前缀-->
        <docker.image.prefix>xylink/config</docker.image.prefix>
        <sentinel.version>1.7.1-SNAPSHOT</sentinel.version>
        <package.exclude.resource>false</package.exclude.resource>
    </properties>

    <!-- 环境 -->
    <profiles>
        <!-- 开发 -->
        <profile>
            <id>dev</id>
            <activation>
                <!--默认激活配置-->
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <!--当前环境-->
                <profile.name>dev</profile.name>

                <!--NACOS-->
                <nacos.server-addr>127.0.0.1:8848</nacos.server-addr>
                <nacos.namespace/>
                <nacos.isregister>false</nacos.isregister>

                <!--MYSQL-->
                <!--master-->
                <mysql.master.url>*****************************************************************************************************************************************************</mysql.master.url>
                <mysql.master.username>mysqlroot</mysql.master.username>
                <mysql.master.password>devWow1root</mysql.master.password>
                <!--slave1-->
                <mysql.slave1.url>*****************************************************************************************************************************************************</mysql.slave1.url>
                <mysql.slave1.username>mysqlroot</mysql.slave1.username>
                <mysql.slave1.password>devWow1root</mysql.slave1.password>

                <!--REDIS-->
                <redis.access.host>127.0.0.1</redis.access.host>
                <redis.access.port>6379</redis.access.port>
                <redis.access.password>wow!nemo</redis.access.password>
                <redis.access.timeout>10000</redis.access.timeout>
                <redis.access.sentinel.enable>true</redis.access.sentinel.enable>
                <redis.access.sentinel.master>accessmaster</redis.access.sentinel.master>
                <redis.access.sentinel.nodes>*************:26379,*************:26379,*************:26379</redis.access.sentinel.nodes>

                <redis.config.host>127.0.0.1</redis.config.host>
                <redis.config.port>6379</redis.config.port>
                <redis.config.password/>
                <redis.config.timeout>10000</redis.config.timeout>
                <redis.config.sentinel.enable>false</redis.config.sentinel.enable>
                <redis.config.sentinel.master/>
                <redis.config.sentinel.nodes/>

                <redis.craft.host>127.0.0.1</redis.craft.host>
                <redis.craft.port>6379</redis.craft.port>
                <redis.craft.password>wow!nemo</redis.craft.password>
                <redis.craft.timeout>10000</redis.craft.timeout>
                <redis.craft.sentinel.enable>false</redis.craft.sentinel.enable>
                <redis.craft.sentinel.master>craftmaster</redis.craft.sentinel.master>
                <redis.craft.sentinel.nodes>************:26379,*************:26379,***********:26379</redis.craft.sentinel.nodes>

                <!--ACTIVEMQ-->
                <activemq.broker-url>failover:(tcp://**************:61616)?jms.useAsyncSend=true</activemq.broker-url>
                <activemq.user>admin</activemq.user>
                <activemq.password>admin</activemq.password>
                <activemq.send-timeout>1000</activemq.send-timeout>
                <activemq.pool.enabled>true</activemq.pool.enabled>
                <activemq.pool.max-connections>100</activemq.pool.max-connections>
                <activemq.pool.idle-timeout>30000</activemq.pool.idle-timeout>

                <!--common-->
                <common.startShowJoinEnterpriseCodeTime>1576684800000</common.startShowJoinEnterpriseCodeTime>
                <common.restApiInternalPrefix>http://172.18.163.110</common.restApiInternalPrefix>
                <common.netJudgeStrategy/>
                <common.connectionTestTimeout>3000</common.connectionTestTimeout>
                <common.message.queue>msgserver/messages</common.message.queue>
                <common.en.isDev>true</common.en.isDev>

                <!--kafka-->
                <kafka.bootstrap.servers>172.25.48.148:9092,172.25.48.195:9092,172.25.48.105:9092</kafka.bootstrap.servers>
                <kafka.producer.deviceConfig.topic>device_config_topic</kafka.producer.deviceConfig.topic>
                <kafka.producer.retries>0</kafka.producer.retries>
                <kafka.producer.batch.size>4096</kafka.producer.batch.size>
                <kafka.producer.linger>1</kafka.producer.linger>
                <kafka.producer.buffer.memory>40960</kafka.producer.buffer.memory>

            </properties>
        </profile>

        <profile>
            <id>txdev</id>
            <properties>
                <!--当前环境-->
                <profile.name>txdev</profile.name>

                <!--NACOS-->
                <nacos.server-addr>172.25.48.156:8848</nacos.server-addr>
                <nacos.namespace>b1366c8b-bf1e-4ae1-b1d1-364d1f22eebc</nacos.namespace>

                <!--MYSQL-->
                <!--master-->
                <mysql.master.url>*****************************************************************************************************************************************************</mysql.master.url>
                <mysql.master.username>mysqlroot</mysql.master.username>
                <mysql.master.password>devWow1root</mysql.master.password>
                <!--slave1-->
                <mysql.slave1.url>*****************************************************************************************************************************************************</mysql.slave1.url>
                <mysql.slave1.username>mysqlroot</mysql.slave1.username>
                <mysql.slave1.password>devWow1root</mysql.slave1.password>
                <nacos.isregister>true</nacos.isregister>

                <!--REDIS-->
                <redis.access.host>127.0.0.1</redis.access.host>
                <redis.access.port>6379</redis.access.port>
                <redis.access.password>wow!nemo</redis.access.password>
                <redis.access.timeout>10000</redis.access.timeout>
                <redis.access.sentinel.enable>true</redis.access.sentinel.enable>
                <redis.access.sentinel.master>accessmaster</redis.access.sentinel.master>
                <redis.access.sentinel.nodes>*************:26379,************:26379,*************:26379</redis.access.sentinel.nodes>

                <redis.config.host>127.0.0.1</redis.config.host>
                <redis.config.port>6379</redis.config.port>
                <redis.config.password>wow!nemo</redis.config.password>
                <redis.config.timeout>10000</redis.config.timeout>
                <redis.config.sentinel.enable>true</redis.config.sentinel.enable>
                <redis.config.sentinel.master>clientconfig</redis.config.sentinel.master>
                <redis.config.sentinel.nodes>***********4:26379,*************:26379,*************:26380</redis.config.sentinel.nodes>

                <redis.craft.host>127.0.0.1</redis.craft.host>
                <redis.craft.port>6379</redis.craft.port>
                <redis.craft.password>wow!nemo</redis.craft.password>
                <redis.craft.timeout>10000</redis.craft.timeout>
                <redis.craft.sentinel.enable>false</redis.craft.sentinel.enable>
                <redis.craft.sentinel.master>craftmaster</redis.craft.sentinel.master>
                <redis.craft.sentinel.nodes>*************:26379,*************:26379,************:26379</redis.craft.sentinel.nodes>

                <!--ACTIVEMQ-->
                <activemq.broker-url>failover:(tcp://***********:61616)?jms.useAsyncSend=true</activemq.broker-url>
                <activemq.user>admin</activemq.user>
                <activemq.password>admin</activemq.password>
                <activemq.send-timeout>1000</activemq.send-timeout>
                <activemq.pool.enabled>true</activemq.pool.enabled>
                <activemq.pool.max-connections>100</activemq.pool.max-connections>
                <activemq.pool.idle-timeout>30000</activemq.pool.idle-timeout>

                <!--common-->
                <common.startShowJoinEnterpriseCodeTime>1576684800000</common.startShowJoinEnterpriseCodeTime>
                <common.restApiInternalPrefix>http://172.25.48.130:26001</common.restApiInternalPrefix>
                <common.netJudgeStrategy/>
                <common.connectionTestTimeout>3000</common.connectionTestTimeout>
                <common.message.queue>msgserver/messages_distribution</common.message.queue>
                <common.en.isDev>true</common.en.isDev>

                <!--kafka-->
                <kafka.bootstrap.servers>172.25.48.148:9092,172.25.48.195:9092,172.25.48.105:9092</kafka.bootstrap.servers>
                <kafka.producer.deviceConfig.topic>device_config_topic</kafka.producer.deviceConfig.topic>
                <kafka.producer.retries>0</kafka.producer.retries>
                <kafka.producer.batch.size>4096</kafka.producer.batch.size>
                <kafka.producer.linger>1</kafka.producer.linger>
                <kafka.producer.buffer.memory>40960</kafka.producer.buffer.memory>

            </properties>
        </profile>
        <profile>
            <id>private</id>
            <properties>
                <!--当前环境-->
                <profile.name>private</profile.name>

                <!--NACOS-->
                <nacos.server-addr>172.25.48.156:8848</nacos.server-addr>
                <nacos.namespace>b1366c8b-bf1e-4ae1-b1d1-364d1f22eebc</nacos.namespace>

                <!--MYSQL-->
                <!--master-->
                <mysql.master.url>******************************************************************************************************************************************************</mysql.master.url>
                <mysql.master.username>root</mysql.master.username>
                <mysql.master.password>ak87?x</mysql.master.password>
                <!--slave1-->
                <mysql.slave1.url>******************************************************************************************************************************************************</mysql.slave1.url>
                <mysql.slave1.username>root</mysql.slave1.username>
                <mysql.slave1.password>ak87?x</mysql.slave1.password>

                <!--REDIS-->
                <redis.access.host>127.0.0.1</redis.access.host>
                <redis.access.port>6379</redis.access.port>
                <redis.access.password>wow!nemo</redis.access.password>
                <redis.access.timeout>10000</redis.access.timeout>
                <redis.access.sentinel.enable>true</redis.access.sentinel.enable>
                <redis.access.sentinel.master>accessmaster</redis.access.sentinel.master>
                <redis.access.sentinel.nodes>*************:26379,************:26379,*************:26379</redis.access.sentinel.nodes>

                <redis.config.host>127.0.0.1</redis.config.host>
                <redis.config.port>6379</redis.config.port>
                <redis.config.password>wow!nemo</redis.config.password>
                <redis.config.timeout>10000</redis.config.timeout>
                <redis.config.sentinel.enable>true</redis.config.sentinel.enable>
                <redis.config.sentinel.master>clientconfig</redis.config.sentinel.master>
                <redis.config.sentinel.nodes>***********4:26379,*************:26379,*************:26380</redis.config.sentinel.nodes>

                <redis.craft.host>127.0.0.1</redis.craft.host>
                <redis.craft.port>6379</redis.craft.port>
                <redis.craft.password>wow!nemo</redis.craft.password>
                <redis.craft.timeout>10000</redis.craft.timeout>
                <redis.craft.sentinel.enable>false</redis.craft.sentinel.enable>
                <redis.craft.sentinel.master>craftmaster</redis.craft.sentinel.master>
                <redis.craft.sentinel.nodes>*************:26379,*************:26379,************:26379</redis.craft.sentinel.nodes>

                <!--ACTIVEMQ-->
                <activemq.broker-url>failover:(tcp://***********:61616)?jms.useAsyncSend=true</activemq.broker-url>
                <activemq.user>admin</activemq.user>
                <activemq.password>admin</activemq.password>
                <activemq.send-timeout>1000</activemq.send-timeout>
                <activemq.pool.enabled>true</activemq.pool.enabled>
                <activemq.pool.max-connections>100</activemq.pool.max-connections>
                <activemq.pool.idle-timeout>30000</activemq.pool.idle-timeout>

                <!--common-->
                <common.startShowJoinEnterpriseCodeTime>1576684800000</common.startShowJoinEnterpriseCodeTime>
                <common.restApiInternalPrefix>http://***********1</common.restApiInternalPrefix>
                <common.netJudgeStrategy/>
                <common.connectionTestTimeout>3000</common.connectionTestTimeout>
                <common.message.queue>msgserver/messages_distribution</common.message.queue>
                <common.en.isDev>true</common.en.isDev>

                <!--kafka-->
                <kafka.bootstrap.servers>172.25.48.148:9092,172.25.48.195:9092,172.25.48.105:9092</kafka.bootstrap.servers>
                <kafka.producer.deviceConfig.topic>device_config_topic</kafka.producer.deviceConfig.topic>
                <kafka.producer.retries>0</kafka.producer.retries>
                <kafka.producer.batch.size>4096</kafka.producer.batch.size>
                <kafka.producer.linger>1</kafka.producer.linger>
                <kafka.producer.buffer.memory>40960</kafka.producer.buffer.memory>

            </properties>
        </profile>
        <profile>
            <id>testqa</id>
            <properties>
                <!--当前环境-->
                <profile.name>testqa</profile.name>

                <!--NACOS-->
                <nacos.server-addr>172.20.11.180:8848</nacos.server-addr>
                <nacos.namespace>c963be3b-1d86-45aa-b5a0-1b609a7da7fd</nacos.namespace>
                <nacos.isregister>false</nacos.isregister>

                <!--MYSQL-->
                <!--master-->
                <mysql.master.url>jdbc:mysql://*************:3306/ainemo?failOverReadOnly=true&amp;secondsBeforeRetryMaster=5&amp;autoReconnect=true</mysql.master.url>
                <mysql.master.username>z3ainemoroot</mysql.master.username>
                <mysql.master.password>j0Y!T77lb39CP#YE0Ioz</mysql.master.password>
                <!--slave1-->
                <mysql.slave1.url>jdbc:mysql://*************:3306/ainemo?failOverReadOnly=true&amp;secondsBeforeRetryMaster=5&amp;autoReconnect=true</mysql.slave1.url>
                <mysql.slave1.username>z3ainemoroot</mysql.slave1.username>
                <mysql.slave1.password>j0Y!T77lb39CP#YE0Ioz</mysql.slave1.password>

                <!--REDIS-->
                <redis.access.host>127.0.0.1</redis.access.host>
                <redis.access.port>6379</redis.access.port>
                <redis.access.password>wow!nemo</redis.access.password>
                <redis.access.timeout>10000</redis.access.timeout>
                <redis.access.sentinel.enable>true</redis.access.sentinel.enable>
                <redis.access.sentinel.master>accessmaster</redis.access.sentinel.master>
                <redis.access.sentinel.nodes>*************:26379,*************:26379,*************:26379</redis.access.sentinel.nodes>

                <redis.config.host>127.0.0.1</redis.config.host>
                <redis.config.port>6379</redis.config.port>
                <redis.config.password>wow!nemo</redis.config.password>
                <redis.config.timeout>10000</redis.config.timeout>
                <redis.config.sentinel.enable>true</redis.config.sentinel.enable>
                <redis.config.sentinel.master>clientconfig</redis.config.sentinel.master>
                <redis.config.sentinel.nodes>*************:26379,*************:26379,*************:26379</redis.config.sentinel.nodes>

                <redis.craft.host>127.0.0.1</redis.craft.host>
                <redis.craft.port>6379</redis.craft.port>
                <redis.craft.password>wow!nemo</redis.craft.password>
                <redis.craft.timeout>10000</redis.craft.timeout>
                <redis.craft.sentinel.enable>false</redis.craft.sentinel.enable>
                <redis.craft.sentinel.master>craftmaster</redis.craft.sentinel.master>
                <redis.craft.sentinel.nodes>************:26379,*************:26379,*************:26379</redis.craft.sentinel.nodes>

                <!--ACTIVEMQ-->
                <activemq.broker-url>failover:(tcp://************:61616,tcp://*************:61616)?jms.useAsyncSend=true</activemq.broker-url>
                <activemq.user>admin</activemq.user>
                <activemq.password>admin</activemq.password>
                <activemq.send-timeout>1000</activemq.send-timeout>
                <activemq.pool.enabled>true</activemq.pool.enabled>
                <activemq.pool.max-connections>100</activemq.pool.max-connections>
                <activemq.pool.idle-timeout>30000</activemq.pool.idle-timeout>

                <!--common-->
                <common.startShowJoinEnterpriseCodeTime>1576684800000</common.startShowJoinEnterpriseCodeTime>
                <common.restApiInternalPrefix>http://172.20.26.160</common.restApiInternalPrefix>
                <common.netJudgeStrategy/>
                <common.connectionTestTimeout>3000</common.connectionTestTimeout>
                <common.message.queue>msgserver/messages</common.message.queue>
                <common.en.isDev>true</common.en.isDev>

                <!--kafka-->
                <kafka.bootstrap.servers>*************9:9092,172.25.129.89:9092,172.25.129.117:9092</kafka.bootstrap.servers>
                <kafka.producer.deviceConfig.topic>device_config_topic</kafka.producer.deviceConfig.topic>
                <kafka.producer.retries>0</kafka.producer.retries>
                <kafka.producer.batch.size>4096</kafka.producer.batch.size>
                <kafka.producer.linger>1</kafka.producer.linger>
                <kafka.producer.buffer.memory>40960</kafka.producer.buffer.memory>

            </properties>
        </profile>

        <profile>
            <id>pre</id>
            <properties>
                <!--当前环境-->
                <profile.name>pre</profile.name>

                <!--NACOS-->
                <nacos.server-addr>172.20.11.180:8848</nacos.server-addr>
                <nacos.namespace>449c4c99-9e59-4f8f-9e68-43738badaa5e</nacos.namespace>
                <nacos.isregister>true</nacos.isregister>

                <!--MYSQL-->
                <!--master-->
                <mysql.master.url>*********************************************************************************************************************************************************************************************</mysql.master.url>
                <mysql.master.username>preainemoroot</mysql.master.username>
                <mysql.master.password>DkSn4d6QbYNDuY!$8Msa</mysql.master.password>
                <!--slave1-->
                <mysql.slave1.url>*********************************************************************************************************************************************************************************************</mysql.slave1.url>
                <mysql.slave1.username>preainemoroot</mysql.slave1.username>
                <mysql.slave1.password>DkSn4d6QbYNDuY!$8Msa</mysql.slave1.password>

                <!--REDIS-->
                <redis.access.host>127.0.0.1</redis.access.host>
                <redis.access.port>6379</redis.access.port>
                <redis.access.password>wow!nemo</redis.access.password>
                <redis.access.timeout>10000</redis.access.timeout>
                <redis.access.sentinel.enable>true</redis.access.sentinel.enable>
                <redis.access.sentinel.master>accessmaster</redis.access.sentinel.master>
                <redis.access.sentinel.nodes>*************:26379,*************:26379,*************:26379</redis.access.sentinel.nodes>

                <redis.config.host>127.0.0.1</redis.config.host>
                <redis.config.port>6379</redis.config.port>
                <redis.config.password>wow!nemo</redis.config.password>
                <redis.config.timeout>10000</redis.config.timeout>
                <redis.config.sentinel.enable>true</redis.config.sentinel.enable>
                <redis.config.sentinel.master>clientconfig</redis.config.sentinel.master>
                <redis.config.sentinel.nodes>**************:26379,**************:26379,*************:26379</redis.config.sentinel.nodes>

                <redis.craft.host>127.0.0.1</redis.craft.host>
                <redis.craft.port>6379</redis.craft.port>
                <redis.craft.password>wow!nemo</redis.craft.password>
                <redis.craft.timeout>10000</redis.craft.timeout>
                <redis.craft.sentinel.enable>true</redis.craft.sentinel.enable>
                <redis.craft.sentinel.master>craftmaster</redis.craft.sentinel.master>
                <redis.craft.sentinel.nodes>*************:26379,*************:26379,*************:26379</redis.craft.sentinel.nodes>

                <!--ACTIVEMQ-->
                <activemq.broker-url>failover:(tcp://**************:61616)?jms.useAsyncSend=true</activemq.broker-url>
                <activemq.user>admin</activemq.user>
                <activemq.password>admin</activemq.password>
                <activemq.send-timeout>1000</activemq.send-timeout>
                <activemq.pool.enabled>true</activemq.pool.enabled>
                <activemq.pool.max-connections>100</activemq.pool.max-connections>
                <activemq.pool.idle-timeout>30000</activemq.pool.idle-timeout>

                <!--common-->
                <common.startShowJoinEnterpriseCodeTime>1576684800000</common.startShowJoinEnterpriseCodeTime>
                <common.restApiInternalPrefix>http://172.18.163.110</common.restApiInternalPrefix>
                <common.netJudgeStrategy/>
                <common.connectionTestTimeout>3000</common.connectionTestTimeout>
                <common.message.queue>msgserver/messages</common.message.queue>
                <common.en.isDev>true</common.en.isDev>
                <!--kafka-->
                <kafka.bootstrap.servers>172.18.160.206:9092,172.18.160.207:9092,172.18.160.208:9092</kafka.bootstrap.servers>
                <kafka.producer.deviceConfig.topic>device_config_topic</kafka.producer.deviceConfig.topic>
                <kafka.producer.retries>0</kafka.producer.retries>
                <kafka.producer.batch.size>4096</kafka.producer.batch.size>
                <kafka.producer.linger>1</kafka.producer.linger>
                <kafka.producer.buffer.memory>40960</kafka.producer.buffer.memory>

            </properties>
            <build>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <includes>
                            <include>**/*</include>
                        </includes>
                        <excludes>
                            <exclude>**/application.yml</exclude>
                        </excludes>
                        <filtering>true</filtering>
                    </resource>
                    <resource>
                        <directory>src/main/resources-env/${profile.name}</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>

        <!-- 生产 -->
        <profile>
            <id>prd</id>
            <properties>
                <!--当前环境-->
                <profile.name>prd</profile.name>

                <!--NACOS-->
                <nacos.server-addr>172.20.11.180:8848</nacos.server-addr>
                <nacos.namespace>449c4c99-9e59-4f8f-9e68-43738badaa5e</nacos.namespace>
                <nacos.isregister>true</nacos.isregister>

                <!--MYSQL-->
                <!--master-->
                <mysql.master.url>***************************************************************************************************************************************************************************************</mysql.master.url>
                <mysql.master.username>mysqlroot</mysql.master.username>
                <mysql.master.password>passw0rd</mysql.master.password>
                <!--slave1-->
                <mysql.slave1.url>*************************************************************************************************************************************************************************************</mysql.slave1.url>
                <mysql.slave1.username>mysqlroot</mysql.slave1.username>
                <mysql.slave1.password>passw0rd</mysql.slave1.password>

                <!--REDIS-->
                <redis.access.host>127.0.0.1</redis.access.host>
                <redis.access.port>6379</redis.access.port>
                <redis.access.password>wow!nemo</redis.access.password>
                <redis.access.timeout>10000</redis.access.timeout>
                <redis.access.sentinel.enable>true</redis.access.sentinel.enable>
                <redis.access.sentinel.master>accessmaster</redis.access.sentinel.master>
                <redis.access.sentinel.nodes>*************:26379,*************:26379,*************:26379</redis.access.sentinel.nodes>

                <redis.config.host>127.0.0.1</redis.config.host>
                <redis.config.port>6379</redis.config.port>
                <redis.config.password>wow!nemo</redis.config.password>
                <redis.config.timeout>10000</redis.config.timeout>
                <redis.config.sentinel.enable>true</redis.config.sentinel.enable>
                <redis.config.sentinel.master>craftmaster</redis.config.sentinel.master>
                <redis.config.sentinel.nodes>************:26379,*************:26379,***********:26379</redis.config.sentinel.nodes>

                <!--ACTIVEMQ-->
                <activemq.broker-url>failover:(tcp://**************:61616)?jms.useAsyncSend=true</activemq.broker-url>
                <activemq.user>admin</activemq.user>
                <activemq.password>admin</activemq.password>
                <activemq.send-timeout>1000</activemq.send-timeout>
                <activemq.pool.enabled>true</activemq.pool.enabled>
                <activemq.pool.max-connections>100</activemq.pool.max-connections>
                <activemq.pool.idle-timeout>30000</activemq.pool.idle-timeout>

                <!--common-->
                <common.startShowJoinEnterpriseCodeTime>1576684800000</common.startShowJoinEnterpriseCodeTime>
                <common.restApiInternalPrefix>http://172.18.163.110</common.restApiInternalPrefix>
                <common.netJudgeStrategy/>
                <common.connectionTestTimeout>3000</common.connectionTestTimeout>
                <common.message.queue>msgserver/messages</common.message.queue>
                <common.en.isDev>true</common.en.isDev>

                <!--kafka-->
                <kafka.bootstrap.servers>172.25.48.148:9092,172.25.48.195:9092,172.25.48.105:9092</kafka.bootstrap.servers>
                <kafka.producer.deviceConfig.topic>device_config_topic</kafka.producer.deviceConfig.topic>
                <kafka.producer.retries>0</kafka.producer.retries>
                <kafka.producer.batch.size>4096</kafka.producer.batch.size>
                <kafka.producer.linger>1</kafka.producer.linger>
                <kafka.producer.buffer.memory>40960</kafka.producer.buffer.memory>


            </properties>
        </profile>
    </profiles>
    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <excludes>
                    <exclude>**/application.yml</exclude>
                </excludes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources-env/${profile.name}</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${maven.compiler.encoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven-resources-plugin.version}</version>
            </plugin>
            <plugin>
                <!--打包跳过测试-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <repositories>
        <!-- 配置nexus远程仓库 -->
        <repository>
            <id>nexus</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://*********:8081/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>
    <!-- 配置从哪个仓库中下载构件，即jar包 -->
    <pluginRepositories>
        <pluginRepository>
            <id>nexus</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://*********:8081/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://*********:8081/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://*********:8081/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>