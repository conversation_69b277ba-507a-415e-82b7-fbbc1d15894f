apiVersion: v1
kind: Pod
metadata:
  labels:
    app: jenkins_agent
spec:
  imagePullSecrets:
  - name: harbor.xylink.com
  containers:
  - name: gradle-jdk
    image: harbor.xylink.com/xylink/maven-jdk8:3.2.3
    tty: true
    resources:
      requests:
        memory: "1536Mi"
        cpu: "1"
      limits:
        memory: "1536Mi"
        cpu: "2"
  - name: kube-tools
    image: harbor.xylink.com/xylink/kubectl-kubedog-kustomize:1.15.0-0.3.4-3.2.0-gitlab
    tty: true
    resources:
      requests:
        memory: "200Mi"
        cpu: "100m"
      limits:
        memory: "200Mi"
        cpu: "200m"
  - name: docker
    image: docker
    tty: true
    resources:
      requests:
        memory: "200Mi"
        cpu: "200m"
      limits:
        memory: "200Mi"
        cpu: "200m"
    volumeMounts:
    - mountPath: /var/run/docker.sock
      name: dockersock
  volumes:
  - name: dockersock
    hostPath:
      path: /var/run/docker.sock

